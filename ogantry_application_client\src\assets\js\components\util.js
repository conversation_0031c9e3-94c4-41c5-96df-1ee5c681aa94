"use strict";Element.prototype.matches||(Element.prototype.matches=Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector),Element.prototype.closest||(Element.prototype.matches||(Element.prototype.matches=Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector),Element.prototype.closest=function(t){var e=this;if(!document.documentElement.contains(this))return null;do{if(e.matches(t))return e;e=e.parentElement}while(null!==e);return null}),function(t){for(var e=0;e<t.length;e++)!window[t[e]]||"remove"in window[t[e]].prototype||(window[t[e]].prototype.remove=function(){this.parentNode.removeChild(this)})}(["Element","CharacterData","DocumentType"]),function(){for(var t=0,e=["webkit","moz"],n=0;n<e.length&&!window.requestAnimationFrame;++n)window.requestAnimationFrame=window[e[n]+"RequestAnimationFrame"],window.cancelAnimationFrame=window[e[n]+"CancelAnimationFrame"]||window[e[n]+"CancelRequestAnimationFrame"];window.requestAnimationFrame||(window.requestAnimationFrame=function(e){var n=(new Date).getTime(),i=Math.max(0,16-(n-t)),o=window.setTimeout(function(){e(n+i)},i);return t=n+i,o}),window.cancelAnimationFrame||(window.cancelAnimationFrame=function(t){clearTimeout(t)})}(),[Element.prototype,Document.prototype,DocumentFragment.prototype].forEach(function(t){t.hasOwnProperty("prepend")||Object.defineProperty(t,"prepend",{configurable:!0,enumerable:!0,writable:!0,value:function(){var t=Array.prototype.slice.call(arguments),e=document.createDocumentFragment();t.forEach(function(t){var n=t instanceof Node;e.appendChild(n?t:document.createTextNode(String(t)))}),this.insertBefore(e,this.firstChild)}})}),null==Element.prototype.getAttributeNames&&(Element.prototype.getAttributeNames=function(){for(var t=this.attributes,e=t.length,n=new Array(e),i=0;i<e;i++)n[i]=t[i].name;return n}),window.KTUtilElementDataStore={},window.KTUtilElementDataStoreID=0,window.KTUtilDelegatedEventHandlers={};var KTUtil=function(){var t=[],e={sm:544,md:768,lg:992,xl:1200},n=function(){window.addEventListener("resize",function(){KTUtil.throttle(void 0,function(){!function(){for(var e=0;e<t.length;e++)t[e].call()}()},200)})};return{init:function(t){t&&t.breakpoints&&(e=t.breakpoints),n()},addResizeHandler:function(e){t.push(e)},removeResizeHandler:function(e){for(var n=0;n<t.length;n++)e===t[n]&&delete t[n]},runResizeHandlers:function(){_runResizeHandlers()},resize:function(){if("function"==typeof Event)window.dispatchEvent(new Event("resize"));else{var t=window.document.createEvent("UIEvents");t.initUIEvent("resize",!0,!1,window,0),window.dispatchEvent(t)}},getURLParam:function(t){var e,n,i=window.location.search.substring(1).split("&");for(e=0;e<i.length;e++)if((n=i[e].split("="))[0]==t)return unescape(n[1]);return null},isMobileDevice:function(){var t=this.getViewPort().width<this.getBreakpoint("lg");return!1===t&&(t=null!=navigator.userAgent.match(/iPad/i)),t},isDesktopDevice:function(){return!KTUtil.isMobileDevice()},getViewPort:function(){var t=window,e="inner";return"innerWidth"in window||(e="client",t=document.documentElement||document.body),{width:t[e+"Width"],height:t[e+"Height"]}},isInResponsiveRange:function(t){var e=this.getViewPort().width;return"general"==t||("desktop"==t&&e>=this.getBreakpoint("lg")+1||("tablet"==t&&e>=this.getBreakpoint("md")+1&&e<this.getBreakpoint("lg")||("mobile"==t&&e<=this.getBreakpoint("md")||("desktop-and-tablet"==t&&e>=this.getBreakpoint("md")+1||("tablet-and-mobile"==t&&e<=this.getBreakpoint("lg")||"minimal-desktop-and-below"==t&&e<=this.getBreakpoint("xl"))))))},isBreakpointUp:function(t){return this.getViewPort().width>=this.getBreakpoint(t)},isBreakpointDown:function(t){return this.getViewPort().width<this.getBreakpoint(t)},getUniqueID:function(t){return t+Math.floor(Math.random()*(new Date).getTime())},getBreakpoint:function(t){return e[t]},isset:function(t,e){var n;if(-1!==(e=e||"").indexOf("["))throw new Error("Unsupported object path notation.");e=e.split(".");do{if(void 0===t)return!1;if(n=e.shift(),!t.hasOwnProperty(n))return!1;t=t[n]}while(e.length);return!0},getHighestZindex:function(t){for(var e,n;t&&t!==document;){if(("absolute"===(e=KTUtil.css(t,"position"))||"relative"===e||"fixed"===e)&&(n=parseInt(KTUtil.css(t,"z-index")),!isNaN(n)&&0!==n))return n;t=t.parentNode}return null},hasFixedPositionedParent:function(t){for(;t&&t!==document;){if("fixed"===KTUtil.css(t,"position"))return!0;t=t.parentNode}return!1},sleep:function(t){for(var e=(new Date).getTime(),n=0;n<1e7&&!((new Date).getTime()-e>t);n++);},getRandomInt:function(t,e){return Math.floor(Math.random()*(e-t+1))+t},isAngularVersion:function(){return void 0!==window.Zone},deepExtend:function(t){t=t||{};for(var e=1,n=arguments.length;e<n;++e){var i=arguments[e];if(i)for(var o in i)i.hasOwnProperty(o)&&("[object Object]"!==Object.prototype.toString.call(i[o])?t[o]=i[o]:t[o]=KTUtil.deepExtend(t[o],i[o]))}return t},extend:function(t){t=t||{};for(var e=1;e<arguments.length;e++)if(arguments[e])for(var n in arguments[e])arguments[e].hasOwnProperty(n)&&(t[n]=arguments[e][n]);return t},getById:function(t){return"string"==typeof t?document.getElementById(t):t},getByTag:function(t){return document.getElementsByTagName(t)},getByTagName:function(t){return document.getElementsByTagName(t)},getByClass:function(t){return document.getElementsByClassName(t)},getBody:function(){return document.getElementsByTagName("body")[0]},hasClasses:function(t,e){if(t){for(var n=e.split(" "),i=0;i<n.length;i++)if(0==KTUtil.hasClass(t,KTUtil.trim(n[i])))return!1;return!0}},hasClass:function(t,e){if(t)return t.classList?t.classList.contains(e):new RegExp("\\b"+e+"\\b").test(t.className)},addClass:function(t,e){if(t&&void 0!==e){var n=e.split(" ");if(t.classList)for(var i=0;i<n.length;i++)n[i]&&n[i].length>0&&t.classList.add(KTUtil.trim(n[i]));else if(!KTUtil.hasClass(t,e))for(var o=0;o<n.length;o++)t.className+=" "+KTUtil.trim(n[o])}},removeClass:function(t,e){if(t&&void 0!==e){var n=e.split(" ");if(t.classList)for(var i=0;i<n.length;i++)t.classList.remove(KTUtil.trim(n[i]));else if(KTUtil.hasClass(t,e))for(var o=0;o<n.length;o++)t.className=t.className.replace(new RegExp("\\b"+KTUtil.trim(n[o])+"\\b","g"),"")}},triggerCustomEvent:function(t,e,n){var i;window.CustomEvent?i=new CustomEvent(e,{detail:n}):(i=document.createEvent("CustomEvent")).initCustomEvent(e,!0,!0,n),t.dispatchEvent(i)},triggerEvent:function(t,e){var n;if(t.ownerDocument)n=t.ownerDocument;else{if(9!=t.nodeType)throw new Error("Invalid node passed to fireEvent: "+t.id);n=t}if(t.dispatchEvent){var i="";switch(e){case"click":case"mouseenter":case"mouseleave":case"mousedown":case"mouseup":i="MouseEvents";break;case"focus":case"change":case"blur":case"select":i="HTMLEvents";break;default:throw"fireEvent: Couldn't find an event class for event '"+e+"'."}var o="change"!=e;(r=n.createEvent(i)).initEvent(e,o,!0),r.synthetic=!0,t.dispatchEvent(r,!0)}else if(t.fireEvent){var r;(r=n.createEventObject()).synthetic=!0,t.fireEvent("on"+e,r)}},index:function(t){for(var e=t.parentNode.children,n=0;n<e.length;n++)if(e[n]==t)return n},trim:function(t){return t.trim()},eventTriggered:function(t){return!!t.currentTarget.dataset.triggered||(t.currentTarget.dataset.triggered=!0,!1)},remove:function(t){t&&t.parentNode&&t.parentNode.removeChild(t)},find:function(t,e){if(t=KTUtil.getById(t))return t.querySelector(e)},findAll:function(t,e){if(t=KTUtil.getById(t))return t.querySelectorAll(e)},insertAfter:function(t,e){return e.parentNode.insertBefore(t,e.nextSibling)},parents:function(t,e){Element.prototype.matches||(Element.prototype.matches=Element.prototype.matchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector||Element.prototype.webkitMatchesSelector||function(t){for(var e=(this.document||this.ownerDocument).querySelectorAll(t),n=e.length;--n>=0&&e.item(n)!==this;);return n>-1});for(var n=[];t&&t!==document;t=t.parentNode)e?t.matches(e)&&n.push(t):n.push(t);return n},children:function(t,e,n){if(t&&t.childNodes){for(var i=[],o=0,r=t.childNodes.length;o<r;++o)1==t.childNodes[o].nodeType&&KTUtil.matches(t.childNodes[o],e,n)&&i.push(t.childNodes[o]);return i}},child:function(t,e,n){var i=KTUtil.children(t,e,n);return i?i[0]:null},matches:function(t,e,n){var i=Element.prototype,o=i.matches||i.webkitMatchesSelector||i.mozMatchesSelector||i.msMatchesSelector||function(t){return-1!==[].indexOf.call(document.querySelectorAll(t),this)};return!(!t||!t.tagName)&&o.call(t,e)},data:function(t){return{set:function(e,n){t&&(void 0===t.customDataTag&&(window.KTUtilElementDataStoreID++,t.customDataTag=window.KTUtilElementDataStoreID),void 0===window.KTUtilElementDataStore[t.customDataTag]&&(window.KTUtilElementDataStore[t.customDataTag]={}),window.KTUtilElementDataStore[t.customDataTag][e]=n)},get:function(e){if(t)return void 0===t.customDataTag?null:this.has(e)?window.KTUtilElementDataStore[t.customDataTag][e]:null},has:function(e){return!!t&&(void 0!==t.customDataTag&&!(!window.KTUtilElementDataStore[t.customDataTag]||!window.KTUtilElementDataStore[t.customDataTag][e]))},remove:function(e){t&&this.has(e)&&delete window.KTUtilElementDataStore[t.customDataTag][e]}}},outerWidth:function(t,e){var n;return!0===e?(n=parseFloat(t.offsetWidth),n+=parseFloat(KTUtil.css(t,"margin-left"))+parseFloat(KTUtil.css(t,"margin-right")),parseFloat(n)):n=parseFloat(t.offsetWidth)},offset:function(t){var e,n;if(t)return t.getClientRects().length?(e=t.getBoundingClientRect(),n=t.ownerDocument.defaultView,{top:e.top+n.pageYOffset,left:e.left+n.pageXOffset}):{top:0,left:0}},height:function(t){return KTUtil.css(t,"height")},outerHeight:function(t,e=!1){var n,i=t.offsetHeight;return e?(n=getComputedStyle(t),i+=parseInt(n.marginTop)+parseInt(n.marginBottom)):i},visible:function(t){return!(0===t.offsetWidth&&0===t.offsetHeight)},attr:function(t,e,n){if(null!=t)return void 0===n?t.getAttribute(e):void t.setAttribute(e,n)},hasAttr:function(t,e){if(null!=t)return!!t.getAttribute(e)},removeAttr:function(t,e){null!=t&&t.removeAttribute(e)},animate:function(t,e,n,i,o,r){var a={};if(a.linear=function(t,e,n,i){return n*t/i+e},o=a.linear,"number"==typeof t&&"number"==typeof e&&"number"==typeof n&&"function"==typeof i){"function"!=typeof r&&(r=function(){});var l=window.requestAnimationFrame||function(t){window.setTimeout(t,20)},s=e-t;i(t);var c=window.performance&&window.performance.now?window.performance.now():+new Date;l(function a(d){var u=(d||+new Date)-c;u>=0&&i(o(u,t,s,n)),u>=0&&u>=n?(i(e),r()):l(a)})}},actualCss:function(t,e,n){var i,o="";if(t instanceof HTMLElement!=!1)return t.getAttribute("kt-hidden-"+e)&&!1!==n?parseFloat(t.getAttribute("kt-hidden-"+e)):(o=t.style.cssText,t.style.cssText="position: absolute; visibility: hidden; display: block;","width"==e?i=t.offsetWidth:"height"==e&&(i=t.offsetHeight),t.style.cssText=o,t.setAttribute("kt-hidden-"+e,i),parseFloat(i))},actualHeight:function(t,e){return KTUtil.actualCss(t,"height",e)},actualWidth:function(t,e){return KTUtil.actualCss(t,"width",e)},getScroll:function(t,e){return e="scroll"+e,t==window||t==document?self["scrollTop"==e?"pageYOffset":"pageXOffset"]||browserSupportsBoxModel&&document.documentElement[e]||document.body[e]:t[e]},css:function(t,e,n){if(t)if(void 0!==n)t.style[e]=n;else{var i=(t.ownerDocument||document).defaultView;if(i&&i.getComputedStyle)return e=e.replace(/([A-Z])/g,"-$1").toLowerCase(),i.getComputedStyle(t,null).getPropertyValue(e);if(t.currentStyle)return e=e.replace(/\-(\w)/g,function(t,e){return e.toUpperCase()}),n=t.currentStyle[e],/^\d+(em|pt|%|ex)?$/i.test(n)?function(e){var n=t.style.left,i=t.runtimeStyle.left;return t.runtimeStyle.left=t.currentStyle.left,t.style.left=e||0,e=t.style.pixelLeft+"px",t.style.left=n,t.runtimeStyle.left=i,e}(n):n}},slide:function(t,e,n,i,o){if(!(!t||"up"==e&&!1===KTUtil.visible(t)||"down"==e&&!0===KTUtil.visible(t))){n=n||600;var r=KTUtil.actualHeight(t),a=!1,l=!1;KTUtil.css(t,"padding-top")&&!0!==KTUtil.data(t).has("slide-padding-top")&&KTUtil.data(t).set("slide-padding-top",KTUtil.css(t,"padding-top")),KTUtil.css(t,"padding-bottom")&&!0!==KTUtil.data(t).has("slide-padding-bottom")&&KTUtil.data(t).set("slide-padding-bottom",KTUtil.css(t,"padding-bottom")),KTUtil.data(t).has("slide-padding-top")&&(a=parseInt(KTUtil.data(t).get("slide-padding-top"))),KTUtil.data(t).has("slide-padding-bottom")&&(l=parseInt(KTUtil.data(t).get("slide-padding-bottom"))),"up"==e?(t.style.cssText="display: block; overflow: hidden;",a&&KTUtil.animate(0,a,n,function(e){t.style.paddingTop=a-e+"px"},"linear"),l&&KTUtil.animate(0,l,n,function(e){t.style.paddingBottom=l-e+"px"},"linear"),KTUtil.animate(0,r,n,function(e){t.style.height=r-e+"px"},"linear",function(){t.style.height="",t.style.display="none","function"==typeof i&&i()})):"down"==e&&(t.style.cssText="display: block; overflow: hidden;",a&&KTUtil.animate(0,a,n,function(e){t.style.paddingTop=e+"px"},"linear",function(){t.style.paddingTop=""}),l&&KTUtil.animate(0,l,n,function(e){t.style.paddingBottom=e+"px"},"linear",function(){t.style.paddingBottom=""}),KTUtil.animate(0,r,n,function(e){t.style.height=e+"px"},"linear",function(){t.style.height="",t.style.display="",t.style.overflow="","function"==typeof i&&i()}))}},slideUp:function(t,e,n){KTUtil.slide(t,"up",e,n)},slideDown:function(t,e,n){KTUtil.slide(t,"down",e,n)},show:function(t,e){void 0!==t&&(t.style.display=e||"block")},hide:function(t){void 0!==t&&(t.style.display="none")},addEvent:function(t,e,n,i){null!=t&&t.addEventListener(e,n)},removeEvent:function(t,e,n){null!==t&&t.removeEventListener(e,n)},on:function(t,e,n,i){if(e){var o=KTUtil.getUniqueID("event");return window.KTUtilDelegatedEventHandlers[o]=function(n){for(var o=t.querySelectorAll(e),r=n.target;r&&r!==t;){for(var a=0,l=o.length;a<l;a++)r===o[a]&&i.call(r,n);r=r.parentNode}},KTUtil.addEvent(t,n,window.KTUtilDelegatedEventHandlers[o]),o}},off:function(t,e,n){t&&window.KTUtilDelegatedEventHandlers[n]&&(KTUtil.removeEvent(t,e,window.KTUtilDelegatedEventHandlers[n]),delete window.KTUtilDelegatedEventHandlers[n])},one:function(t,e,n){t.addEventListener(e,function e(i){return i.target&&i.target.removeEventListener&&i.target.removeEventListener(i.type,e),t&&t.removeEventListener&&i.currentTarget.removeEventListener(i.type,e),n(i)})},hash:function(t){var e,n=0;if(0===t.length)return n;for(e=0;e<t.length;e++)n=(n<<5)-n+t.charCodeAt(e),n|=0;return n},animateClass:function(t,e,n){var i,o={animation:"animationend",OAnimation:"oAnimationEnd",MozAnimation:"mozAnimationEnd",WebkitAnimation:"webkitAnimationEnd",msAnimation:"msAnimationEnd"};for(var r in o)void 0!==t.style[r]&&(i=o[r]);KTUtil.addClass(t,"animated "+e),KTUtil.one(t,i,function(){KTUtil.removeClass(t,"animated "+e)}),n&&KTUtil.one(t,i,n)},transitionEnd:function(t,e){var n,i={transition:"transitionend",OTransition:"oTransitionEnd",MozTransition:"mozTransitionEnd",WebkitTransition:"webkitTransitionEnd",msTransition:"msTransitionEnd"};for(var o in i)void 0!==t.style[o]&&(n=i[o]);KTUtil.one(t,n,e)},animationEnd:function(t,e){var n,i={animation:"animationend",OAnimation:"oAnimationEnd",MozAnimation:"mozAnimationEnd",WebkitAnimation:"webkitAnimationEnd",msAnimation:"msAnimationEnd"};for(var o in i)void 0!==t.style[o]&&(n=i[o]);KTUtil.one(t,n,e)},animateDelay:function(t,e){for(var n=["webkit-","moz-","ms-","o-",""],i=0;i<n.length;i++)KTUtil.css(t,n[i]+"animation-delay",e)},animateDuration:function(t,e){for(var n=["webkit-","moz-","ms-","o-",""],i=0;i<n.length;i++)KTUtil.css(t,n[i]+"animation-duration",e)},scrollTo:function(t,e,n){n=n||500;var i,o,r=t?KTUtil.offset(t).top:0,a=window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0;e&&(a+=e),i=a,o=r,KTUtil.animate(i,o,n,function(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t})},scrollTop:function(t,e){KTUtil.scrollTo(null,t,e)},isArray:function(t){return t&&Array.isArray(t)},ready:function(t){(document.attachEvent?"complete"===document.readyState:"loading"!==document.readyState)?t():document.addEventListener("DOMContentLoaded",t)},isEmpty:function(t){for(var e in t)if(t.hasOwnProperty(e))return!1;return!0},numberString:function(t){for(var e=(t+="").split("."),n=e[0],i=e.length>1?"."+e[1]:"",o=/(\d+)(\d{3})/;o.test(n);)n=n.replace(o,"$1,$2");return n+i},detectIE:function(){var t=window.navigator.userAgent,e=t.indexOf("MSIE ");if(e>0)return parseInt(t.substring(e+5,t.indexOf(".",e)),10);if(t.indexOf("Trident/")>0){var n=t.indexOf("rv:");return parseInt(t.substring(n+3,t.indexOf(".",n)),10)}var i=t.indexOf("Edge/");return i>0&&parseInt(t.substring(i+5,t.indexOf(".",i)),10)},isRTL:function(){var t=KTUtil.getByTagName("html")[0];if(t)return"rtl"==KTUtil.attr(t,"direction")},scrollInit:function(t,e){if(t){e=KTUtil.deepExtend({},{wheelSpeed:.5,swipeEasing:!0,wheelPropagation:!1,minScrollbarLength:40,maxScrollbarLength:300,suppressScrollX:!0},e),n(),e.handleWindowResize&&KTUtil.addResizeHandler(function(){n()})}function n(){var n,i,o=t.getAttributeNames();if(o.length>0&&o.forEach(function(n){if(/^data-.*/g.test(n)&&0==["scroll","height","mobile-height"].includes(i)){var i=n.replace("data-","").toLowerCase().replace(/(?:[\s-])\w/g,function(t){return t.replace("-","").toUpperCase()});e[i]=KTUtil.filterBoolean(t.getAttribute(n))}}),!1!==(i=e.height instanceof Function?e.height.call():!0===KTUtil.isMobileDevice()&&e.mobileHeight?parseInt(e.mobileHeight):parseInt(e.height)))if(i=parseInt(i),!e.mobileNativeScroll&&!e.disableForMobile||!0!==KTUtil.isMobileDevice())if(i>0&&KTUtil.css(t,"height",i+"px"),e.desktopNativeScroll)KTUtil.css(t,"overflow","auto");else{"true"==KTUtil.attr(t,"data-window-scroll")&&(e.windowScroll=!0),(n=KTUtil.data(t).get("ps"))?n.update():(KTUtil.css(t,"overflow","hidden"),KTUtil.addClass(t,"scroll"),n=new PerfectScrollbar(t,e),KTUtil.data(t).set("ps",n));KTUtil.attr(t,"id")}else(n=KTUtil.data(t).get("ps"))?(e.resetHeightOnDestroy?KTUtil.css(t,"height","auto"):(KTUtil.css(t,"overflow","auto"),i>0&&KTUtil.css(t,"height",i+"px")),n.destroy(),n=KTUtil.data(t).remove("ps")):i>0&&(KTUtil.css(t,"overflow","auto"),KTUtil.css(t,"height",i+"px"));else KTUtil.scrollDestroy(t,!0)}},scrollUpdate:function(t){var e=KTUtil.data(t).get("ps");e&&e.update()},scrollUpdateAll:function(t){for(var e=KTUtil.findAll(t,".ps"),n=0,i=e.length;n<i;n++)KTUtil.scrollUpdate(e[n])},scrollDestroy:function(t,e){var n=KTUtil.data(t).get("ps");n&&(n.destroy(),n=KTUtil.data(t).remove("ps")),t&&e&&(t.style.setProperty("overflow",""),t.style.setProperty("height",""))},filterBoolean:function(t){return!0===t||"true"===t||!1!==t&&"false"!==t&&t},setHTML:function(t,e){t.innerHTML=e},getHTML:function(t){if(t)return t.innerHTML},getDocumentHeight:function(){var t=document.body,e=document.documentElement;return Math.max(t.scrollHeight,t.offsetHeight,e.clientHeight,e.scrollHeight,e.offsetHeight)},getScrollTop:function(){return(document.scrollingElement||document.documentElement).scrollTop},colorDarken:function(t,e){var n=function(t,e){var n=parseInt(t,16)-e,i=n<0?0:n;return i=i.toString(16).length>1?i.toString(16):`0${i.toString(16)}`};return t=t.indexOf("#")>=0?t.substring(1,t.length):t,e=parseInt(255*e/100),`#${n(t.substring(0,2),e)}${n(t.substring(2,4),e)}${n(t.substring(4,6),e)}`},colorLighten:function(t,e){var n=function(t,e){var n=parseInt(t,16)+e,i=n>255?255:n;return i=i.toString(16).length>1?i.toString(16):`0${i.toString(16)}`};return t=t.indexOf("#")>=0?t.substring(1,t.length):t,e=parseInt(255*e/100),`#${n(t.substring(0,2),e)}${n(t.substring(2,4),e)}${n(t.substring(4,6),e)}`},throttle:function(t,e,n){t||(t=setTimeout(function(){e(),t=void 0},n))},debounce:function(t,e,n){clearTimeout(t),t=setTimeout(e,n)},btnWait:function(t,e,n,i=!0){if(t&&(i&&KTUtil.attr(t,"disabled",!0),e&&(KTUtil.addClass(t,e),KTUtil.attr(t,"wait-class",e)),n)){var o=KTUtil.find(t,".btn-caption");o?(KTUtil.data(o).set("caption",KTUtil.getHTML(o)),KTUtil.setHTML(o,n)):(KTUtil.data(t).set("caption",KTUtil.getHTML(t)),KTUtil.setHTML(t,n))}},btnRelease:function(t){if(t){KTUtil.removeAttr(t,"disabled"),KTUtil.hasAttr(t,"wait-class")&&KTUtil.removeClass(t,KTUtil.attr(t,"wait-class"));var e=KTUtil.find(t,".btn-caption");e&&KTUtil.data(e).has("caption")?KTUtil.setHTML(e,KTUtil.data(e).get("caption")):KTUtil.data(t).has("caption")&&KTUtil.setHTML(t,KTUtil.data(t).get("caption"))}},isOffscreen:function(t,e,n=0){var i=KTUtil.getViewPort().width,o=KTUtil.getViewPort().height,r=KTUtil.offset(t).top,a=KTUtil.outerHeight(t)+n,l=KTUtil.offset(t).left,s=KTUtil.outerWidth(t)+n;if("bottom"==e){if(o<r+a)return!0;if(o>r+1.5*a)return!0}if("top"==e){if(r<0)return!0;if(r>a)return!0}return"left"==e&&l<0||"right"==e&&i<l+s}}}();"undefined"!=typeof module&&void 0!==module.exports&&(module.exports=KTUtil),KTUtil.ready(function(){"undefined"!=typeof KTAppSettings?KTUtil.init(KTAppSettings):KTUtil.init()}),window.onload=function(){var t=KTUtil.getByTagName("body");t&&t[0]&&KTUtil.removeClass(t[0],"page-loading")};

export interface AddPurchaseOrder {
  purchase_order: addPurchaseOrderBody;
}

export interface addPurchaseOrderBody {
  description: string;
  po_number: string;
  amount: string; // Consider using number if calculations are needed
  termination_date: string; // Consider Date type if working with dates
  customer_id: number;
}

export interface PurchaseOrderQueryParam {
  customer_id: number;
}

interface PurchaseOrder {
  id: number;
  description: string;
  customer_id: number;
  amount: string;
  po_number: string;
  termination_date: string;
}

export interface PurchaseOrderWrapper {
  purchase_order: PurchaseOrder;
}

export interface PurchaseOrderList {
  purchase_orders: PurchaseOrderWrapper[];
}

export enum ProjectAPIState {
  Paused = 'PAUSED',
  Recalculating = 'Recalculating',
  Validated = 'YES',
  NotValidate = 'NO',
  INPROGRESS = 'IN PROGRESS'
}

export enum ProjectUserState {
  Paused = 'PAUSED',
  Recalculating = 'Recalculating...',
  Validated = 'Validated',
  NotValidate = 'IN PROGRESS',
  INPROGRESS = 'IN PROGRESS...'
}

export enum ProjectButtonState {
  Resume = 'Calculate',
  ReCalculate = 'Re Calculate'
}

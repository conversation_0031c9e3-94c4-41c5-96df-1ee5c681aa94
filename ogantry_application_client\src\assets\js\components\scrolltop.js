"use strict";var KTScrolltop=function(t,e){var n=this,o=KTUtil.getById(t),l=KTUtil.getBody();if(o){var i={offset:300,speed:6e3},r={construct:function(t){return KTUtil.data(o).has("scrolltop")?n=KTUtil.data(o).get("scrolltop"):(r.init(t),r.build(),KTUtil.data(o).set("scrolltop",n)),n},init:function(t){n.events=[],n.options=KTUtil.deepExtend({},i,t)},build:function(){window.addEventListener("scroll",function(){KTUtil.throttle(void 0,function(){r.handle()},200)}),KTUtil.addEvent(o,"click",r.scroll)},handle:function(){KTUtil.getScrollTop()>n.options.offset?!1===l.hasAttribute("data-scrolltop")&&l.setAttribute("data-scrolltop","on"):!0===l.hasAttribute("data-scrolltop")&&l.removeAttribute("data-scrolltop")},scroll:function(t){t.preventDefault(),KTUtil.scrollTop(0,n.options.speed)},eventTrigger:function(t,e){for(var o=0;o<n.events.length;o++){var l=n.events[o];if(l.name==t){if(1!=l.one)return l.handler.call(this,n,e);if(0==l.fired)return n.events[o].fired=!0,l.handler.call(this,n,e)}}},addEvent:function(t,e,o){n.events.push({name:t,handler:e,one:o,fired:!1})}};return n.setDefaults=function(t){i=t},n.on=function(t,e){return r.addEvent(t,e)},n.one=function(t,e){return r.addEvent(t,e,!0)},r.construct.apply(n,[e]),!0,n}};"undefined"!=typeof module&&void 0!==module.exports&&(module.exports=KTScrolltop);

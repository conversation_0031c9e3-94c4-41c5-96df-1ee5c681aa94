import { NgModule } from '@angular/core';

import { ClientRoutingModule } from './client-routing.module';
import { ManageClientComponent } from './manage-client/manage-client.component';
import { CreateClientComponent } from './create-client/create-client.component';
import { TableModule } from 'primeng/table';
import { DropdownModule } from 'primeng/dropdown';
import { InputTextModule } from 'primeng/inputtext';
import { SharedModule } from '@shared/shared.module';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { InlineSVGModule } from 'ng-inline-svg';
import { MatSidenavModule } from '@angular/material/sidenav';
import { ListboxModule } from 'primeng/listbox';
import { DialogModule } from 'primeng/dialog';
import { BadgeModule } from 'primeng/badge';
import { TreeSelectModule } from 'primeng/treeselect';
import { CheckboxModule } from 'primeng/checkbox';
import { TreeTableModule } from 'primeng/treetable';

@NgModule({
  declarations: [ManageClientComponent, CreateClientComponent],
  imports: [
    SharedModule,
    ClientRoutingModule,
    TableModule,
    DropdownModule,
    InputTextModule,
    MatSlideToggleModule,
    InlineSVGModule,
    MatSidenavModule,
    ListboxModule,
    DialogModule,
    TreeSelectModule,
    BadgeModule,
    TreeTableModule,
    CheckboxModule
  ]
})
export class ClientModule {}

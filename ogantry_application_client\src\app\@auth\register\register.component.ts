// Angular
import { ChangeDete<PERSON><PERSON><PERSON>, <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewEncapsulation } from '@angular/core';
import { Router } from '@angular/router';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
// Translate
import { TranslateService } from '@ngx-translate/core';
// Auth
import { Subject } from 'rxjs/internal/Subject';
import { ConfirmPasswordValidator } from './confirm-password.validator';
import { AuthNoticeService } from '@auth/auth-notice/auth-notice.service';
import { AuthService } from '@auth/auth.service';
import { User } from '@shared/models/user.model';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { AppConstants } from '@shared/constants';

@Component({
  selector: 'kt-register',
  templateUrl: './register.component.html',
  encapsulation: ViewEncapsulation.None
})
export class RegisterComponent extends SflBaseComponent implements <PERSON><PERSON><PERSON><PERSON>, On<PERSON><PERSON>roy {
  registerForm: FormGroup;
  loading = false;

  private readonly unsubscribe: Subject<string>; // Read more: => https://brianflove.com/2016/12/11/anguar-2-unsubscribe-observables/

  /**
   * Component constructor
   *
   * @param authNoticeService: AuthNoticeService
   * @param translate: TranslateService
   * @param router: Router
   * @param auth: AuthService
   * @param store: Store<AppState>
   * @param fb: FormBuilder
   * @param cdr
   */
  constructor(
    private readonly authNoticeService: AuthNoticeService,
    private readonly translate: TranslateService,
    private readonly router: Router,
    private readonly auth: AuthService,
    private readonly fb: FormBuilder,
    private readonly cdr: ChangeDetectorRef
  ) {
    super();
    this.unsubscribe = new Subject();
  }

  /**
   * On init
   */
  ngOnInit() {
    this.initRegisterForm();
  }

  /*
   * On destroy
   */
  ngOnDestroy(): void {
    this.unsubscribe.next();
    this.unsubscribe.complete();
    this.loading = false;
  }

  /**
   * Form initalization
   * Default params, validators
   */
  initRegisterForm() {
    this.registerForm = this.fb.group(
      {
        fullname: ['', Validators.compose([Validators.required, Validators.minLength(AppConstants.requiredMinLength), Validators.maxLength(AppConstants.passwordMaxLength)])],
        email: [
          '',
          Validators.compose([Validators.required, Validators.email, Validators.minLength(AppConstants.requiredMinLength), Validators.maxLength(AppConstants.requiredMaxLength)])
        ],
        username: ['', Validators.compose([Validators.required, Validators.minLength(AppConstants.requiredMinLength), Validators.maxLength(AppConstants.passwordMaxLength)])],
        password: ['', Validators.compose([Validators.required, Validators.minLength(AppConstants.requiredMinLength), Validators.maxLength(AppConstants.passwordMaxLength)])],
        confirmPassword: [
          '',
          Validators.compose([Validators.required, Validators.minLength(AppConstants.requiredMinLength), Validators.maxLength(AppConstants.passwordMaxLength)])
        ],
        agree: [false, Validators.compose([Validators.required])]
      },
      {
        validators: [ConfirmPasswordValidator.MatchPassword]
      }
    );
  }

  /**
   * Form Submit
   */
  submit() {
    const controls = this.registerForm.controls;

    // check form
    if (this.registerForm.invalid) {
      Object.keys(controls).forEach((controlName) => controls[controlName].markAsTouched());
      return;
    }

    this.loading = true;

    if (!controls.agree.value) {
      // you must agree the terms and condition
      // checkbox cannot work inside mat-form-field https://github.com/angular/material2/issues/7891
      this.authNoticeService.setNotice('You must agree the terms and condition', 'danger');
      return;
    }

    const _user: User = new User();
  }
}

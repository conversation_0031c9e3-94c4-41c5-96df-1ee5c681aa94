import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Params } from '@angular/router';
import { AuthNoticeService } from '@auth/auth-notice/auth-notice.service';
import { AdministrationService } from '@entities/administration/administration.service';
import { EmployeeType } from '@entities/utilization-management/utilization.model';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { ButtonParams, OGantryHttpResponse } from '@shared/models';
import { KtDialogService } from '@shared/services';

@Component({
  selector: 'app-create-employee-type',
  templateUrl: './create-employee-type.component.html',
  styleUrls: ['./create-employee-type.component.scss']
})
export class CreateEmployeeTypeComponent extends SflBaseComponent implements OnInit {
  cardTitle = 'Add Employee Type';
  cardSubTitle = null;
  buttons: ButtonParams[] = [];
  createEmployeeTypeForm: FormGroup;
  typeId: number;
  employeeType: EmployeeType;
  constructor(
    private readonly administrationService: AdministrationService,
    private readonly authNoticeService: AuthNoticeService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly ktDialogService: KtDialogService
  ) {
    super();
  }
  ngOnInit(): void {
    this.loading$.next(false);
    this.initForm();
    this.getParams();
  }

  getParams() {
    this.activatedRoute.params.subscribe((params: Params) => {
      this.typeId = params.typeId;
      if (this.typeId) {
        this.cardTitle = 'Edit Employee Type';
        this.getEmployeeType();
      }
      this.setBtnParams();
    });
  }

  getEmployeeType() {
    this.loading$.next(true);
    this.subscriptionManager.add(
      this.administrationService.getEmployeeType(this.typeId).subscribe(
        (res) => {
          this.employeeType = res.data;
          this.setExpenseTypeForm();
          this.loading$.next(false);
        },
        () => this.loading$.next(false)
      )
    );
  }

  initForm() {
    this.createEmployeeTypeForm = new FormGroup({
      name: new FormControl('', Validators.required),
      is_employee: new FormControl(false),
      is_utilized: new FormControl(false)
    });
  }

  setBtnParams() {
    this.buttons = [
      {
        btnClass: 'btn btn-cancel',
        btnText: 'Cancel',
        redirectPath: this.appRoutes.MANAGE_EMPLOYEE_TYP
      },
      {
        btnClass: 'btn-save',
        btnText: 'Save',
        btnType: 'submit',
        action: this.typeId ? this.onEdit.bind(this) : this.onSave.bind(this),
        loading: this.isSubmitting
      }
    ];
  }

  onEdit() {
    if (!this.checkFormForValidation(this.createEmployeeTypeForm)) {
      this.isSubmitting = true;
      this.setBtnParams();
      this.subscriptionManager.add(
        this.administrationService.updateEmployeeType(this.createEmployeeTypeForm.value, this.typeId).subscribe(
          (res) => {
            this.onSuccess(res.data, 'Employee Type updated successfully');
          },
          (err) => this.onError(err)
        )
      );
    }
  }

  onSave() {
    if (!this.checkFormForValidation(this.createEmployeeTypeForm)) {
      this.isSubmitting = true;
      this.setBtnParams();
      this.subscriptionManager.add(
        this.administrationService.createEmployeeType(this.createEmployeeTypeForm.value).subscribe(
          (res) => {
            this.onSuccess(res.data, 'Employee Type created successfully');
          },
          (err) => this.onError(err)
        )
      );
    }
  }

  ngOnDestroy() {
    this.authNoticeService.setNotice(null);
    this.ktDialogService.hide();
  }

  onSuccess(expenseType: EmployeeType, successMsg: string) {
    this.isSubmitting = false;
    this.setBtnParams();
    this.employeeType = expenseType;
    this.typeId = this.employeeType.employee_type.id;
    this.setBtnParams();
    this.authNoticeService.setNotice(successMsg, 'success');
  }

  onError(err) {
    this.isSubmitting = false;
    this.setBtnParams();
    const error: OGantryHttpResponse<EmployeeType> = err.error;
    this.authNoticeService.setNotice(error.errors, 'danger');
  }

  setExpenseTypeForm() {
    const expenseType = this.employeeType.employee_type;
    this.createEmployeeTypeForm.controls['name'].setValue(expenseType.name);
    this.createEmployeeTypeForm.controls['is_employee'].setValue(expenseType.is_employee);
    this.createEmployeeTypeForm.controls['is_utilized'].setValue(expenseType.is_utilized);
  }
}

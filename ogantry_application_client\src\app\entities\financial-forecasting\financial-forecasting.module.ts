import { NgModule } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';

import { FinancialForecastingRoutingModule } from './financial-forecasting-routing.module';

import { ManagePLComponent } from './manage-pl/manage-pl.component';
import { MatSidenavModule } from '@angular/material/sidenav';
import { TableModule } from 'primeng/table';
import { TreeTableModule } from 'primeng/treetable';
import { DropdownModule } from 'primeng/dropdown';
import { InputTextModule } from 'primeng/inputtext';
import { SharedModule } from '@shared/shared.module';
import { DialogModule } from 'primeng/dialog';
import { InputNumberModule } from 'primeng/inputnumber';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { PlComparisonComponent } from './pl-comparison/pl-comparison.component';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { TooltipModule } from 'primeng/tooltip';
import { AddCommasToNumbersPipe } from '@shared/pipes/add-commas-to-numbers.pipe';
@NgModule({
  declarations: [ManagePLComponent, PlComparisonComponent],
  imports: [
    CommonModule,
    SharedModule,
    FinancialForecastingRoutingModule,
    TableModule,
    DropdownModule,
    InputTextModule,
    MatSidenavModule,
    TreeTableModule,
    DialogModule,
    InputNumberModule,
    InputTextareaModule,
    NgbModule,
    TooltipModule
  ],
  providers: [DatePipe, AddCommasToNumbersPipe]
})
export class FinancialForecastingModule {}

<span class="mange-time-wrapper">
  <mat-drawer-container id="manage-time" class="h-100">
    <mat-drawer class="filter-sidebar-for-manage-time" #sidebarFilter mode="over" position="end" disableClose [opened]="openFilter">
      <div class="card card-custom add-contact">
        <app-card-header [cardTitle]="'Filter'" [cardLabelClass]="'mb-0'" [buttons]="buttonsForSideBarFilter"></app-card-header>
        <div class="card-body create-card">
          <form class="form filter-form h-100 d-flex justify-content-between flex-column" autocomplete="off" novalidate="novalidate" (ngSubmit)="filterApply()">
            <div class="row">
              <div class="col-12">
                <div class="pt-4">
                  <div class="timesheet-range dropdown form-group first p-2">
                    <label class="form-label timesheet-range-label" for="Timesheet Range">Add Timesheet By</label>
                    <p-dropdown
                      appendTo="body"
                      #multiSelectComp2
                      [options]="timeSheetRangeone"
                      placeholder="Select Timesheet Range"
                      display="chip"
                      [(ngModel)]="selectedTimesheetRange"
                      [ngModelOptions]="{ standalone: true }"
                    ></p-dropdown>
                  </div>
                  <p-tabView [(activeIndex)]="activeIndexTab" (activeIndexChange)="activeIndexTabChange($event)">
                    <p-tabPanel [header]="currentTabName.Client">
                      <div class="PL-border">
                        <!-- <div class="form-group">
                          <label class="form-label background">
                            <div class="width-65">
                              {{
                                isFilterBySavedClientFiltres
                                  ? "Saved Client Filter"
                                  : "Client"
                              }}
                            </div>
                            <div class="form-group pt-0 save-filter-radio">
                              <div class="form-check form-check-inline mt-1">
                                <input
                                  class="form-check-input d-none"
                                  type="radio"
                                  id="saveClientFilter"
                                  [checked]="isFilterBySavedClientFiltres"
                                  autocomplete="off"
                                  (click)="showSavedClientFilterList()"
                                />
                                <label class="mb-0" for="saveClientFilter">
                                  Use Saved Client Filter
                                </label>
                              </div>
                            </div>
                          </label>
                        </div>
                        <div
                          class="form-group first dropdown p-2"
                          *ngIf="isFilterBySavedClientFiltres"
                        >
                          <p-dropdown
                            appendTo="body"
                            placeholder="Select"
                            [options]="savedClientFilterList"
                            (onChange)="getClientIds($event)"
                            [(ngModel)]="dataFilter.selectedSavedClientFilter"
                            [ngModelOptions]="{ standalone: true }"
                          >
                          </p-dropdown>
                        </div> -->
                        <div class="form-group first dropdown p-2" *ngIf="!isFilterBySavedClientFiltres">
                          <p-dropdown
                            appendTo="body"
                            #multiSelectComp2
                            [options]="clientList"
                            placeholder="Select"
                            [showClear]="true"
                            optionLabel="label"
                            display="chip"
                            (onChange)="clientSelected($event)"
                            [(ngModel)]="selectedClientIds"
                            [ngModelOptions]="{ standalone: true }"
                          >
                            <ng-template pTemplate="selectedItem">
                              <div class="flex align-items-center gap-2" *ngIf="selectedClientIds">
                                <div>{{ selectedClientIds.label }}</div>
                              </div>
                            </ng-template>
                          </p-dropdown>
                          <p class="red-text" *ngIf="errorInForm">Please Select {{ currentTabName.Client }}</p>
                        </div>
                      </div>
                    </p-tabPanel>
                    <p-tabPanel [header]="currentTabName.Project">
                      <div class="PL-border">
                        <!-- <div class="form-group">
                          <label class="form-label background">
                            <div class="width-65">
                              {{
                                isFilterBySavedProjectFiltres
                                  ? "Saved Project Filter"
                                  : "Project"
                              }}
                            </div>
                            <div class="form-group pt-0 save-filter-radio">
                              <div class="form-check form-check-inline mt-1">
                                <input
                                  class="form-check-input d-none"
                                  type="radio"
                                  id="saveProjFilter"
                                  [checked]="isFilterBySavedProjectFiltres"
                                  autocomplete="off"
                                  (click)="showSavedProjectFilterList()"
                                />
                                <label class="mb-0" for="saveProjFilter">
                                  Use Saved Project Filter
                                </label>
                              </div>
                            </div>
                          </label>
                        </div> -->
                        <!-- <div
                          class="form-group first dropdown p-2"
                          *ngIf="isFilterBySavedProjectFiltres"
                        >
                          <p-dropdown
                            appendTo="body"
                            placeholder="Select"
                            [options]="savedProjectFilterList"
                            (onChange)="getProjectIds($event)"
                            [(ngModel)]="dataFilter.selectedSavedProjectFilter"
                            [ngModelOptions]="{ standalone: true }"
                          >
                          </p-dropdown>
                        </div> -->
                        <div class="form-group first dropdown p-2" *ngIf="!isFilterBySavedProjectFiltres">
                          <p-dropdown
                            appendTo="body"
                            [options]="projectList"
                            placeholder="Select"
                            display="chip"
                            (onChange)="projectSelected($event)"
                            [(ngModel)]="selectedProjectesIds"
                            [ngModelOptions]="{ standalone: true }"
                            showClear="true"
                            optionLabel="label"
                          >
                            <ng-template pTemplate="selectedItem">
                              <div class="flex align-items-center gap-2" *ngIf="selectedProjectesIds">
                                <div>{{ selectedProjectesIds.label }}</div>
                              </div>
                            </ng-template>
                          </p-dropdown>
                          <p class="red-text" *ngIf="errorInForm">Please Select {{ currentTabName.Project }}</p>
                        </div>
                      </div>
                    </p-tabPanel>
                    <p-tabPanel [header]="currentTabName.Position">
                      <div class="PL-border">
                        <!-- <div class="form-group">
                          <label class="form-label background">Position</label>
                        </div> -->
                        <div class="form-group first dropdown p-2">
                          <p-dropdown
                            appendTo="body"
                            [options]="positionList"
                            [(ngModel)]="selectedPositionOption"
                            [ngModelOptions]="{ standalone: true }"
                            class="position-dropdown-wrapper"
                            placeholder="Select"
                            showClear="true"
                            optionLabel="label"
                            (onChange)="onPositionChange($event)"
                          >
                            <ng-template pTemplate="selectedItem">
                              <div class="flex align-items-center gap-2" *ngIf="selectedPositionOption">
                                <div>{{ selectedPositionOption.label }}</div>
                              </div>
                            </ng-template>
                          </p-dropdown>
                          <p class="red-text" *ngIf="errorInForm">Please Select {{ currentTabName.Position }}</p>
                        </div>
                      </div>
                    </p-tabPanel>
                    <p-tabPanel [header]="currentTabName.Employee">
                      <div class="PL-border filterby-employee">
                        <div class="form-group">
                          <!-- <label class="form-label background">
                            <div class="width-65">
                              {{
                                isFilterBySavedEmployeeFiltres
                                  ? "Saved Employee Filter"
                                  : "Employee"
                              }}
                            </div>
                            <div class="form-group pt-0 save-filter-radio">
                              <div class="form-check form-check-inline mt-1">
                                <input
                                  class="form-check-input d-none"
                                  type="radio"
                                  id="saveEmployeeFilter"
                                  [checked]="isFilterBySavedEmployeeFiltres"
                                  autocomplete="off"
                                  (click)="showSavedEmployeeFilterList()"
                                />
                                <label class="mb-0" for="saveEmployeeFilter">
                                  Use Saved Employee Filter
                                </label>
                              </div>
                            </div>
                          </label>
                        </div>
                        <div
                          class="form-group first dropdown p-2 ng-star-inserted"
                          *ngIf="isFilterBySavedEmployeeFiltres"
                        >
                          <p-dropdown
                            appendTo="body"
                            placeholder="Select"
                            [options]="savedEmployeeFilterList"
                            (onChange)="getEmployeeIds($event)"
                            [(ngModel)]="dataFilter.selectedSavedEmployeeFilter"
                            [ngModelOptions]="{ standalone: true }"
                          >
                          </p-dropdown>
                        </div> -->
                          <!-- <div> -->
                          <div class="form-group first dropdown p-2" *ngIf="!isFilterBySavedEmployeeFiltres">
                            <p-dropdown
                              appendTo="body"
                              [options]="employeeList"
                              placeholder="Select"
                              [(ngModel)]="selectedEmployeeIds"
                              display="chip"
                              (onChange)="employeeSelected($event)"
                              [ngModelOptions]="{ standalone: true }"
                              showClear="true"
                              optionLabel="label"
                            >
                              <ng-template pTemplate="selectedItem">
                                <div class="flex align-items-center gap-2" *ngIf="selectedEmployeeIds">
                                  <div>{{ selectedEmployeeIds.label }}</div>
                                </div>
                              </ng-template>
                            </p-dropdown>
                            <p class="red-text" *ngIf="errorInForm">Please Select {{ currentTabName.Employee }}</p>
                          </div>
                        </div>
                      </div>
                    </p-tabPanel>
                  </p-tabView>
                  <div class="timesheet-range dropdown form-group first p-2">
                    <label class="form-label timesheet-range-label" for="Timesheet Range">Timesheet status</label>
                    <p-dropdown [options]="timesheetStatus" [(ngModel)]="selectedTimeSheetHourStatus" [ngModelOptions]="{ standalone: true }" placeholder="Select a status">
                    </p-dropdown>
                  </div>
                </div>
              </div>
            </div>
            <div class="form-group d-flex flex-wrap justify-content-end align-items-center mt-2 pb-4">
              <button id="addContactCancel" type="button" (click)="resetFilter()" class="btn-cancel">Reset</button>
              <button id="addContactSubmit" type="submit" [isSubmitting]="isSubmitting" class="btn-save">Apply</button>
            </div>
          </form>
        </div>
      </div>
    </mat-drawer>
    <mat-drawer-content class="detail-sidebar-content">
      <ng-container *ngTemplateOutlet="managetimeContent"></ng-container>
    </mat-drawer-content>
  </mat-drawer-container>
</span>

<ng-template #managetimeContent>
  <div class="card card-custom gutter-b overflow-auto scroll-hide">
    <app-card-header
      [cardTitle]="cardTitle"
      [showSplitButton]="activatedMode === pageName.MangeTimesheet"
      [splitButtonDropDownOption]="splitButtonDropDownOption"
      [buttons]="buttons"
    ></app-card-header>
    <div>
      <div class="table-content">
        <p-table
          *ngIf="resizeFlag"
          [autoLayout]="true"
          [value]="customers"
          [scrollable]="true"
          [loading]="loadingTable"
          class="p-table-for-manage-time"
          [paginator]="isShowPaginator()"
          [rows]="isShowPaginator() ? rowsPerPage : null"
          [responsiveLayout]="'scroll'"
          (onPage)="pageChange($event)"
          currentPageReportTemplate="Displaying {first} - {last} of {totalRecords} records"
          [rowsPerPageOptions]="[10, 25, 50]"
          [first]="isShowPaginator() ? currentPage * rowsPerPage : null"
          (onSort)="sortColumn($event)"
          [sortField]="activatedMode === pageName.MangeTimesheet ? sortFieldName : null"
          [sortOrder]="activatedMode === pageName.MangeTimesheet ? sortOrderNumber : null"
        >
          <ng-template pTemplate="caption">
            <div class="d-flex justify-content-between align-items-center">
              <div class="d-flex flex-wrap">
                <app-selected-filter-tags
                  *ngIf="activatedMode === pageName.MangeTimesheet"
                  [tags]="tags"
                  [showClear]="false"
                  (onCancel)="resetFilterTags()"
                ></app-selected-filter-tags>
              </div>
              <div class="d-flex align-items-center flex-nowrap">
                <div>
                  <div *ngIf="activatedMode === pageName.MangeTimesheet" class="float-right mr-2 py-2 pointer" (click)="isShowHideColumns = !isShowHideColumns" #coulmnToggel>
                    <app-filter-icon-shared></app-filter-icon-shared>
                  </div>
                  <span class="bench">
                    <div class="popup-column card">
                      <app-filter-table-fields
                        *ngIf="isShowHideColumns"
                        [selectedColumns]="selectedColumns"
                        [frozenCols]="frozenCols"
                        dynamicBindingKey="monthLabel"
                        (onSelectColumChange)="onSelectColumsChange($event)"
                      ></app-filter-table-fields>
                    </div>
                  </span>
                </div>
                <div (click)="previousMonth()">
                  <button class="btn-switcher">
                    <img src="../../../../assets/media/svg/icons/double-arrow-back.svg" alt="" />
                  </button>
                </div>
                <div>
                  <ng-container *ngIf="selectedTimesheetRange === timeSheetRange.MONTHLY; else dateCalender">
                    <p-calendar
                      [(ngModel)]="selectedMonth"
                      styleClass="month-picker"
                      appendTo="body"
                      view="month"
                      [showIcon]="true"
                      dateFormat="M-yy"
                      (ngModelChange)="setMonth($event)"
                    >
                    </p-calendar>
                  </ng-container>
                  <ng-template #dateCalender>
                    <p-calendar
                      #calendar
                      [(ngModel)]="selectedDateRange"
                      styleClass="month-picker"
                      appendTo="body"
                      [showIcon]="true"
                      (ngModelChange)="onSelectDate($event)"
                      selectionMode="range"
                    >
                    </p-calendar>
                  </ng-template>
                </div>
                <div (click)="nextMonth()">
                  <button class="btn-switcher">
                    <img src="../../../../assets/media/svg/icons/double-arrow-next.svg" alt="" />
                  </button>
                </div>
                <div class="d-flex">
                  <button
                    (click)="saveAddedLogs()"
                    *ngIf="activatedMode === pageName.CreateTimeSheet && !isSubmitted"
                    class="btn-save submit-btn"
                    [disabled]="!(isSubmitted || isDraft)"
                  >
                    Submit
                  </button>
                  <button (click)="onUnSubmitAll()" *ngIf="activatedMode === pageName.CreateTimeSheet && isSubmitted" class="btn-save">UnSubmit</button>
                </div>
              </div>
            </div>
          </ng-template>
          <ng-template pTemplate="header">
            <tr>
              <th *ngIf="_pCols?.includes('Client')" class="col-width-wrapper" pFrozenColumn pResizableColumn></th>
              <th class="col-width-wrapper" [ngClass]="_pCols?.includes('Client') ? 'fix-left-project' : ''" pFrozenColumn pResizableColumn></th>
              <th class="col-width-wrapper" [ngClass]="_pCols?.includes('Client') ? 'fix-left-position' : 'fix-left-position-without-client'" pFrozenColumn pResizableColumn></th>
              <th
                *ngIf="_pCols?.includes('Employee')"
                class="col-width-wrapper fix-employee"
                pFrozenColumn
                pResizableColumn
                [ngClass]="{ 'fix-width-employee': _pCols?.includes('Client') }"
              ></th>
              <th *ngIf="_pCols?.includes('status')" class="col-width-wrapper" pFrozenColumn></th>

              <ng-container *ngFor="let item of extendFields; let index = index">
                <ng-container *ngFor="let filed of item?.jsonData?.extendedFieldsConfig; let index2 = index">
                  <ng-container *ngIf="filed?.component">
                    <ng-container *ngFor="let filedDetails of filed?.fields; let index3 = index">
                      <th colspan="1" class="extended-field-header" pResizableColumn *ngIf="checkSelectedColumnExtendFiled(filed?.component, filedDetails?.name)"></th>
                    </ng-container>
                  </ng-container>
                </ng-container>
              </ng-container>

              <ng-container *ngIf="selectedTimesheetRange === timeSheetRange.WEEKLY && _pCols?.includes('Daily value')">
                <th *ngFor="let item of weekArray" class="text-center day-col-wrapper" [class.gray-background]="isWeekend(item?.fullDate)">
                  {{ item?.fullDate | date : 'EE' }}
                </th>
              </ng-container>

              <ng-container *ngIf="selectedTimesheetRange === timeSheetRange.BIWEEKLY && _pCols?.includes('Daily value')">
                <th *ngFor="let item of biWeekArray" class="text-center day-col-wrapper" [class.gray-background]="isWeekend(item?.fullDate)">
                  {{ item?.fullDate | date : 'EE' }}
                </th>
              </ng-container>
              <ng-container *ngIf="selectedTimesheetRange === timeSheetRange.MONTHLY && _pCols?.includes('Daily value')">
                <th *ngFor="let item of monthArray" class="text-center day-col-wrapper" [class.gray-background]="isWeekend(item?.fullDate)">
                  {{ item?.fullDate | date : 'EE' }}
                </th>
              </ng-container>

              <th
                *ngIf="_pCols?.includes('Total')"
                class="col-width-wrapper last-col-width"
                [ngClass]="isPermissionManageTime ? 'frozen-right-total' : 'frozen-right'"
                pFrozenColumn
                pResizableColumn
              ></th>

              <ng-container *ngIf="activatedMode === pageName.MangeTimesheet">
                <th
                  *hasAnyPermission="[permissionModules.MANAGE_TIME_ENTRIES]; hideTemplate: true"
                  class="col-width-wrapper frozen-right last-col-width"
                  pFrozenColumn
                  pResizableColumn
                ></th>
              </ng-container>
            </tr>
            <tr>
              <th *ngIf="_pCols?.includes('Client')" class="col-width-wrapper" pFrozenColumn pResizableColumn pSortableColumn="customer_name">
                Client <p-sortIcon field="customer_name" *ngIf="activatedMode === pageName.MangeTimesheet"></p-sortIcon>
              </th>
              <th class="col-width-wrapper" [ngClass]="_pCols?.includes('Client') ? 'fix-left-project' : ''" pFrozenColumn pResizableColumn pSortableColumn="project_name">
                Project <p-sortIcon field="project_name" *ngIf="activatedMode === pageName.MangeTimesheet"></p-sortIcon>
              </th>
              <th
                class="col-width-wrapper"
                [ngClass]="_pCols?.includes('Client') ? 'fix-left-position' : 'fix-left-position-without-client'"
                pFrozenColumn
                pResizableColumn
                pSortableColumn="name"
              >
                Position <p-sortIcon field="name" *ngIf="activatedMode === pageName.MangeTimesheet"></p-sortIcon>
              </th>
              <th
                id="Employee"
                *ngIf="_pCols?.includes('Employee')"
                class="col-width-wrapper fix-employee"
                pFrozenColumn
                pResizableColumn
                [ngClass]="{ 'fix-width-employee': _pCols?.includes('Client') }"
                pSortableColumn="employee_first_name"
              >
                <span>Employee</span>
                <p-sortIcon field="employee_first_name" *ngIf="activatedMode === pageName.MangeTimesheet"></p-sortIcon>
              </th>
              <th id="Employee" *ngIf="_pCols?.includes('status')" class="col-width-wrapper">
                <span>Status</span>
              </th>

              <ng-container *ngFor="let item of extendFields; let index = index">
                <ng-container *ngFor="let filed of item?.jsonData?.extendedFieldsConfig; let index2 = index">
                  <ng-container *ngIf="filed?.component">
                    <ng-container *ngFor="let filedDetails of filed?.fields; let index3 = index">
                      <th colspan="1" class="extended-field-header" pResizableColumn *ngIf="checkSelectedColumnExtendFiled(filed?.component, filedDetails?.name)">
                        {{ filed?.component }}:
                        {{ filedDetails?.name | titlecase }}
                      </th>
                    </ng-container>
                  </ng-container>
                </ng-container>
              </ng-container>

              <ng-container *ngIf="selectedTimesheetRange === timeSheetRange.WEEKLY && _pCols?.includes('Daily value')">
                <th *ngFor="let item of weekArray" class="text-center day-col-wrapper" [class.gray-background]="isWeekend(item?.fullDate)">
                  {{ item?.label }}
                </th>
              </ng-container>
              <ng-container *ngIf="selectedTimesheetRange === timeSheetRange.BIWEEKLY && _pCols?.includes('Daily value')">
                <th *ngFor="let item of biWeekArray" class="text-center day-col-wrapper" [class.gray-background]="isWeekend(item?.fullDate)">
                  {{ item?.label }}
                </th>
              </ng-container>
              <ng-container *ngIf="selectedTimesheetRange === timeSheetRange.MONTHLY && _pCols?.includes('Daily value')">
                <th *ngFor="let item of monthArray" class="text-center day-col-wrapper" [class.gray-background]="isWeekend(item?.fullDate)">
                  {{ item?.label }}
                </th>
              </ng-container>
              <th
                *ngIf="_pCols?.includes('Total')"
                class="col-width-wrapper last-col-width"
                [ngClass]="isPermissionManageTime ? 'frozen-right-total' : 'frozen-right'"
                pFrozenColumn
                pResizableColumn
              >
                Total
              </th>
              <ng-container *ngIf="activatedMode === pageName.MangeTimesheet">
                <th
                  *hasAnyPermission="[permissionModules.MANAGE_TIME_ENTRIES]; hideTemplate: true"
                  class="col-width-wrapper frozen-right last-col-width"
                  pFrozenColumn
                  pResizableColumn
                >
                  Action
                </th>
              </ng-container>
            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-customer let-rowIndex="rowIndex">
            <tr>
              <td *ngIf="_pCols?.includes('Client')" class="col-width-wrapper frozen-column" pFrozenColumn pResizableColumn>
                {{ customer?.position?.project?.customer?.name }}
              </td>
              <td class="col-width-wrapper frozen-column" [ngClass]="_pCols?.includes('Client') ? 'fix-left-project' : ''" pFrozenColumn pResizableColumn>
                {{ customer?.position?.project?.name }}
              </td>
              <td
                class="col-width-wrapper frozen-column"
                [ngClass]="_pCols?.includes('Client') ? 'fix-left-position' : 'fix-left-position-without-client'"
                pFrozenColumn
                pResizableColumn
              >
                {{ customer?.position?.name }}
              </td>
              <td
                class="col-width-wrapper fix-employee frozen-column"
                *ngIf="_pCols?.includes('Employee')"
                pFrozenColumn
                pResizableColumn
                [ngClass]="{ 'fix-width-employee': _pCols?.includes('Client') }"
              >
                <span>
                  {{ customer.position.employee.first_name }}
                  {{ customer.position.employee.last_name }}</span
                >
              </td>
              <td class="col-width-wrapper" *ngIf="_pCols?.includes('status')">
                <!-- <span>
                  {{ selectedTimeSheetHourStatusHardCopy===timeSheetStatusEnum.ALL? includeStatusArray(customer?.position?.time_entries) : selectedTimeSheetHourStatusHardCopy  }}</span
                > -->
                <span> {{ customer?.position?.time_entries ? includeStatusArray(customer?.position?.time_entries) : '' }}</span>
              </td>
              <ng-container *ngFor="let item of extendFields; let index = index">
                <ng-container *ngFor="let filed of item?.jsonData?.extendedFieldsConfig; let index2 = index">
                  <ng-container *ngIf="filed?.component">
                    <ng-container *ngFor="let filedDetails of filed?.fields; let index3 = index">
                      <ng-container *ngIf="filed?.component">
                        <td
                          colspan="1"
                          *ngIf="checkSelectedColumnExtendFiled(filed?.component, filedDetails?.name)"
                          class="extended-field-header"
                          [id]="filedDetails?.name"
                          [ngClass]="{
                            'dynamic-text text-truncate': filedDetails.type === filedType.Text_Area
                          }"
                        >
                          {{ getValueBYExtendFiled(filed?.component, customer, filedDetails?.DBTag) }}
                        </td>
                      </ng-container>
                    </ng-container>
                  </ng-container>
                </ng-container>
              </ng-container>

              <ng-container *ngIf="selectedTimesheetRange === timeSheetRange.WEEKLY && _pCols?.includes('Daily value')">
                <ng-container *ngFor="let day of weekArray">
                  <td
                    *ngIf="isDateInRange(day?.fullDate, customer?.position?.start_date, customer?.position?.end_date)"
                    class="day-col-wrapper timesheet-data"
                    [pEditableColumn]="customer?.position[day?.label] || ''"
                    pEditableColumnField="label"
                    [class.gray-background]="isWeekend(day?.fullDate)"
                    [title]="timeentryStatus(customer?.position?.time_entries, day?.fullDate) || ''"
                  >
                    <ng-container *ngIf="true">
                      <p-cellEditor>
                        <ng-template pTemplate="input">
                          <input
                            pInputText
                            type="text"
                            [ngModel]="customer?.position[day?.label]"
                            (input)="validateNumberInput($event, day, rowIndex)"
                            (paste)="validatePaste($event, day, rowIndex)"
                            (blur)="onBlur($event, rowIndex, day)"
                            (keydown)="onEnter($event, rowIndex, day)"
                            [disabled]="isEditableHoursComment(customer?.position?.time_entries, day?.fullDate)"
                          />
                        </ng-template>
                        <ng-template pTemplate="output">
                          <div class="output">
                            {{ outputDayHours(customer, day) }}
                          </div>
                        </ng-template>
                      </p-cellEditor>
                      <span class="add-comment" (click)="onAddCommentClick($event, day, rowIndex, customer?.position[day?.label], customer?.position?.time_entries)">
                        <ng-container *ngIf="getComment(day, customer?.position?.time_entries, rowIndex); else addIcon">
                          <a title="Edit" class="btn btn-icon btn-light btn-sm cursor-pointer custom-btn">
                            <span inlineSVG="assets/media/svg/icons/edit.svg" cacheSVG="true" class="svg-icon custom-icon"> </span>
                          </a>
                        </ng-container>
                        <ng-template #addIcon>
                          <em class="pi pi-plus"></em>
                        </ng-template>
                      </span>
                    </ng-container>
                  </td>
                  <td
                    (click)="positionNOTInRangePopup()"
                    class="day-col-wrapper gray-background"
                    *ngIf="!isDateInRange(day?.fullDate, customer?.position?.start_date, customer?.position?.end_date)"
                  >
                    <div>
                      <p class="my-auto">
                        {{ customer?.position[day?.label] }}
                      </p>
                    </div>
                  </td>
                </ng-container>
              </ng-container>

              <ng-container *ngIf="selectedTimesheetRange === timeSheetRange.BIWEEKLY && _pCols?.includes('Daily value')">
                <ng-container *ngFor="let day of biWeekArray">
                  <td
                    *ngIf="isDateInRange(day?.fullDate, customer?.position?.start_date, customer?.position?.end_date)"
                    class="day-col-wrapper timesheet-data"
                    [pEditableColumn]="customer?.position[day?.label] || ''"
                    pEditableColumnField="label"
                    [class.gray-background]="isWeekend(day?.fullDate)"
                    [title]="timeentryStatus(customer?.position?.time_entries, day?.fullDate) || ''"
                  >
                    <p-cellEditor>
                      <ng-template pTemplate="input">
                        <input
                          pInputText
                          type="text"
                          [ngModel]="customer?.position[day?.label]"
                          (input)="validateNumberInput($event, day, rowIndex)"
                          (paste)="validatePaste($event, day, rowIndex)"
                          (blur)="onBlur($event, rowIndex, day)"
                          (keydown)="onEnter($event, rowIndex, day)"
                          [disabled]="isEditableHoursComment(customer?.position?.time_entries, day?.fullDate)"
                        />
                      </ng-template>
                      <ng-template pTemplate="output">
                        <div class="output">
                          {{ outputDayHours(customer, day) }}
                        </div>
                      </ng-template>
                    </p-cellEditor>
                    <span class="add-comment" (click)="onAddCommentClick($event, day, rowIndex, customer?.position[day?.label], customer?.position?.time_entries)">
                      <ng-container *ngIf="getComment(day, customer?.position?.time_entries, rowIndex); else addIcon">
                        <a title="Edit" class="btn btn-icon btn-light btn-sm cursor-pointer custom-btn">
                          <span inlineSVG="assets/media/svg/icons/edit.svg" cacheSVG="true" class="svg-icon custom-icon"> </span>
                        </a>
                      </ng-container>
                      <ng-template #addIcon>
                        <em class="pi pi-plus"></em>
                      </ng-template>
                    </span>
                  </td>
                  <td
                    (click)="positionNOTInRangePopup()"
                    class="day-col-wrapper gray-background"
                    *ngIf="!isDateInRange(day?.fullDate, customer?.position?.start_date, customer?.position?.end_date)"
                  >
                    <div>
                      <p class="my-auto">
                        {{ customer?.position[day?.label] }}
                      </p>
                    </div>
                  </td>
                </ng-container>
              </ng-container>

              <ng-container *ngIf="selectedTimesheetRange === timeSheetRange.MONTHLY && _pCols?.includes('Daily value')">
                <ng-container *ngFor="let day of monthArray">
                  <td
                    *ngIf="isDateInRange(day?.fullDate, customer?.position?.start_date, customer?.position?.end_date)"
                    class="day-col-wrapper timesheet-data"
                    [pEditableColumn]="customer?.position[day?.label] || ''"
                    pEditableColumnField="label"
                    [class.gray-background]="isWeekend(day?.fullDate)"
                    [title]="timeentryStatus(customer?.position?.time_entries, day?.fullDate) || ''"
                  >
                    <p-cellEditor>
                      <ng-template pTemplate="input">
                        <input
                          pInputText
                          type="text"
                          [ngModel]="customer.position[day.label]"
                          (input)="validateNumberInput($event, day, rowIndex)"
                          (paste)="validatePaste($event, day, rowIndex)"
                          (blur)="onBlur($event, rowIndex, day)"
                          (keydown)="onEnter($event, rowIndex, day)"
                          [disabled]="isEditableHoursComment(customer?.position?.time_entries, day?.fullDate)"
                        />
                      </ng-template>
                      <ng-template pTemplate="output">
                        <div class="output">
                          {{ outputDayHours(customer, day) }}
                        </div>
                      </ng-template>
                    </p-cellEditor>
                    <span class="add-comment" (click)="onAddCommentClick($event, day, rowIndex, customer?.position[day?.label], customer?.position?.time_entries)">
                      <ng-container *ngIf="getComment(day, customer?.position?.time_entries, rowIndex); else addIcon">
                        <a title="Edit" class="btn btn-icon btn-light btn-sm cursor-pointer custom-btn">
                          <span inlineSVG="assets/media/svg/icons/edit.svg" cacheSVG="true" class="svg-icon custom-icon"> </span>
                        </a>
                      </ng-container>
                      <ng-template #addIcon>
                        <em class="pi pi-plus"></em>
                      </ng-template>
                    </span>
                  </td>
                  <td
                    (click)="positionNOTInRangePopup()"
                    class="day-col-wrapper gray-background"
                    *ngIf="!isDateInRange(day.fullDate, customer.position.start_date, customer.position.end_date)"
                  >
                    <div>
                      <p class="my-auto">{{ customer.position[day.label] }}</p>
                    </div>
                  </td>
                </ng-container>
              </ng-container>

              <td
                *ngIf="_pCols?.includes('Total')"
                class="col-width-wrapper last-col-width"
                [ngClass]="isPermissionManageTime ? 'frozen-right-total' : 'frozen-right'"
                pFrozenColumn
                pResizableColumn
              >
                {{ (totalHours[rowIndex] ?? 0).toFixed(2) }}
              </td>
              <ng-container *ngIf="activatedMode === pageName.MangeTimesheet">
                <td
                  *hasAnyPermission="[permissionModules.MANAGE_TIME_ENTRIES]; hideTemplate: true"
                  class="col-width-wrapper frozen-right last-col-width"
                  pFrozenColumn
                  pResizableColumn
                  [title]="checkStatus(customer?.position?.time_entries)"
                >
                  <div class="d-flex mbsc-justify-content-between w-100" *ngIf="customer?.position?.time_entries?.length">
                    <div>
                      <div class="d-flex flex-nowrap flex-row ml-3" *ngIf="checkStatus(customer?.position?.time_entries) || customer?.position?.edit">
                        <div>
                          <img [src]="imageConst.approvedIcon" title="approved" alt="approved" (click)="openApprovedRejectedFlow(customer?.position?.time_entries, true)" />
                        </div>
                        <div>
                          <img [src]="imageConst.removeIcon" title="rejected" alt="rejected" (click)="openApprovedRejectedFlow(customer?.position?.time_entries, false)" />
                        </div>
                      </div>
                      <!-- <div>
                      <div>
                        <img [src]="imageConst.approvedIcon" title="approved" alt="approved">
                      </div>
                      <div>
                        <img [src]="imageConst.removeIcon" title="rejected" alt="rejected">
                      </div>
                    </div> -->
                    </div>
                    <div class="ml-2" *ngIf="!checkStatus(customer?.position?.time_entries) && customer?.position?.edit !== true">
                      <div class="d-flex">
                        <div>
                          <ng-container *ngIf="checkEveryStatusSame(customer?.position?.time_entries, timeSheetStatusEnum.APPROVED)">
                            <img [src]="imageConst.approvedIcon" title="approved" alt="approved" />
                            <span *ngIf="checkEveryStatusSame(customer?.position?.time_entries, timeSheetStatusEnum.APPROVED)"> Approved </span>
                          </ng-container>
                          <ng-container *ngIf="checkEveryStatusSame(customer?.position?.time_entries, timeSheetStatusEnum.Rejected)">
                            <img [src]="imageConst.removeIcon" title="Rejected" alt="Rejected" />
                            <span> Rejected </span>
                          </ng-container>
                        </div>
                        <div class="ml-2">
                          <img [src]="imageConst.editIcon" title="Edit" alt="Edit" (click)="customer?.position.edit = true" />
                        </div>
                      </div>
                    </div>
                    <div></div>
                  </div>
                </td>
              </ng-container>
            </tr>
          </ng-template>
          <ng-template pTemplate="emptymessage" let-customer>
            <p class="w-50 text-capitalize text-center" *ngIf="!customer && activatedMode === pageName.MangeTimesheet && customers?.length === 0">No Data Found</p>
            <p class="w-50 text-capitalize text-center text-danger" *ngIf="!customer && activatedMode === pageName.CreateTimeSheet">
              This employee has no positions during this time frame.
            </p>
            <p class="w-50 text-capitalize text-center text-danger" *ngIf="!customer && tags.length === 0 && activatedMode === pageName.MangeTimesheet">
              Please apply filter to load the Data.
            </p>
          </ng-template>
        </p-table>
      </div>
    </div>
    <div class="pt-5 mt-5" *ngIf="activatedMode === pageName.CreateTimeSheet">
      <div class="table-content">
        <ng-container *ngTemplateOutlet="timeOff"></ng-container>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #timeOff>
  <p-table *ngIf="resizeFlag" [autoLayout]="true" [value]="employeeData" [scrollable]="true" [loading]="loadingTable" class="p-table-for-manage-time">
    <ng-template pTemplate="caption">
      <div class="d-flex justify-content-between align-items-center">
        <H3 *hasAnyPermission="[permissionModules.View_Work_Exception_Request]; hideTemplate: true" class="card-label purple-color">Time OFF</H3>
        <h4 *hasAnyPermission="[permissionModules.Manage_Work_Exception_Request]; hideTemplate: true">
          <a href="javascript:;void" (click)="bookTimeOffPopUp = true" class="purple-color pointer" [ngClass]="{ 'disable-link': !selectedEmployeeIds?.value }">+ Book Time Off</a>
        </h4>
      </div>
    </ng-template>
    <ng-template pTemplate="header">
      <ng-container *hasAnyPermission="[permissionModules.View_Work_Exception_Request]; hideTemplate: true">
        <tr class="fix-Timeofff-length">
          <th id="Employee" class="col-width-wrapper" pFrozenColumn pResizableColumn></th>
          <th id="Employee" class="col-width-wrapper position-sticky work-exception-fix" pFrozenColumn pResizableColumn></th>

          <ng-container *ngIf="selectedTimesheetRange === timeSheetRange.WEEKLY">
            <th *ngFor="let item of weekArray" class="text-center day-col-wrapper" [class.gray-background]="isWeekend(item?.fullDate)">
              {{ item?.fullDate | date : 'EE' }}
            </th>
          </ng-container>

          <ng-container *ngIf="selectedTimesheetRange === timeSheetRange.BIWEEKLY">
            <th *ngFor="let item of biWeekArray" class="text-center day-col-wrapper" [class.gray-background]="isWeekend(item?.fullDate)">
              {{ item?.fullDate | date : 'EE' }}
            </th>
          </ng-container>
          <ng-container *ngIf="selectedTimesheetRange === timeSheetRange.MONTHLY">
            <th *ngFor="let item of monthArray" class="text-center day-col-wrapper" [class.gray-background]="isWeekend(item?.fullDate)">
              {{ item?.fullDate | date : 'EE' }}
            </th>
          </ng-container>
        </tr>
        <tr class="fix-Timeofff-length">
          <th id="Employee" class="col-width-wrapper" pFrozenColumn pResizableColumn>
            <span class="px-2">Employee</span>
          </th>
          <th id="Employee" class="col-width-wrapper position-sticky work-exception-fix" pFrozenColumn pResizableColumn>
            <span>Work exception type</span>
          </th>

          <ng-container *ngIf="selectedTimesheetRange === timeSheetRange.WEEKLY">
            <th *ngFor="let item of weekArray" class="text-center day-col-wrapper" [class.gray-background]="isWeekend(item?.fullDate)">
              {{ item?.label }}
            </th>
          </ng-container>

          <ng-container *ngIf="selectedTimesheetRange === timeSheetRange.BIWEEKLY">
            <th *ngFor="let item of biWeekArray" class="text-center day-col-wrapper" [class.gray-background]="isWeekend(item?.fullDate)">
              {{ item?.label }}
            </th>
          </ng-container>
          <ng-container *ngIf="selectedTimesheetRange === timeSheetRange.MONTHLY">
            <th *ngFor="let item of monthArray" class="text-center day-col-wrapper" [class.gray-background]="isWeekend(item?.fullDate)">
              {{ item?.label }}
            </th>
          </ng-container>

          <!-- <th *ngIf="_pCols?.includes('Total')" class="col-width-wrapper fix-right-col" pFrozenColumn pResizableColumn></th> -->
        </tr>
      </ng-container>
    </ng-template>
    <ng-template pTemplate="body" let-customer let-rowIndex="rowIndex">
      <ng-container *hasAnyPermission="[permissionModules.View_Work_Exception_Request]; hideTemplate: true">
        <ng-container *ngFor="let item of exceptionTypes">
          <tr class="fix-Timeofff-length">
            <td class="col-width-wrapper frozen-column" pFrozenColumn pResizableColumn>
              <span>
                {{ customer?.employee?.name }}
                <!-- {{ customer?.position?.employee?.last_name }} -->
              </span>
            </td>
            <td class="col-width-wrapper frozen-column fix-left-project" pFrozenColumn pResizableColumn>
              {{ item?.work_exception_type?.name }}
            </td>
            <ng-container *ngIf="selectedTimesheetRange === timeSheetRange.WEEKLY">
              <ng-container *ngFor="let day of weekArray">
                <td class="day-col-wrapper timesheet-data" [class.gray-background]="isWeekend(day?.fullDate)">
                  <p-cellEditor style="width: 100%">
                    <ng-template pTemplate="input">
                      <!-- <input
                    pInputText
                    type="text"
                    [ngModel]="customer?.position[day?.label]"
                    (input)="validateNumberInput($event, day, rowIndex)"
                    (paste)="validatePaste($event, day, rowIndex)"
                    (blur)="onBlur($event, rowIndex, day)"
                    (keydown)="onEnter($event, rowIndex, day)"
                  /> -->
                      <!-- <input
                        pInputText
                        type="text"
                        [value]="
                          getHoursByIdAndDate(
                            item?.work_exception_type?.id,
                            day?.fullDate
                          )
                        "
                        [disabled]="true"
                      />  -->
                      <div class="text-center">
                        <span>
                          {{ getHoursByIdAndDate(item?.work_exception_type?.id, day?.fullDate) }}
                        </span>
                      </div>
                    </ng-template>
                    <ng-template pTemplate="output">
                      <div class="text-center">
                        <span>
                          {{ getHoursByIdAndDate(item?.work_exception_type?.id, day?.fullDate) }}
                        </span>
                      </div>
                    </ng-template>
                  </p-cellEditor>
                </td>
              </ng-container>
            </ng-container>

            <ng-container *ngIf="selectedTimesheetRange === timeSheetRange.BIWEEKLY">
              <ng-container *ngFor="let day of biWeekArray">
                <td class="day-col-wrapper timesheet-data" [class.gray-background]="isWeekend(day?.fullDate)">
                  <p-cellEditor style="width: 100%">
                    <ng-template pTemplate="input">
                      <div class="text-center">
                        <span>
                          {{ getHoursByIdAndDate(item?.work_exception_type?.id, day?.fullDate) }}
                        </span>
                      </div>
                    </ng-template>
                    <ng-template pTemplate="output">
                      <div class="text-center">
                        <span>
                          {{ getHoursByIdAndDate(item?.work_exception_type?.id, day?.fullDate) }}
                        </span>
                      </div>
                    </ng-template>
                  </p-cellEditor>
                </td>
              </ng-container>
            </ng-container>

            <ng-container *ngIf="selectedTimesheetRange === timeSheetRange.MONTHLY">
              <ng-container *ngFor="let day of monthArray">
                <td class="day-col-wrapper timesheet-data" [class.gray-background]="isWeekend(day?.fullDate)">
                  <p-cellEditor style="width: 100%">
                    <ng-template pTemplate="input">
                      <div class="text-center pt-2">
                        <span>
                          {{ getHoursByIdAndDate(item?.work_exception_type?.id, day?.fullDate) }}
                        </span>
                      </div>
                    </ng-template>
                    <ng-template pTemplate="output">
                      <div class="text-center pt-2">
                        <span>
                          {{ getHoursByIdAndDate(item?.work_exception_type?.id, day?.fullDate) }}
                        </span>
                      </div>
                    </ng-template>
                  </p-cellEditor>
                </td>
              </ng-container>
            </ng-container>
          </tr>
        </ng-container>
      </ng-container>
    </ng-template>
    <ng-template pTemplate="emptymessage" let-customer>
      <ng-container *hasAnyPermission="[permissionModules.View_Work_Exception_Request]; hideTemplate: true">
        <p class="w-50 text-capitalize text-center" *ngIf="!customer && activatedMode === pageName.MangeTimesheet && customers?.length === 0">No Data Found</p>
        <p class="w-50 text-capitalize text-center text-danger" *ngIf="!customer && activatedMode === pageName.CreateTimeSheet">
          No employee in the system that matches this user Please Contact Your Admin
        </p>
        <p class="w-50 text-capitalize text-center text-danger" *ngIf="!customer && tags.length === 0 && activatedMode === pageName.MangeTimesheet">
          Please apply filter to load the Data.
        </p>
      </ng-container>
    </ng-template>
  </p-table>
</ng-template>

<ng-template #UserNotFound>
  <p>User Not Found</p>
</ng-template>

<p-dialog
  dismissableMask="true"
  backdrop="false"
  position="top-right"
  [(visible)]="showFilterListDialog"
  [modal]="true"
  class="filter-dialog"
  [draggable]="false"
  [resizable]="false"
>
  <ng-template pTemplate>
    <div class="filter-listing" *ngIf="showSavedFilter">
      <input type="search" class="form-control mb-2" placeholder="Search" (input)="searchFilters($event.target.value)" />
      <div *hasAnyPermission="permissionModules.VIEW_SHARE_FILTER; hideTemplate: true" class="title">Shared Filters</div>
      <ng-container *hasAnyPermission="permissionModules.VIEW_SHARE_FILTER; hideTemplate: true">
        <span *ngIf="sharedFilters?.length; else noData">
          <div
            class="form-check filter-body"
            *ngFor="let filterOption of sharedFilters"
            [ngClass]="{
              'selected-filter': filterOption?.query_filter?.id === selectedFilterFormControl?.value?.query_filter?.id
            }"
          >
            <label class="form-check-label">
              <input
                [formControl]="selectedFilterFormControl"
                (ngModelChange)="applySelectedFilterAndUpdateUrl()"
                type="radio"
                class="form-check-input custom-radio"
                [value]="filterOption"
                name="filteroption"
              />{{ filterOption?.query_filter?.name }}
            </label>
            <div class="filter-icons">
              <ng-container *hasAnyPermission="permissionModules.MANAGE_SHARE_FILTER; hideTemplate: true">
                <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary copy-icon" (click)="copyLinkToTheFilter(filterOption?.query_filter?.id)">
                  <em title="Copy Filter Link" class="fa-regular fa-copy"></em>
                </a>
                <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary share-icon" (click)="unShareFilter(filterOption)">
                  <em class="fa-solid fa-share"></em>
                </a>
                <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary">
                  <span title="Edit" [inlineSVG]="'assets/media/svg/icons/edit.svg'" cacheSVG="true" class="svg-icon svg-icon-md" (click)="editFilter(filterOption)"> </span>
                </a>
                <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary">
                  <span title="Delete" [inlineSVG]="'assets/media/svg/icons/delete.svg'" cacheSVG="true" class="svg-icon svg-icon-md" (click)="deleteFilter(filterOption)"> </span>
                </a>
              </ng-container>
            </div>
          </div>
        </span>
      </ng-container>

      <ng-template #noData>
        <div>-</div>
      </ng-template>
      <div class="title">My Filters</div>
      <span *ngIf="myFilters?.length; else noData">
        <div
          class="form-check filter-body"
          *ngFor="let filterOption of myFilters"
          [ngClass]="{
            'selected-filter': filterOption?.query_filter?.id === selectedFilterFormControl?.value?.query_filter?.id
          }"
        >
          <label class="form-check-label">
            <input
              [formControl]="selectedFilterFormControl"
              (ngModelChange)="applySelectedFilterAndUpdateUrl()"
              type="radio"
              class="form-check-input custom-radio"
              [value]="filterOption"
              name="filteroption"
            />{{ filterOption?.query_filter?.name }}
          </label>
          <div class="filter-icons">
            <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary copy-icon" (click)="copyLinkToTheFilter(filterOption?.query_filter?.id)">
              <em title="Copy Filter Link" class="fa-regular fa-copy"></em>
            </a>
            <a
              class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary"
              (click)="shareFilter(filterOption)"
              *hasAnyPermission="permissionModules.MANAGE_SHARE_FILTER; hideTemplate: true"
            >
              <em class="fa-solid fa-share"></em>
            </a>
            <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary">
              <span title="Edit" [inlineSVG]="'assets/media/svg/icons/edit.svg'" cacheSVG="true" class="svg-icon svg-icon-md" (click)="editFilter(filterOption)"> </span>
            </a>
            <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary">
              <span title="Delete" [inlineSVG]="'assets/media/svg/icons/delete.svg'" cacheSVG="true" class="svg-icon svg-icon-md" (click)="deleteFilter(filterOption)"> </span>
            </a>
          </div>
        </div>
      </span>
    </div>
  </ng-template>
</p-dialog>

<p-dialog header="Delete" [(visible)]="showDeleteDialog" [modal]="true" class="confirm-dialog" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
  <h5 class="p-m-0 delete-title">Do you want to delete "{{ deleteFilterObj?.query_filter?.name }}" Filter?</h5>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center">
      <button type="button" class="btn-cancel" (click)="closeModal()">No</button>
      <button type="button" class="btn-save" (click)="saveDeleteFilter()" [isSubmitting]="isSubmitting">Yes</button>
    </div>
  </ng-template>
</p-dialog>

<p-dialog [header]="shareFilterObj?.header" [(visible)]="showShareDialog" [modal]="true" class="confirm-dialog" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
  <h5 class="p-m-0 delete-title">{{ shareFilterObj?.text }}</h5>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center">
      <button type="button" class="btn-cancel" (click)="closeModal()">No</button>
      <button type="button" class="btn-save" (click)="saveShareFilter()" [isSubmitting]="isSubmitting">Yes</button>
    </div>
  </ng-template>
</p-dialog>

<p-dialog header="Rename" [(visible)]="showEditDialog" [modal]="true" class="confirm-dialog" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
  <div class="form-group first" *ngIf="editFilterObj">
    <input type="text" pInputText class="form-control custom" [(ngModel)]="editFilterObj.query_filter.name" (input)="inputFilterName()" />
    <small *ngIf="showNameError" class="form-text text-danger"> Name is required</small>
  </div>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center">
      <button type="button" class="btn-cancel" (click)="closeModal()">Cancel</button>
      <button type="button" class="btn-save" (click)="saveEditFilter()" [isSubmitting]="isSubmitting">Update</button>
    </div>
  </ng-template>
</p-dialog>

<p-confirmDialog key="conformationDialog" [position]="'top'" rejectButtonStyleClass="p-button-outlined"></p-confirmDialog>

<p-dialog
  header="Position not In Range"
  [(visible)]="positionNOTInRange"
  [modal]="true"
  class="position-Dialog"
  [style]="{ width: '50vw' }"
  [draggable]="false"
  [resizable]="false"
  [baseZIndex]="10000"
  [showHeader]="true"
>
  <p class="red-text ml-2 f-13 pb-3 mt-2">
    Position not in effect on this day.” if you try to enter something before or after the position dates. Contact your admin to get the dates adjusted of needed.
  </p>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center">
      <button type="button" class="btn-cancel" (click)="closePositionModal()">OK</button>
    </div>
  </ng-template>
</p-dialog>

<p-dialog
  dismissableMask="true"
  backdrop="false"
  position="top-right"
  [(visible)]="showExportOptionDialog"
  [modal]="true"
  class="export-dialog"
  [draggable]="false"
  [resizable]="false"
>
  <ng-template pTemplate>
    <div class="export-action-listing" *ngIf="showExportOptions">
      <button pButton class="btn p-button-text mb-2 mt-2" icon="pi pi-file-o" iconPos="left" (click)="exportReport('csv')">Export CSV</button>
      <button pButton class="btn p-button-text mb-2" icon="pi pi-file-pdf" iconPos="left" (click)="exportReport('pdf')">Export PDF</button>
      <button pButton class="btn p-button-text mb-2" icon="pi pi-file-excel" iconPos="left" (click)="exportReport('excel')">Export Excel</button>
    </div>
  </ng-template>
</p-dialog>

<p-overlayPanel styleClass="comment-overlay position-absolute" #op>
  <ng-template pTemplate>
    <div class="d-flex justify-content-between">
      <label for="add comment" class="form-label">Add comment</label>
      <em class="pi pi-times pointer" (click)="cancelComment()"></em>
    </div>
    <hr />
    <textarea
      [disabled]="isDisableComment"
      class="form-control custom add-comment-input"
      rows="3"
      cols="9"
      pInputTextarea
      [(ngModel)]="addCommentText"
      placeholder="add comment"
    ></textarea>
    <div class="d-flex justify-content-end mt-2">
      <button type="button" class="btn-cancel mr-1" (click)="cancelComment()">Cancel</button>
      <button type="button" class="btn-save" [disabled]="!addCommentText || isDisableComment" (click)="saveComment()">Save</button>
    </div>
  </ng-template>
</p-overlayPanel>

<p-dialog
  [(visible)]="unableToDownloadPdf"
  header="Unable To Download PDF"
  [style]="{ width: '400px' }"
  class="confirm-dialog-expense"
  [modal]="true"
  [draggable]="false"
  [resizable]="false"
>
  <h5 class="p-m-0">Export to PDF is limited to a specific page width. Please reduce the number of columns or select a different export format option.</h5>
  <ng-template pTemplate="footer">
    <div class="d-flex justify-content-end align-items-center">
      <button type="button" class="btn-save" (click)="closePdfModel()" [isSubmitting]="isSubmitting">Ok</button>
    </div>
  </ng-template>
</p-dialog>

<p-dialog
  *ngIf="selectedEmployeeIds?.value"
  [(visible)]="bookTimeOffPopUp"
  header="Book Time Off"
  [style]="{ width: '40vw' }"
  [modal]="true"
  [draggable]="false"
  [resizable]="false"
  (onHide)="closeBookTimeOffModel()"
>
  <app-book-time-off class="my-5" #bookTimeOFF [employee_id]="selectedEmployeeIds?.value" (closeModelSignal)="closeBookTimeOffModel(true)"></app-book-time-off>

  <ng-template pTemplate="footer">
    <div class="d-flex justify-content-end align-items-center">
      <button type="button" class="btn-outline-primary btn mx-2" (click)="closeBookTimeOffModel()">Cancel</button>
      <button [disabled]="bookTimeOff?.createWorkExceptionForm?.invalid" type="button" class="btn-save" (click)="saveBookTimeOffAPI()">Book Now</button>
    </div>
  </ng-template>
</p-dialog>

<p-dialog
  [header]="isApproved ? 'Approval' : 'Reject'"
  [(visible)]="showApprovedDialog"
  [modal]="true"
  class="confirm-dialog-approved"
  [baseZIndex]="10000"
  [draggable]="false"
  [resizable]="false"
  (onHide)="closeApprovePopup()"
>
  <div class="d-flex flex-column">
    <div class="font-weight-bold py-2 line-hight-dialgo text-center" *ngIf="isApproved">
      <div>
        <img [src]="imageConst.approveCalender" alt="approved" />
      </div>
      <div class="approved">Approve Hours</div>
      <div class="approved">Approved hours will be sent to payroll.</div>
    </div>
    <div class="font-weight-bold py-2 line-hight-dialgo text-center" *ngIf="!isApproved">
      <div>
        <img [src]="imageConst.rejectCalender" alt="rejectCalender" />
      </div>
      <div class="approved">Reject Hours</div>
    </div>
  </div>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center">
      <button type="button" class="btn-cancel" (click)="closeApprovePopup()">Cancel</button>
      <button *ngIf="isApproved" type="button" class="btn-save" (click)="updateHourSApproved()" [isSubmitting]="isSubmitting">Approve</button>
      <button *ngIf="!isApproved" type="button" class="btn-save" (click)="updateHourSApproved()" [isSubmitting]="isSubmitting">Reject</button>
    </div>
  </ng-template>
</p-dialog>

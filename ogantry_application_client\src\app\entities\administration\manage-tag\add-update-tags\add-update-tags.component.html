<div class="card card-custom gutter-b" id="create_role_form">
  <app-card-header [cardTitle]="cardTitle" [cardSubTitle]="cardSubTitle" [buttons]="buttons"></app-card-header>
  <div class="card-body create-card">
    <form class="form" autocomplete="off" [formGroup]="createTagForm" autocomplete="off" novalidate="novalidate" (ngSubmit)="createTagForm.valid && onSave()">
      <div id="client-detail">
        <kt-auth-notice></kt-auth-notice>
        <div class="mx-auto content-center">
          <div class="row">
            <div class="col-sm-3 col-12 pr-md-5">
              <div class="form-group dropdown">
                <label class="form-label mr-3">Select Tag Category</label>
                <p-dropdown
                  class="form-control custom"
                  appendTo="body"
                  [options]="categoryMasterData"
                  (onChange)="updatedSubCategoryList()"
                  placeholder="Select Category"
                  formControlName="tagCategory"
                  optionLabel="name"
                  optionValue="id"
                ></p-dropdown>
                <app-form-error [validation]="'required'" [form]="createTagForm" [controlName]="'tagCategory'" [fieldLabel]="'Tag Category'"></app-form-error>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-sm-3 col-12 pr-md-5">
              <div class="form-group dropdown">
                <label class="form-label mr-3">Select Tag Sub Category</label>
                <p-dropdown
                  class="form-control custom"
                  appendTo="body"
                  emptyMessage="Please select category to see sub category in it"
                  [options]="subCategories"
                  placeholder="Select Sub Category"
                  formControlName="subTagCategory"
                  optionLabel="name"
                  optionValue="id"
                ></p-dropdown>
                <app-form-error [validation]="'required'" [form]="createTagForm" [controlName]="'subTagCategory'" [fieldLabel]="'Tag Category'"></app-form-error>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-sm-3 col-12 pr-md-5">
              <div class="form-group">
                <label class="form-label">Tag Name</label>
                <input type="text" class="form-control custom" placeholder="e.g. Profit-2022" formControlName="name" />
                <app-form-error [validation]="'required'" [form]="createTagForm" [controlName]="'name'" [fieldLabel]="'Tag Name'"></app-form-error>
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>

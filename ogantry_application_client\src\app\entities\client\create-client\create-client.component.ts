import { Component, Input, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { MatSidenav } from '@angular/material/sidenav';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { ButtonParams, OGantryHttpResponse, CustomerContact } from '@shared/models';
import { SidebarParams } from '@shared/models/sidebar-params.model';
import { ClientService } from '../client.service';
import { Client, Contact } from '../client.model';
import { AuthNoticeService } from '@auth/auth-notice/auth-notice.service';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { ContactPersonService } from '@shared/components/contact-person/contact-person.service';
import { LayoutUtilsService } from '@shared/services/layout-utils.service';
import { AlertType } from '@shared/models/alert-type.enum';
import { KtDialogService } from '@shared/services';
import { AdministrationService } from '@entities/administration/administration.service';
import { ComponentsType } from '@shared/models/component-type-enum';
import { ExtendedFormComponent } from '@shared/components/extended-form/extended-form.component';
import { AppConstants } from '@shared/constants';
import { PurchaseOrderService } from '@shared/services/purchase-order.service';
import { PurchaseOrderList, PurchaseOrderWrapper } from '@shared/models/custom/purchase-order.model';
interface CustomerContactResponse {
  customer_contact: CustomerContact;
}
@Component({
  selector: 'app-create-client',
  templateUrl: './create-client.component.html',
  styleUrls: ['./create-client.component.scss'],
  providers: [PurchaseOrderService]
})
export class CreateClientComponent extends SflBaseComponent implements OnInit, OnDestroy {
  cardTitle = 'Create Client';
  isEdit: boolean;
  listOFPurchaseOrder: PurchaseOrderList | Array<any> = [];
  selectedPurchaseOrder: PurchaseOrderWrapper | undefined;
  cardSubTitle = null;
  createClientForm: FormGroup;
  isAddContactVisible = false;
  selectedTemplate: MatSidenav;
  sidebarParams: SidebarParams<CustomerContactResponse>;
  client: Client;
  clientId: number;
  saveBtnText = 'Save';
  buttons: ButtonParams[] = [];
  selectedContact: Contact;
  showDeleteDialog: boolean;
  deleteContactId: number;
  tags: string[];
  openFilter = false;
  globalDetailId: number = 0;
  extendFieldsObj: any = {};
  componentType = ComponentsType;
  @ViewChild('extendFrom') extendFiledComponent: ExtendedFormComponent;
  purchaseOrderVisible = false;

  constructor(
    private readonly clientService: ClientService,
    private readonly authNoticeService: AuthNoticeService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly contactPersonService: ContactPersonService,
    private readonly layoutUtilsService: LayoutUtilsService,
    private readonly ktDialogService: KtDialogService,
    private readonly purchaseOrderService: PurchaseOrderService,
    private router: Router
  ) {
    super();
  }

  ngOnInit(): void {
    this.loading$.next(null);
    this.getParams();
    this.initForm();
  }

  getParams() {
    this.clientId = this.activatedRoute?.snapshot?.params?.clientId;

    if (this.clientId) {
      this.cardTitle = 'Edit Client';
      this.getClient();
      this.listPurchaseOrder(this.clientId);
    }
    this.setBtnParams();
  }

  setBtnParams() {
    this.buttons = [
      {
        btnClass: 'btn btn-cancel',
        btnText: 'Cancel',
        redirectPath: this.appRoutes.MANAGE_CLIENT
      },
      {
        btnClass: 'btn-save',
        btnText: 'Save',
        btnType: 'submit',
        action: this.clientId ? this.onEdit.bind(this) : this.onSave.bind(this),
        loading: this.isSubmitting
      }
    ];
  }

  getClient() {
    this.loading$.next(true);
    this.subscriptionManager.add(
      this.clientService.getClient(this.clientId).subscribe(
        (res) => {
          this.client = res.data;
          this.extendFieldsObj = res.data.customer.extended_fields;
          this.tags = this.client.customer.tags;
          this.setClientForm();
          this.loading$.next(false);
        },
        () => this.loading$.next(false)
      )
    );
  }

  initForm() {
    this.createClientForm = new FormGroup({
      name: new FormControl('', Validators.required),
      is_active: new FormControl(true),
      comment: new FormControl('')
    });
  }

  setClientForm() {
    const client = this.client.customer;
    this.createClientForm.controls['name'].setValue(client.name);
    this.createClientForm.controls['comment'].setValue(client.comment);
    this.createClientForm.controls['is_active'].setValue(client.is_active);
  }

  async onSave() {
    this.checkFormForValidation(this.createClientForm);
    this.isSubmitting = true;
    // check if the client is already exist with the given name
    if (await this.isClientWithSameNameExists()) {
      this.authNoticeService.setNotice('A client with the same name already exists.', 'danger');
      this.isSubmitting = false;
      return;
    }
    const clientSetupForm = JSON.parse(JSON.stringify(this.createClientForm.value));

    let finalClientObj;
    if (this.extendFiledComponent?.extendFieldsObj) {
      finalClientObj = { ...clientSetupForm };
      finalClientObj['extended_fields'] = this.extendFiledComponent?.extendFieldsObj;
    } else {
      finalClientObj = {
        ...clientSetupForm
      };
    }

    this.subscriptionManager.add(
      this.clientService.createClient(finalClientObj).subscribe(
        (res) => {
          this.onSuccess(res.data, 'Client created successfully');
        },
        (err) => this.onError(err)
      )
    );
  }

  isClientWithSameNameExists(): Promise<boolean> {
    const queryString = {
      name: this.createClientForm.controls['name'].value
    };
    return new Promise((resolve) => {
      this.subscriptionManager.add(
        this.clientService.getClientByName(queryString).subscribe((res) => {
          resolve(res?.body?.data?.customers?.length > 0);
        })
      );
    });
  }

  openSidebar(sidebarParams: SidebarParams<CustomerContactResponse>, contactPerson?: Contact, isEdit = false): void {
    this.client.isEdit = isEdit;
    this.selectedContact = contactPerson;
    this.isAddContactVisible = true;
    this.sidebarParams = sidebarParams;
    // this.sidebarParams.template.toggle();
    this.openFilter = true;
  }

  openPurchaseOrderSidebar(sidebarParams: SidebarParams<CustomerContactResponse>, purchaseOrder?: PurchaseOrderWrapper | undefined, isEdit = false): void {
    this.isAddContactVisible = false;
    this.sidebarParams = sidebarParams;
    this.purchaseOrderVisible = true;
    this.openFilter = true;
    if (isEdit) {
      this.selectedPurchaseOrder = purchaseOrder;
      this.isEdit = isEdit;
    }
    // this.client.isEdit = isEdit;
    // this.selectedContact = contactPerson;
    // this.isAddContactVisible = true;
    // this.sidebarParams = sidebarParams;
    // this.sidebarParams.template.toggle();
    // this.openFilter = true;
  }

  sidebarClosed(isClosed: boolean) {
    if (isClosed) {
      this.isAddContactVisible = false;
      this.purchaseOrderVisible = false;
      // this.sidebarParams.template.toggle();
      this.openFilter = false;
      this.selectedPurchaseOrder = undefined;
      this.isEdit = false;
    }
  }

  onEdit() {
    this.checkFormForValidation(this.createClientForm);
    const clientSetupForm = JSON.parse(JSON.stringify(this.createClientForm.value));

    let finalClientObj;
    if (this.extendFiledComponent?.extendFieldsObj) {
      finalClientObj = { ...clientSetupForm };
      finalClientObj['extended_fields'] = this.extendFiledComponent?.extendFieldsObj;
    } else {
      finalClientObj = {
        ...clientSetupForm
      };
    }

    this.isSubmitting = true;
    this.subscriptionManager.add(
      this.clientService.updateClient(this.clientId, finalClientObj).subscribe(
        (res) => {
          this.onSuccess(res.data, AppConstants.updateClient);
        },
        (err) => this.onError(err)
      )
    );
  }

  onSuccess(client: Client, successMsg: string) {
    this.router.navigate(['/client/create', client.customer.id]);
    this.isSubmitting = false;
    this.client = client;
    this.clientId = this.client.customer.id;
    this.setBtnParams();
    setTimeout(() => {
      this.authNoticeService.setNotice(successMsg, 'success');
    }, 0);
  }

  onError(err) {
    this.isSubmitting = false;
    const error: OGantryHttpResponse<Client> = err.error;
    this.authNoticeService.setNotice(error.errors, 'danger');
  }

  // update the contact list with the updated contact
  updateClientList(sidebarParams: SidebarParams<CustomerContactResponse>) {
    this.getClient();
  }

  ngOnDestroy() {
    this.authNoticeService.setNotice(null);
    this.ktDialogService.hide();
  }

  showModal(contactPerson) {
    this.showDeleteDialog = true;
    this.deleteContactId = contactPerson.contact.id;
  }

  deleteContact() {
    this.isSubmitting = true;
    this.subscriptionManager.add(
      this.contactPersonService.deleteContactPerson(this.deleteContactId).subscribe(
        () => {
          this.isSubmitting = false;
          this.closeModal();
          this.layoutUtilsService.showActionNotification('Contact has been deleted successsfully', AlertType.Success);
          this.getClient();
        },
        () => (this.isSubmitting = true)
      )
    );
  }

  closeModal() {
    this.deleteContactId = null;
    this.showDeleteDialog = false;
  }

  listPurchaseOrder(customer_id: number): void {
    this.loading$.next(true);
    this.subscriptionManager.add(
      this.purchaseOrderService.purchaseOrderListBYCustomer(customer_id).subscribe({
        next: (res) => {
          this.listOFPurchaseOrder = res?.data.purchase_orders;
          this.loading$.next(false);
        },
        error: () => {
          this.loading$.next(false);
        }
      })
    );
  }
}

/**
 * TODO Prashant
 * Check responsiveness of this page.
 * There is an issue in showing loader on Save button.
 * I'm using directive here to do that. It works in sidebar component but doesn't work here.
 * I doubt this is because I'm using buttonParam approach here.
 * card header is a custom component which accepts different types of buttons.
 * Check that approach once.
 */

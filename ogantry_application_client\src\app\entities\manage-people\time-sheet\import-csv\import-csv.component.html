<div #wizard class="wizard wizard-4" id="kt_wizard_v4" data-wizard-state="step-first">
  <!--begin: Form Wizard Nav -->
  <div class="wizard-nav">
    <div class="wizard-steps align-items-center justify-content-around">
      <a class="wizard-step" data-wizard-type="step" data-wizard-clickable="true">
        <div class="wizard-wrapper flex-nowrap" (click)="goToNextStep('1')">
          <div class="wizard-number">1</div>
          <div class="wizard-label">
            <div class="wizard-title">Match Data/Repair Data</div>
            <div class="wizard-desc">You can update data according to our system</div>
          </div>
        </div>
      </a>
      <div class="wizard-line-wrapper"></div>
      <a class="wizard-step" data-wizard-type="step" data-wizard-clickable="true">
        <div class="wizard-wrapper flex-nowrap" (click)="goToNextStep('2')">
          <div class="wizard-number">2</div>
          <div class="wizard-label">
            <div class="wizard-title">Review/Complete</div>
            <div class="wizard-desc">Review and Save</div>
          </div>
        </div>
      </a>
      <div class="wizard-buttons d-flex justify-content-around">
        <button type="button" class="btn-save" (click)="saveTimeSheetData()" *ngIf="activeTab >= 2">Apply Actuals</button>
      </div>
    </div>
  </div>
  <div class="card card-custom card-shadowless rounded-top-0 cal-height">
    <div class="card-body p-0">
      <div class="row justify-content-center full-height">
        <div class="col-xl-12">
          <!--begin: Form Wizard Form-->
          <form class="form mt-0" id="kt-form">
            <div data-wizard-type="step-content">
              <div class="pt-1">
                <app-repair-csv-data
                  [tableHeader]="selectedHeaderAlongWithOGFields"
                  [updatedCSVData]="CSVData"
                  [selectedColumns]="columnMapping"
                  [OGClientList]="OGClientList"
                  [OGProjectList]="OGProjectList"
                  [OGPositionList]="OGPositionList"
                  [isAllDataMatched]="isDataMatched"
                  (isDataIsChanged)="checkIsAllDataISVlidated($event)"
                  [formMapping]="formMapping"
                ></app-repair-csv-data>
              </div>
            </div>
            <div data-wizard-type="step-content">
              <div class="pt-1">
                <app-review-csv-data
                  [tableHeader]="selectedHeaderAlongWithOGFields"
                  [updatedCSVData]="CSVData"
                  [uploadedCSVFile]="uploadedCSVFile"
                  [selectedColumns]="columnMapping"
                  [timeEntries]="timeEntriesGlobalDetails"
                ></app-review-csv-data>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

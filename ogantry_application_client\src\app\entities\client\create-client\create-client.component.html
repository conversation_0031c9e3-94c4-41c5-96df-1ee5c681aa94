<span class="createClientWrapper">
  <mat-drawer-container class="h-100" id="benchReportContainer">
    <mat-drawer class="detail-sidebar w-50" #sidebarEdit mode="over" position="end" disableClose [opened]="openFilter">
      <app-add-contact
        *ngIf="isAddContactVisible"
        (closeSidebarEvent)="sidebarClosed($event)"
        [sidebarParams]="sidebarParams"
        [client]="client"
        [selectedContact]="selectedContact"
        (updateTheClientList)="updateClientList($event)"
      ></app-add-contact>
      <app-add-edit-purchase-order
        [isEdit]="isEdit"
        *ngIf="purchaseOrderVisible"
        [clientId]="clientId"
        [purchaseOrder]="selectedPurchaseOrder"
        (closeSidebarEvent)="sidebarClosed($event)"
        (updatePurchaseOrderList)="listPurchaseOrder(this.clientId)"
      >
      </app-add-edit-purchase-order>
    </mat-drawer>
    <mat-drawer-content class="detail-sidebar-content">
      <ng-container *ngTemplateOutlet="createClientBody"></ng-container>
    </mat-drawer-content>
  </mat-drawer-container>
</span>
<ng-template #createClientBody>
  <div class="card card-custom gutter-b" id="createClient">
    <app-card-header [cardTitle]="cardTitle" [buttons]="buttons"></app-card-header>
    <div class="card-body create-card" *isFetchingData="loading$">
      <form class="form" [formGroup]="createClientForm" autocomplete="off" novalidate="novalidate" id="create_client_form" (ngSubmit)="createClientForm.valid && onSave()">
        <div id="client-detail">
          <kt-auth-notice></kt-auth-notice>
          <div class="row">
            <!-- Company Details begins -->
            <div class="col-lg-6 col-12 pr-md-5">
              <div class="form-group first">
                <label class="form-label">Name</label>
                <input type="email" class="form-control custom" required placeholder="Client Name" formControlName="name" />
                <app-form-error [validation]="'required'" [form]="createClientForm" [controlName]="'name'" [fieldLabel]="'Company Name'"></app-form-error>
              </div>
              <div class="form-group">
                <label class="form-label">Status</label>
                <span class="status-toggle">Inactive <mat-slide-toggle class="mx-3" color="warn" formControlName="is_active"></mat-slide-toggle>Active</span>
              </div>
              <div class="form-group">
                <label class="form-label">Comments</label>
                <textarea class="form-control custom" placeholder="Type comment here" rows="5" formControlName="comment"></textarea>
              </div>

              <div class="col-12 pl-md-5" *ngIf="clientId">
                <ng-container *ngTemplateOutlet="purchaseOrder"></ng-container>
              </div>

              <div class="tags">
                <ng-container *ngTemplateOutlet="clientTags"></ng-container>
              </div>
            </div>
            <div class="col-lg-6 col-12 pr-md-5">
              <app-extended-form #extendFrom [projectId]="clientId" [componentType]="componentType.Client" [extendFieldsObj]="extendFieldsObj"></app-extended-form>
            </div>
            <!-- Company Details ends -->

            <!-- Contact person begins -->
            <div class="col-lg-6 col-12 pl-md-5" *ngIf="clientId">
              <ng-container *ngTemplateOutlet="contactPersonDetails"></ng-container>
            </div>
            <!-- Contact person ends -->
          </div>
        </div>
      </form>
    </div>
  </div>
</ng-template>

<ng-template #clientTags>
  <div *ngIf="clientId">
    <hr />
    <app-tag-listing-view [_employeeId]="clientId" [_tags]="tags" [_flag]="'CLIENT_TAG'" (_reloadTags)="getClient()"></app-tag-listing-view>
  </div>
</ng-template>

<ng-template #companyDetails>
  <div class="form-group">
    <label class="form-label">Company Name</label>
    <input type="email" class="form-control custom" required placeholder="Company Name" formControlName="companyName" />
    <small *ngIf="isControlHasError(createClientForm, 'companyName', 'required')" class="form-text text-danger"> Company Name is required </small>
  </div>
</ng-template>

<ng-template #contactPersonDetails>
  <div class="form-group">
    <label class="form-label">Contacts</label>
    <a href="javascript:;void" class="add-new" (click)="openSidebar({ template: sidebarEdit })"><strong>+ Add New</strong></a>
    <app-contact-person
      [contactPersons]="client?.customer?.contacts"
      (showDeleteModal)="showModal($event)"
      (updateContactSideNav)="openSidebar({ template: sidebarEdit }, $event, true)"
    >
    </app-contact-person>
  </div>
</ng-template>

<ng-template #purchaseOrder>
  <div class="form-group">
    <label class="form-label">Purchase Order</label>
    <a href="javascript:;void" class="add-new" (click)="openPurchaseOrderSidebar({ template: sidebarEdit })"><strong>+ Add New</strong></a>

    <app-list-purchase-order
      [listOFPurchaseOrder]="listOFPurchaseOrder"
      (purchaseOrder)="openPurchaseOrderSidebar({ template: sidebarEdit }, $event, true)"
      (updatePurchaseOrderList)="listPurchaseOrder(this.clientId)"
    ></app-list-purchase-order>
  </div>
</ng-template>

<p-dialog header="Delete Project" [(visible)]="showDeleteDialog" [modal]="true" class="confirm-dialog" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
  <h5 class="p-m-0">Are you sure you want to delete this Contact?</h5>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center">
      <button type="button" class="btn-cancel" (click)="closeModal()">No</button>
      <button type="button" class="btn-save" (click)="deleteContact()" [isSubmitting]="isSubmitting">Yes</button>
    </div>
  </ng-template>
</p-dialog>

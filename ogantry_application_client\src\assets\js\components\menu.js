"use strict";
var KTMenu = function (e, t) {
  var n = this,
    o = !1,
    i = KTUtil.getById(e),
    u = KTUtil.getBody();
  if (i) {
    var r = {
        scroll: { rememberPosition: !1 },
        accordion: {
          slideSpeed: 200,
          autoScroll: !1,
          autoScrollSpeed: 1200,
          expandAll: !0,
        },
        dropdown: { timeout: 500 },
      },
      l = {
        construct: function (e) {
          return (
            KTUtil.data(i).has("menu")
              ? (n = KTUtil.data(i).get("menu"))
              : (l.init(e),
                l.reset(),
                l.build(),
                KTUtil.data(i).set("menu", n)),
            n
          );
        },
        init: function (e) {
          (n.events = []),
            (n.eventHandlers = {}),
            (n.options = KTUtil.deepExtend({}, r, e)),
            (n.pauseDropdownHoverTime = 0),
            (n.uid = KTUtil.getUniqueID());
        },
        update: function (e) {
          (n.options = KTUtil.deepExtend({}, r, e)),
            (n.pauseDropdownHoverTime = 0),
            l.reset(),
            (n.eventHandlers = {}),
            l.build(),
            KTUtil.data(i).set("menu", n);
        },
        reload: function () {
          l.reset(), l.build(), l.resetSubmenuProps();
        },
        build: function () {
          (n.eventHandlers.event_1 = KTUtil.on(
            i,
            ".menu-toggle",
            "click",
            l.handleSubmenuAccordion
          )),
            ("dropdown" === l.getSubmenuMode() ||
              l.isConditionalSubmenuDropdown()) &&
              ((n.eventHandlers.event_2 = KTUtil.on(
                i,
                '[data-menu-toggle="hover"]',
                "mouseover",
                l.handleSubmenuDrodownHoverEnter
              )),
              (n.eventHandlers.event_3 = KTUtil.on(
                i,
                '[data-menu-toggle="hover"]',
                "mouseout",
                l.handleSubmenuDrodownHoverExit
              )),
              (n.eventHandlers.event_4 = KTUtil.on(
                i,
                '[data-menu-toggle="click"] > .menu-toggle, [data-menu-toggle="click"] > .menu-link .menu-toggle',
                "click",
                l.handleSubmenuDropdownClick
              )),
              (n.eventHandlers.event_5 = KTUtil.on(
                i,
                '[data-menu-toggle="tab"] > .menu-toggle, [data-menu-toggle="tab"] > .menu-link .menu-toggle',
                "click",
                l.handleSubmenuDropdownTabClick
              ))),
            (n.eventHandlers.event_6 = KTUtil.on(
              i,
              ".menu-item > .menu-link:not(.menu-toggle):not(.menu-link-toggle-skip)",
              "click",
              l.handleLinkClick
            )),
            n.options.scroll && n.options.scroll.height && l.scrollInit();
        },
        reset: function () {
          KTUtil.off(i, "click", n.eventHandlers.event_1),
            KTUtil.off(i, "mouseover", n.eventHandlers.event_2),
            KTUtil.off(i, "mouseout", n.eventHandlers.event_3),
            KTUtil.off(i, "click", n.eventHandlers.event_4),
            KTUtil.off(i, "click", n.eventHandlers.event_5),
            KTUtil.off(i, "click", n.eventHandlers.event_6);
        },
        scrollInit: function () {
          n.options.scroll && n.options.scroll.height
            ? (KTUtil.scrollDestroy(i, !0),
              KTUtil.scrollInit(i, {
                mobileNativeScroll: !0,
                windowScroll: !1,
                resetHeightOnDestroy: !0,
                handleWindowResize: !0,
                height: n.options.scroll.height,
                rememberPosition: n.options.scroll.rememberPosition,
              }))
            : KTUtil.scrollDestroy(i, !0);
        },
        scrollUpdate: function () {
          n.options.scroll && n.options.scroll.height && KTUtil.scrollUpdate(i);
        },
        scrollTop: function () {
          n.options.scroll && n.options.scroll.height && KTUtil.scrollTop(i);
        },
        getSubmenuMode: function (e) {
          return KTUtil.isBreakpointUp("lg")
            ? e &&
              KTUtil.hasAttr(e, "data-menu-toggle") &&
              "hover" == KTUtil.attr(e, "data-menu-toggle")
              ? "dropdown"
              : KTUtil.isset(n.options.submenu, "desktop.state.body")
              ? KTUtil.hasClasses(u, n.options.submenu.desktop.state.body)
                ? n.options.submenu.desktop.state.mode
                : n.options.submenu.desktop.default
              : KTUtil.isset(n.options.submenu, "desktop")
              ? n.options.submenu.desktop
              : void 0
            : KTUtil.isBreakpointUp("md") &&
              KTUtil.isBreakpointDown("lg") &&
              KTUtil.isset(n.options.submenu, "tablet")
            ? n.options.submenu.tablet
            : !(
                !KTUtil.isBreakpointDown("md") ||
                !KTUtil.isset(n.options.submenu, "mobile")
              ) && n.options.submenu.mobile;
        },
        isConditionalSubmenuDropdown: function () {
          return !(
            !KTUtil.isBreakpointUp("lg") ||
            !KTUtil.isset(n.options.submenu, "desktop.state.body")
          );
        },
        resetSubmenuProps: function (e) {
          var t = KTUtil.findAll(i, ".menu-submenu");
          if (t)
            for (var n = 0, o = t.length; n < o; n++) {
              var u = t[0];
              KTUtil.css(u, "display", ""),
                KTUtil.css(u, "overflow", ""),
                u.hasAttribute("data-hor-direction") &&
                  (KTUtil.removeClass(u, "menu-submenu-left"),
                  KTUtil.removeClass(u, "menu-submenu-right"),
                  KTUtil.addClass(u, u.getAttribute("data-hor-direction")));
            }
        },
        handleSubmenuDrodownHoverEnter: function (e) {
          if (
            "accordion" !== l.getSubmenuMode(this) &&
            !1 !== n.resumeDropdownHover()
          ) {
            "1" == this.getAttribute("data-hover") &&
              (this.removeAttribute("data-hover"),
              clearTimeout(this.getAttribute("data-timeout")),
              this.removeAttribute("data-timeout")),
              l.showSubmenuDropdown(this);
          }
        },
        handleSubmenuDrodownHoverExit: function (e) {
          if (
            !1 !== n.resumeDropdownHover() &&
            "accordion" !== l.getSubmenuMode(this)
          ) {
            var t = this,
              o = n.options.dropdown.timeout,
              i = setTimeout(function () {
                "1" == t.getAttribute("data-hover") &&
                  l.hideSubmenuDropdown(t, !0);
              }, o);
            t.setAttribute("data-hover", "1"),
              t.setAttribute("data-timeout", i);
          }
        },
        handleSubmenuDropdownClick: function (e) {
          if ("accordion" !== l.getSubmenuMode(this)) {
            var t = this.closest(".menu-item");
            !1 !== l.eventTrigger("submenuToggle", this, e) &&
              "accordion" != t.getAttribute("data-menu-submenu-mode") &&
              (!1 === KTUtil.hasClass(t, "menu-item-hover")
                ? (KTUtil.addClass(t, "menu-item-open-dropdown"),
                  l.showSubmenuDropdown(t))
                : (KTUtil.removeClass(t, "menu-item-open-dropdown"),
                  l.hideSubmenuDropdown(t, !0)),
              e.preventDefault());
          }
        },
        handleSubmenuDropdownTabClick: function (e) {
          if ("accordion" !== l.getSubmenuMode(this)) {
            var t = this.closest(".menu-item");
            !1 !== l.eventTrigger("submenuToggle", this, e) &&
              "accordion" != t.getAttribute("data-menu-submenu-mode") &&
              (0 == KTUtil.hasClass(t, "menu-item-hover") &&
                (KTUtil.addClass(t, "menu-item-open-dropdown"),
                l.showSubmenuDropdown(t)),
              e.preventDefault());
          }
        },
        handleLinkClick: function (e) {
          var t = this.closest(".menu-item.menu-item-submenu");
          !1 !== l.eventTrigger("linkClick", this, e) &&
            t &&
            "dropdown" === l.getSubmenuMode(t) &&
            l.hideSubmenuDropdowns();
        },
        handleSubmenuDropdownClose: function (e, t) {
          if ("accordion" !== l.getSubmenuMode(t)) {
            var n = i.querySelectorAll(
              ".menu-item.menu-item-submenu.menu-item-hover:not(.menu-item-tabs)"
            );
            if (
              n.length > 0 &&
              !1 === KTUtil.hasClass(t, "menu-toggle") &&
              0 === t.querySelectorAll(".menu-toggle").length
            )
              for (var o = 0, u = n.length; o < u; o++)
                l.hideSubmenuDropdown(n[0], !0);
          }
        },
        handleSubmenuAccordion: function (e, t) {
          var o,
            i = t || this;
          if (!1 !== l.eventTrigger("submenuToggle", this, e))
            if (
              "dropdown" === l.getSubmenuMode(t) &&
              (o = i.closest(".menu-item")) &&
              "accordion" != o.getAttribute("data-menu-submenu-mode")
            )
              e.preventDefault();
            else {
              var u = i.closest(".menu-item"),
                r = KTUtil.child(u, ".menu-submenu, .menu-inner");
              if (
                !KTUtil.hasClass(
                  i.closest(".menu-item"),
                  "menu-item-open-always"
                ) &&
                u &&
                r
              ) {
                e.preventDefault();
                var s = n.options.accordion.slideSpeed;
                if (!1 === KTUtil.hasClass(u, "menu-item-open")) {
                  if (!1 === n.options.accordion.expandAll) {
                    var a = i.closest(".menu-nav, .menu-subnav"),
                      m = KTUtil.children(
                        a,
                        ".menu-item.menu-item-open.menu-item-submenu:not(.menu-item-here):not(.menu-item-open-always)"
                      );
                      for (var i = 0; i <= a.children.length; i++) {
                        KTUtil.removeClass(a.children[i], "menu-item-open");
                      }
                    if (a && m)
                      for (var d = 0, c = m.length; d < c; d++) {
                        var p = m[0],
                          v = KTUtil.child(p, ".menu-submenu");
                        v &&
                          KTUtil.slideUp(v, s, function () {
                            l.scrollUpdate(),
                              KTUtil.removeClass(p, "menu-item-open");
                          });
                      }
                  }
                  KTUtil.slideDown(r, s, function () {
                    l.scrollToItem(i),
                      l.scrollUpdate(),
                      l.eventTrigger("submenuToggle", r, e);
                  }),
                    KTUtil.addClass(u, "menu-item-open");
                } else
                  KTUtil.slideUp(r, s, function () {
                    l.scrollToItem(i), l.eventTrigger("submenuToggle", r, e);
                  }),
                    KTUtil.removeClass(u, "menu-item-open");
              }
            }
        },
        scrollToItem: function (e) {
          KTUtil.isBreakpointUp("lg") &&
            n.options.accordion.autoScroll &&
            "1" !== i.getAttribute("data-menu-scroll") &&
            KTUtil.scrollTo(e, n.options.accordion.autoScrollSpeed);
        },
        hideSubmenuDropdown: function (e, t) {
          t &&
            (KTUtil.removeClass(e, "menu-item-hover"),
            KTUtil.removeClass(e, "menu-item-active-tab")),
            e.removeAttribute("data-hover"),
            e.getAttribute("data-menu-toggle-class") &&
              KTUtil.removeClass(u, e.getAttribute("data-menu-toggle-class"));
          var n = e.getAttribute("data-timeout");
          e.removeAttribute("data-timeout"), clearTimeout(n);
        },
        hideSubmenuDropdowns: function () {
          var e;
          if (
            (e = i.querySelectorAll(
              '.menu-item-submenu.menu-item-hover:not(.menu-item-tabs):not([data-menu-toggle="tab"])'
            ))
          )
            for (var t = 0, n = e.length; t < n; t++)
              l.hideSubmenuDropdown(e[t], !0);
        },
        showSubmenuDropdown: function (e) {
          var t = i.querySelectorAll(
            ".menu-item-submenu.menu-item-hover, .menu-item-submenu.menu-item-active-tab"
          );
          if (t)
            for (var n = 0, o = t.length; n < o; n++) {
              var r = t[n];
              e !== r &&
                !1 === r.contains(e) &&
                !1 === e.contains(r) &&
                l.hideSubmenuDropdown(r, !0);
            }
          KTUtil.addClass(e, "menu-item-hover");
          var s = KTUtil.find(e, ".menu-submenu");
          s &&
            !1 === s.hasAttribute("data-hor-direction") &&
            (KTUtil.hasClass(s, "menu-submenu-left")
              ? s.setAttribute("data-hor-direction", "menu-submenu-left")
              : KTUtil.hasClass(s, "menu-submenu-right") &&
                s.setAttribute("data-hor-direction", "menu-submenu-right")),
            s && !0 === KTUtil.isOffscreen(s, "left", 15)
              ? (KTUtil.removeClass(s, "menu-submenu-left"),
                KTUtil.addClass(s, "menu-submenu-right"))
              : s &&
                !0 === KTUtil.isOffscreen(s, "right", 15) &&
                (KTUtil.removeClass(s, "menu-submenu-right"),
                KTUtil.addClass(s, "menu-submenu-left")),
            e.getAttribute("data-menu-toggle-class") &&
              KTUtil.addClass(u, e.getAttribute("data-menu-toggle-class"));
        },
        createSubmenuDropdownClickDropoff: function (e) {
          var t,
            n =
              (t = KTUtil.child(e, ".menu-submenu")
                ? KTUtil.css(t, "z-index")
                : 0) - 1,
            o = document.createElement(
              '<div class="menu-dropoff" style="background: transparent; position: fixed; top: 0; bottom: 0; left: 0; right: 0; z-index: ' +
                n +
                '"></div>'
            );
          u.appendChild(o),
            KTUtil.addEvent(o, "click", function (t) {
              t.stopPropagation(),
                t.preventDefault(),
                KTUtil.remove(this),
                l.hideSubmenuDropdown(e, !0);
            });
        },
        pauseDropdownHover: function (e) {
          var t = new Date();
          n.pauseDropdownHoverTime = t.getTime() + e;
        },
        resumeDropdownHover: function () {
          return new Date().getTime() > n.pauseDropdownHoverTime;
        },
        resetActiveItem: function (e) {
          for (
            var t,
              o,
              u = 0,
              r = (t = i.querySelectorAll(".menu-item-active")).length;
            u < r;
            u++
          ) {
            var l = t[0];
            KTUtil.removeClass(l, "menu-item-active"),
              KTUtil.hide(KTUtil.child(l, ".menu-submenu"));
            for (
              var s = 0,
                a = (o = KTUtil.parents(l, ".menu-item-submenu") || []).length;
              s < a;
              s++
            ) {
              var m = o[u];
              KTUtil.removeClass(m, "menu-item-open"),
                KTUtil.hide(KTUtil.child(m, ".menu-submenu"));
            }
          }
          if (
            !1 === n.options.accordion.expandAll &&
            (t = i.querySelectorAll(".menu-item-open"))
          )
            for (u = 0, r = t.length; u < r; u++)
              KTUtil.removeClass(o[0], "menu-item-open");
        },
        setActiveItem: function (e) {
          l.resetActiveItem();
          for (
            var t = KTUtil.parents(e, ".menu-item-submenu") || [],
              n = 0,
              o = t.length;
            n < o;
            n++
          )
            KTUtil.addClass(t[n], "menu-item-open");
          KTUtil.addClass(e, "menu-item-active");
        },
        getBreadcrumbs: function (e) {
          var t,
            n = [],
            o = KTUtil.child(e, ".menu-link");
          n.push({
            text: (t = KTUtil.child(o, ".menu-text") ? t.innerHTML : ""),
            title: o.getAttribute("title"),
            href: o.getAttribute("href"),
          });
          for (
            var i = KTUtil.parents(e, ".menu-item-submenu"),
              u = 0,
              r = i.length;
            u < r;
            u++
          ) {
            var l = KTUtil.child(i[u], ".menu-link");
            n.push({
              text: (t = KTUtil.child(l, ".menu-text") ? t.innerHTML : ""),
              title: l.getAttribute("title"),
              href: l.getAttribute("href"),
            });
          }
          return n.reverse();
        },
        getPageTitle: function (e) {
          var t;
          return KTUtil.child(e, ".menu-text") ? t.innerHTML : "";
        },
        eventTrigger: function (e, t, o) {
          for (var i = 0; i < n.events.length; i++) {
            var u = n.events[i];
            if (u.name == e) {
              if (1 != u.one) return u.handler.call(this, t, o);
              if (0 == u.fired)
                return (n.events[i].fired = !0), u.handler.call(this, t, o);
            }
          }
        },
        addEvent: function (e, t, o) {
          n.events.push({ name: e, handler: t, one: o, fired: !1 });
        },
        removeEvent: function (e) {
          n.events[e] && delete n.events[e];
        },
      };
    return (
      (n.setDefaults = function (e) {
        r = e;
      }),
      (n.scrollUpdate = function () {
        return l.scrollUpdate();
      }),
      (n.scrollReInit = function () {
        return l.scrollInit();
      }),
      (n.scrollTop = function () {
        return l.scrollTop();
      }),
      (n.setActiveItem = function (e) {
        return l.setActiveItem(e);
      }),
      (n.reload = function () {
        return l.reload();
      }),
      (n.update = function (e) {
        return l.update(e);
      }),
      (n.getBreadcrumbs = function (e) {
        return l.getBreadcrumbs(e);
      }),
      (n.getPageTitle = function (e) {
        return l.getPageTitle(e);
      }),
      (n.getSubmenuMode = function (e) {
        return l.getSubmenuMode(e);
      }),
      (n.hideDropdown = function (e) {
        l.hideSubmenuDropdown(e, !0);
      }),
      (n.hideDropdowns = function () {
        l.hideSubmenuDropdowns();
      }),
      (n.pauseDropdownHover = function (e) {
        l.pauseDropdownHover(e);
      }),
      (n.resumeDropdownHover = function () {
        return l.resumeDropdownHover();
      }),
      (n.on = function (e, t) {
        return l.addEvent(e, t);
      }),
      (n.off = function (e) {
        return l.removeEvent(e);
      }),
      (n.one = function (e, t) {
        return l.addEvent(e, t, !0);
      }),
      l.construct.apply(n, [t]),
      KTUtil.addResizeHandler(function () {
        o && n.reload();
      }),
      (o = !0),
      n
    );
  }
};
"undefined" != typeof module &&
  void 0 !== module.exports &&
  (module.exports = KTMenu),
  document.addEventListener("click", function (e) {
    var t;
    if (
      (t = KTUtil.getByTagName("body")[0].querySelectorAll(
        '.menu-nav .menu-item.menu-item-submenu.menu-item-hover:not(.menu-item-tabs)[data-menu-toggle="click"]'
      ))
    )
      for (var n = 0, o = t.length; n < o; n++) {
        var i = t[n].closest(".menu-nav").parentNode;
        if (i) {
          var u = KTUtil.data(i).get("menu");
          if (!u) break;
          if (!u || "dropdown" !== u.getSubmenuMode()) break;
          e.target !== i && !1 === i.contains(e.target) && u.hideDropdowns();
        }
      }
  });

<div class="card card-custom gutter-b" id="manageTagCategory">
  <app-card-header [cardTitle]="cardTitle" [cardSubTitle]="cardSubTitle" [buttons]="buttons"></app-card-header>
  <div class="card-body">
    <p-table
      responsiveLayout="scroll"
      #dt
      [value]="tagCategory"
      [lazy]="true"
      (onLazyLoad)="getTagCategories()"
      dataKey="id"
      [rows]="10"
      [loading]="loading"
      styleClass="p-datatable-customers"
      [filterDelay]="0"
      [sortField]="'name'"
      [sortOrder]="-1"
    >
      <ng-template pTemplate="header">
        <tr>
          <th id="category" class="header-width">Category</th>
          <th id="actions" class="header-width text-center" colspan="2">Actions</th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-category>
        <tr>
          <td>
            {{ category?.name }}
          </td>
          <td class="text-center pr-0">
            <a
              class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary"
              *hasAnyPermission="permissionModules.MANAGE_TAG_CATEGORY; disableEvent: true"
              [routerLink]="[appRoutes.EDIT_TAG_CATEGORY, category?.id]"
            >
              <span title="Edit" [inlineSVG]="'assets/media/svg/icons/edit.svg'" cacheSVG="true" class="svg-icon svg-icon-md"> </span>
            </a>
            <a
              class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary ml-3"
              *hasAnyPermission="permissionModules.MANAGE_TAG_CATEGORY; disableEvent: true"
              (click)="confirmDeleteCategory(category?.id)"
            >
              <span title="Archive" [inlineSVG]="'assets/media/svg/icons/archive.svg'" cacheSVG="true" class="svg-icon svg-icon-md"> </span>
            </a>
          </td>
        </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="4" class="center-align">No tag category found.</td>
        </tr>
      </ng-template>
    </p-table>
  </div>
</div>

<p-dialog header="Delete Project" [(visible)]="showDeleteDialog" [modal]="true" class="confirm-dialog" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
  <h5 class="p-m-0">Are you sure you want to delete this tag category?</h5>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center">
      <button type="button" class="btn-cancel" (click)="closeModal()">No</button>
      <button type="button" class="btn-save" (click)="deleteCategory()" [isSubmitting]="isSubmitting">Yes</button>
    </div>
  </ng-template>
</p-dialog>

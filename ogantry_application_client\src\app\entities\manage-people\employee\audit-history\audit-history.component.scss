.center-align {
  text-align: center !important;
}
.text-number-right {
  text-align: right;
}
.header-width-type {
  width: 10%;
}
.header-width-skill {
  width: 8%;
}
.financial-history-body,
.financial-history-header {
  height: 60px !important;
}
.header-width-region {
  width: 8%;
}
.header-width {
  width: 12%;
}
.header-width-cost {
  width: 9%;
}
.header-width-daily {
  width: 20%;
}

.header-width-avail {
  width: 9%;
}
.header-width-start {
  width: 11%;
}
::ng-deep .p-datatable .p-datatable-loading-overlay {
  top: 35px;
  background-color: transparent;
}
::ng-deep .p-datatable-customers .p-datatable .p-datatable-thead > tr > th {
  height: 20px;
  color: #4b3f72;
  font-family: Poppins;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 21px;
  border-bottom: none;
  padding: 0.5rem 0.5rem;
  background-color: #ecedf6 !important;
}

::ng-deep .p-datatable .p-datatable-thead tr > th:first-child,
::ng-deep .p-datatable .p-datatable-tbody tr > td:first-child {
  padding-left: 1rem;
}

::ng-deep .p-datatable-customers .p-datatable-tbody > tr > td {
  height: 20px;
  max-width: 123px;
  color: #000000;
  font-family: Poppins;
  font-size: 12px;
  letter-spacing: 0;
  line-height: 15px;
  padding: 0.5rem 0.5rem;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
::ng-deep .p-datatable .p-sortable-column.p-highlight:hover .p-sortable-column-icon {
  color: #4b3f72;
}

::ng-deep .p-datatable .p-sortable-column.p-highlight .p-sortable-column-icon {
  color: #4b3f72;
}

::ng-deep .p-datatable .p-sortable-column.p-highlight,
.p-datatable .p-sortable-column.p-highlight:hover {
  background: #f8f9fa;
  color: #4b3f72;
}

::ng-deep .p-datatable .p-sortable-column.p-highlight:hover {
  background: #f8f9fa;
  color: #4b3f72;
}
::ng-deep .accordion .card .card-header {
  background-color: transparent;
  padding: 0.5rem;
}
::ng-deep .accordion > .card {
  border: 0;
}

::ng-deep .p-datatable .p-sortable-column:focus {
  box-shadow: inset 0 0 0 0.2rem #4b3f72;
  outline: 0 none;
}
::ng-deep .p-paginator .p-dropdown {
  width: 91px;
}

::ng-deep .p-datatable-tbody tr:last-child > td {
  border-bottom: none;
}

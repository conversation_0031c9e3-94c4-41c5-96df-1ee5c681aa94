import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SecurityDomainsRoutingModule } from './access-control-routing.module';
import { ManageUserComponent } from './manage-user/manage-user.component';
import { CreateUserComponent } from './create-user/create-user.component';
import { SharedModule } from '@shared/shared.module';
import { TableModule } from 'primeng/table';
import { DialogModule } from 'primeng/dialog';
import { ManageRoleComponent } from './roles/manage-role/manage-role.component';
import { CreateRoleComponent } from './roles/create-role/create-role.component';
import { FormsModule } from '@angular/forms';

@NgModule({
  declarations: [ManageRoleComponent, CreateRoleComponent, ManageUserComponent, CreateUserComponent],
  imports: [CommonModule, SecurityDomainsRoutingModule, SharedModule, TableModule, DialogModule, FormsModule]
})
export class AccessControlModule {}

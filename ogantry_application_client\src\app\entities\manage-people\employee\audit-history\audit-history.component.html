<ng-container *ngIf="showAccordian">
  <ngb-accordion #acc="ngbAccordion" activeIds="custom-panel-1">
    <ngb-panel id="custom-panel-1" *ngIf="showFinancial">
      <ng-template ngbPanelHeader let-opened="opened">
        <div class="d-flex align-items-center justify-content-between border-bottom">
          <h5 class="pl-4">Employee History</h5>
          <button ngbPanelToggle class="btn btn-link" (click)="getFinancialHistory()"><fa-icon icon="chevron-down"></fa-icon></button>
        </div>
      </ng-template>
      <ng-template ngbPanelContent let-opened="opened">
        <p-table [value]="financialHistory" dataKey="id" [loading]="financialLoader" styleClass="p-datatable-customers">
          <ng-template pTemplate="header">
            <tr class="financial-history-header">
              <th class="text-center">Hourly Cost</th>
              <th class="text-center">Utilization Target</th>
              <th class="text-center">Start Date</th>
              <th class="text-center">End Date</th>
              <th class="text-center">Effective Start Date</th>
              <th class="text-center">Effective End Date</th>
            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-financial>
            <tr class="financial-history-body">
              <td class="text-center">
                <span>{{ financial?.employee_financial?.hourly_cost }} </span>
              </td>
              <td class="text-center">
                <span>{{ financial?.employee_financial?.utilization_target }}</span>
              </td>
              <td class="text-center">
                <span>{{ financial?.employee_financial?.start_date | date : 'MM/dd/yyyy' }}</span>
              </td>
              <td class="text-center">
                <span>{{ financial?.employee_financial?.end_date | date : 'MM/dd/yyyy' }}</span>
              </td>
              <td class="text-center">
                <span>{{ getDateTime(financial?.employee_financial?.effective_start_date) ? getDateTime(financial?.employee_financial?.effective_start_date) : '-' }}</span>
              </td>
              <td class="text-center">
                <span>{{ getDateTime(financial?.employee_financial?.effective_end_date) ? getDateTime(financial?.employee_financial?.effective_end_date) : '-' }}</span>
              </td>
            </tr>
          </ng-template>
        </p-table>
      </ng-template>
    </ngb-panel>
    <!-- TODO -->
    <!-- <ngb-panel id="custom-panel-2">
          <ng-template ngbPanelHeader let-opened="opened">
            <div class="d-flex align-items-center justify-content-between border-bottom">
              <h5 class="pl-4">Audit History</h5>
              <button ngbPanelToggle class="btn btn-link" (click)="getAuditHistory('custom-panel-2')"> <fa-icon icon="chevron-down"></fa-icon></button>
            </div>
          </ng-template>
          <ng-template ngbPanelContent>
            <p-table responsiveLayout="scroll" #dt [value]="auditHistory"  dataKey="id"
            [loading]="auditLoader" styleClass="p-datatable-customers">
            <ng-template pTemplate="header">
              <tr>
                <th id="first_name" class="header-width">
                  First Name
                </th>
                <th id="last_name" class="header-width">
                  Last Name
                </th>
                <th id="start_date" class="header-width-start text-number-right">
                  Start Date
                </th>
                <th id="start_date" class="header-width-start text-number-right">
                  End Date
                </th>
                <th id="position" class="header-width-skill">
                  Skill Set
                </th>

                <th id="type" class="header-width-type">
                  Type
                </th>
                <th id="cost" class="header-width-cost text-number-right">
                  Cost
                </th>
                <th id="cost" class="header-width-daily text-number-right">
                  Daily Billable Hours
                </th>
                <th id="cost" class="header-width text-number-right">
                  Utilization
                </th>
                <th id="region" class="header-width-region">
                  Region
                </th>
                <th id="start_date" class="header-width-start text-number-right">
                  Effective Start Date
                </th>
                <th id="start_date" class="header-width-start text-number-right">
                  Effective End Date
                </th>

              </tr>
            </ng-template>
            <ng-template pTemplate="body" let-employee>
              <tr>
                <td>
                <span [title]="employee?.employee?.first_name"> {{ employee?.employee?.first_name }} </span>
                </td>
                <td>
                <span [title]="employee?.employee?.last_name"> {{ employee?.employee?.last_name }} </span>
                </td>
                <td class="text-number-right">
                  {{ employee?.employee?.start_date | date: "MM/dd/yyyy" }}
                </td>
                <td class="text-number-right">
                  {{ employee?.employee?.end_date | date: "MM/dd/yyyy" }}
                </td>
                <td>
                <span [title]="getSkillSet(employee?.employee?.position_types)"> {{getSkillSet(employee?.employee?.position_types)}} </span>
                </td>

                <td> {{employee?.employee?.employee_type?.name}} </td>
                <td class="text-number-right"> ${{ employee?.employee?.hourly_cost }}</td>

                <td class="text-number-right"> {{ employee?.employee?.utilization_target }}</td>
                <td> {{ employee?.employee?.region?.name }}</td>
                <td class="text-number-right" [title]=" getDateTime( employee?.employee?.effective_start_date) ">
                  {{ getDateTime( employee?.employee?.effective_start_date) }}
                </td>
                <td class="text-number-right" [title]=" getDateTime( employee?.employee?.effective_end_date) ">
                  {{ getDateTime(employee?.employee?.effective_end_date) }}
                </td>
              </tr>
            </ng-template>
            <ng-template pTemplate="emptymessage">
              <tr>
                <td colspan="12" class="center-align">No Audit History found.</td>
              </tr>
            </ng-template>
          </p-table>
          </ng-template>
        </ngb-panel> -->
    <!-- <ngb-panel id="custom-panel-3">
          <ng-template ngbPanelHeader let-opened="opened">
            <div class="d-flex align-items-center justify-content-between border-bottom">
              <h5 class="pl-4">Pending Jobs</h5>
              <button ngbPanelToggle class="btn btn-link" (click)="getPendingJobs('custom-panel-3')"> <fa-icon icon="chevron-down"></fa-icon></button>
            </div>
          </ng-template>
          <ng-template ngbPanelContent>
            <p-table responsiveLayout="scroll" #dt [value]="pendingJobs"  dataKey="id"
            [loading]="pendingJobLoader" styleClass="p-datatable-customers">
          <ng-template pTemplate="header">
            <tr>
              <th id="first_name" class="header-width">
                Status
              </th>
              <th id="first_name" class="header-width">
                Start Date
              </th>
              <th id="first_name" class="header-width">
                End Date
              </th>
              <th id="last_name" class="header-width">
                Attempted At
              </th>
              <th id="last_name" class="header-width">
                Scheduled At
              </th>
              <th id="position" class="header-width">
                Completed At
              </th>
              <th id="email" class="header-width">
                Inserted At
              </th>
            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-employee>
            <tr>
              <td>
                <span [title]="employee?.job?.state"> {{ employee?.job?.state }} </span>
              </td>
              <td>
                <span [title]="employee?.job?.args?.start_date"> {{ employee?.job?.args?.start_date  | date: "MM/dd/yyyy"}} </span>
              </td>
              <td>
                <span [title]="employee?.job?.args?.end_date"> {{ employee?.job?.args?.end_date  | date: "MM/dd/yyyy"}} </span>
              </td>
              <td>
                <span [title]="employee?.job?.attempted_at"> {{ getDateTime(employee?.job?.attempted_at) }} </span>
              </td>
              <td>
                <span [title]="employee?.job?.scheduled_at"> {{ getDateTime(employee?.job?.scheduled_at)}} </span>
              </td>
              <td>
                <span [title]="employee?.job?.completed_at"> {{ getDateTime(employee?.job?.completed_at)  }} </span>
              </td>

              <td> {{ getDateTime(employee?.job?.inserted_at)  }}</td>
            </tr>
          </ng-template>
          <ng-template pTemplate="emptymessage">
            <tr>
              <td colspan="12" class="center-align">No Pending Jobs found.</td>
            </tr>
          </ng-template>
        </p-table>
          </ng-template>
        </ngb-panel> -->
  </ngb-accordion>
</ng-container>

@import '/src/assets/sass/components/variables.bootstrap';

#manage-time {
  ::ng-deep .p-paginator .p-paginator-pages .p-paginator-page:not(.p-highlight):hover {
    border-radius: 50%;
  }

  ::ng-deep .p-paginator .p-paginator-first:not(.p-disabled):not(.p-highlight):hover,
  ::ng-deep .p-paginator .p-paginator-prev:not(.p-disabled):not(.p-highlight):hover,
  ::ng-deep .p-paginator .p-paginator-next:not(.p-disabled):not(.p-highlight):hover,
  ::ng-deep .p-paginator .p-paginator-last:not(.p-disabled):not(.p-highlight):hover {
    background: #e9ecef;
    border-color: transparent;
    color: #495057;
    border-radius: 50%;
  }

  ::ng-deep .p-paginator .p-paginator-first:not(.p-disabled):not(.p-highlight),
  ::ng-deep .p-paginator .p-paginator-prev:not(.p-disabled):not(.p-highlight),
  ::ng-deep .p-paginator .p-paginator-next:not(.p-disabled):not(.p-highlight),
  ::ng-deep .p-paginator .p-paginator-last:not(.p-disabled):not(.p-highlight) {
    border-radius: 50%;
    background-color: #f4f5f8;
  }

  ::ng-deep .p-paginator .p-paginator-pages .p-paginator-page {
    font-size: 14px;
    &.p-highlight {
      background: #4b3f72;
      border-color: #e3f2fd;
      color: #ffffff;
      border-radius: 50%;
      pointer-events: none;
    }
  }

  ::ng-deep .p-paginator .p-dropdown {
    width: 91px;
  }

  ::ng-deep .p-paginator {
    display: flex !important;
    justify-content: flex-start !important;
    border: none;
    margin-top: 15px;

    .p-paginator-current {
      position: absolute;
      right: 0;
      color: #575962;
      font-size: 14px;
      letter-spacing: 0;
      font-weight: 500;
      cursor: default;
    }

    .p-paginator-rpp-options {
      margin-left: 20px;
      height: 37px;
      width: 100px;
      border-radius: 20px;
      background-color: #f4f5f8;
      border: none;
    }
    .p-dropdown-label.p-inputtext {
      display: flex;
      align-items: center;
      padding-left: 15px;
      font-size: 14px;
    }

    .p-dropdown-trigger-icon {
      padding-right: 20px;
    }
  }

  .last-col-width {
    width: 85px !important;
  }

  .scroll-hide {
    scrollbar-width: none;
  }

  .filter-sidebar-for-manage-time {
    width: 40%;
  }

  .filter-sidebar-for-manage-time {
    .add-contact .card-body.create-card {
      box-shadow: none;
      margin: 0;
      padding: 20px !important;
    }

    .filter-form {
      .PL-border {
        box-sizing: border-box;
        border: 1px solid #eeebf4;
        border-radius: 9px;
        margin-bottom: 1rem;
      }

      input[type='radio']:checked + label:before {
        background: #4b3f72;
        color: #ffffff;
        content: '\2713';
        text-align: center;
      }

      input[type='radio'] + label:before {
        border: 1px solid #4b3f72;
        border-radius: 1rem;
        content: '\00a0';
        display: inline-block;
        font: 16px/1em sans-serif;
        height: 16px;
        padding: 0;
        vertical-align: top;
        width: 16px;
        margin-top: 1px !important;
      }

      .save-filter-radio {
        display: flex;
        justify-content: flex-end;
        width: 100%;
      }

      .width-65 {
        width: 65%;
      }

      ::ng-deep .dropdown .p-dropdown,
      .dropdown .p-dropdown .p-focus {
        width: 100%;
        height: 100%;
        border-radius: 9px !important;
        border: none !important;
        box-shadow: none !important;
        background-color: #f8f8ff !important;
        min-height: 60px !important;
        padding: 1.2rem 0rem;
      }

      ::ng-deep .dropdown .p-dropdown .p-dropdown-label {
        color: #000000;
        font-family: Poppins;
        font-size: 16px;
        font-weight: 500;
        letter-spacing: -0.32px;
        line-height: 25px;
      }

      ::ng-deep .dropdown .p-dropdown .p-dropdown-label.p-placeholder {
        color: #b5b5c3 !important;
      }

      ::ng-deep .dropdown .p-dropdown,
      ::ng-deep .p-multiselect {
        width: 100%;
        height: 100%;
        border-radius: 9px !important;
        border: none !important;
        background-color: #f8f8ff !important;
        min-height: 30px !important;
      }

      ::ng-deep .dropdown .p-dropdown .p-dropdown-label,
      ::ng-deep .p-multiselect .p-multiselect-label {
        color: #000000;
        font-family: Poppins;
        font-size: 16px;
        font-weight: 500;
        letter-spacing: -0.32px;
        line-height: 25px;
        padding-top: 0.5rem;
        padding-left: 1rem;
      }

      ::ng-deep .dropdown .p-dropdown .p-dropdown-label.p-placeholder,
      ::ng-deep .p-multiselect .p-multiselect-label.p-placeholder {
        color: #b5b5c3 !important;
      }

      ::ng-deep .p-multiselect:not(.p-disabled).p-focus {
        box-shadow: none;
      }

      ::ng-deep .p-checkbox .p-checkbox-box.p-highlight {
        border-color: #827da0 !important;
        background: #827da0 !important;
      }

      ::ng-deep .p-checkbox:not(.p-checkbox-disabled) .p-checkbox-box:hover {
        border-color: #827da0;
      }

      ::ng-deep .p-checkbox:not(.p-checkbox-disabled) .p-checkbox-box .p-highlight:hover {
        border-color: #827da0 !important;
        background: #827da0 !important;
      }

      ::ng-deep .p-multiselect-panel .p-multiselect-items .p-multiselect-item.p-highlight {
        border-color: #dcdcdd;
        background: #dcdcdd;
      }

      ::ng-deep .p-multiselect-panel .p-multiselect-items .p-multiselect-item:focus {
        box-shadow: none;
      }

      ::ng-deep .p-multiselect.p-multiselect-chip .p-multiselect-token {
        background: #dcdcdd;
      }
    }
    .form-group {
      padding-bottom: 0;

      &:first-child {
        padding-top: 0;
      }
    }
    .background {
      height: 38px;
      border-radius: 9px 9px 0 0;
      background-color: #eeebf4;
      display: flex;
      align-items: center;
      padding-left: 0.5rem;
    }

    .card-body .timesheet-range .timesheet-range-label {
      background-color: #eeebf4 !important;
      padding: 10px;
      border-radius: 9px 9px 0 0;
      width: 100%;
      margin-bottom: 0 !important;
    }
  }

  .table-content {
    .p-table-for-manage-time {
      ::ng-deep .p-datatable-wrapper {
        height: auto !important;
        max-height: 75vh !important;
        overflow: auto;
      }
      ::ng-deep .p-datatable .p-datatable-header {
        border: none !important;
        background-color: #fff !important;
      }
      ::ng-deep .p-datatable .p-datatable-tbody > tr > td {
        color: #000000;
        font-family: Poppins;
        font-size: 12px;
        font-weight: 500;
        letter-spacing: 0;
        line-height: 15px;
        border: 1px solid #edeff3;
        height: 40px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 80px;
        flex: auto;

        .p-element {
          .output {
            padding-top: 1rem;
            padding-left: 1.5rem;
          }

          .p-inputtext {
            margin-top: 5px;
            margin-left: 10px;
          }
        }
      }
      ::ng-deep .p-datatable .p-datatable-thead > tr > th {
        height: 36px;
        color: #4b3f72;
        font-family: Poppins;
        font-size: 14px;
        font-weight: 500;
        letter-spacing: 0;
        line-height: 21px;
        border-bottom: none;
        border: 1px solid #edeff3;
        background-color: #ecedf6;
        position: sticky;
        top: 0px;
        padding: 0.5rem 1rem;
        width: 80px;
        flex: auto;
      }

      .submit-btn {
        margin-right: 8px;
      }
    }
    ::ng-deep .p-inputtext:enabled:focus {
      box-shadow: none !important;
      border-color: #4b3f72;
    }
    ::ng-deep .month-picker .p-inputtext {
      width: 160px !important;
      min-height: 40px !important;
      height: 100%;
    }

    .btn-switcher {
      border: 0px !important;
      background: #574985;
      height: 32px;
      width: 32px;
      margin: 0px 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0px;
      border-radius: 4px;
    }

    .day-col-wrapper {
      width: 77px !important;
      display: flex;
      justify-content: center;
      text-align: end;
      ::ng-deep .p-inputtext {
        width: 52px !important;
      }

      &.timesheet-data {
        justify-content: space-between;
        align-items: flex-start;
        padding: 0;
        position: relative;
      }

      .add-comment {
        background-color: #4b3f72;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;

        .pi,
        .svg-icon {
          color: white;
          font-size: 6px;
          padding: 1px;
        }

        .custom-btn {
          background-color: #4b3f72;
          height: 10px;
          width: 10px;
          border: none;
        }

        .custom-icon {
          filter: brightness(5);
        }

        ::ng-deep {
          .custom-icon svg {
            height: 6px !important;
            width: 6px !important;
          }
        }
      }
    }

    .day-col-wrapper:hover .add-comment .pi {
      font-size: 10px;
    }

    .day-col-wrapper:hover .add-comment .custom-btn {
      height: 14px !important;
      width: 14px !important;

      ::ng-deep {
        .custom-icon svg {
          height: 8px !important;
          width: 8px !important;
        }
      }
    }

    .col-width-wrapper {
      width: 150px !important;

      &.frozen-column {
        z-index: 9999 !important;
      }

      &.fix-left-project {
        left: 150px !important;
      }

      &.fix-left-position {
        left: 300px !important;
      }

      &.fix-left-position-without-client {
        left: 150px !important;
      }

      &.fix-right-col {
        width: 100px !important;
        right: 0;
      }

      &.last-col-width {
        width: 85px !important;
      }
    }

    ::ng-deep {
      .p-datatable-scrollable .p-datatable-thead > tr,
      .p-datatable-scrollable .p-datatable-tbody > tr,
      .p-datatable-scrollable .p-datatable-tfoot > tr {
        position: sticky !important;
      }
    }
  }
}

::ng-deep .comment-overlay {
  margin-left: 0px !important;
  margin-top: 40px !important;

  .add-comment-input {
    width: 300px;
  }
}

.gray-background {
  background-color: #ecedf6 !important;
}

::ng-deep .filter-dialog {
  .p-component-overlay {
    background: none !important;
    animation: none !important;
  }

  .p-dialog {
    height: 253px;
    width: 400px;
    top: 51px;

    .p-dialog-header {
      display: none;
    }

    .p-dialog-content {
      padding-top: 1rem;
    }

    .title {
      color: #757575;
      font-family: Poppins;
      font-size: 12px;
      letter-spacing: 0;
      line-height: 18px;
    }

    .share-icon {
      background-color: #4b3f72 !important;
      border-color: #4b3f72 !important;

      i,
      em {
        color: white;
      }
    }

    .filter-body {
      display: flex;
      align-content: center;
      align-items: center;
    }

    .form-check {
      height: 40px;
      background-color: white;
      display: flex;
      align-items: center;
      width: 100%;

      .form-check-label,
      .form-check-label:hover {
        cursor: pointer;
        color: black;
        width: 75%;
        color: #000000;
        font-family: Poppins;
        font-size: 12px;
        letter-spacing: 0;
        line-height: 18px;
      }
    }

    .filter-icons {
      display: flex;
      width: 100%;
      justify-content: flex-end;
    }
  }
}

// .p-tabview-nav ul{
//   li{
//     flex: 1 !important;
//     a{
//     span{
//       text-align: center;
//     }
//     }
//   }
// }

::ng-deep .p-tabview-nav {
  li {
    flex: 1 !important;
    span {
      text-align: center !important;
      width: 100%;
    }
  }
}

::ng-deep .p-tabview .p-tabview-nav li.p-highlight .p-tabview-nav-link {
  background: #eeebf4;
  border-color: #4b3f72 !important;
  color: black !important;
}

::ng-deep .p-tabview .p-tabview-nav li .p-tabview-nav-link:not(.p-disabled):focus {
  outline: 0 none;
  outline-offset: 0;
  box-shadow: inset 0 0 0 0.2rem #4b3f72;
}

.p-highlight {
  border-bottom: 0px;
  outline-width: 0px 1px;
}

.bench {
  ::ng-deep .popup-column {
    z-index: 100 !important;
    position: absolute !important;
    right: 230px !important;
    top: 58px !important;
  }
}

::ng-deep .fix-employee {
  position: sticky !important;
  left: 300px !important;
}

.fix-width-employee {
  left: 450px !important;
}
::ng-deep .p-datatable-scrollable-both .p-datatable-thead > tr > th,
.p-datatable-scrollable-both .p-datatable-tbody > tr > td,
.p-datatable-scrollable-both .p-datatable-tfoot > tr > td,
.p-datatable-scrollable-horizontal .p-datatable-thead > tr > th .p-datatable-scrollable-horizontal .p-datatable-tbody > tr > td,
.p-datatable-scrollable-horizontal .p-datatable-tfoot > tr > td,
.p-datatable-scrollable-both .p-datatable-thead > tr > th {
  flex: 1 1 0 !important;
}

.pointer-disable {
  // pointer-events: none;
}

::ng-deep .p-datatable-scrollable-both .p-datatable-tbody > tr > td,
.p-datatable-scrollable-both .p-datatable-tfoot > tr > td,
.p-datatable-scrollable-horizontal .p-datatable-thead > tr > th .p-datatable-scrollable-horizontal .p-datatable-tbody > tr > td,
.p-datatable-scrollable-horizontal .p-datatable-tfoot > tr > td {
  flex: 1 1 0 !important;
}

.mange-time-wrapper {
  ::ng-deep .mat-drawer.mat-drawer-end {
    transform: none !important;
  }
}

.position-Dialog {
  .p-dialog {
    height: 253px;
    width: 400px;
    top: 51px;
  }
}

::ng-deep .btn-filter-icon {
  background: #4b3f72;
  border-radius: 6px;
  margin-right: 1rem;
}
.export-action-listing {
  display: flex;
  align-items: center;
  flex-direction: column;
  button {
    width: 100%;
    background-color: #f1f1f1;
    color: #000;
    &:hover {
      background-color: #b9b9b9;
      color: #000;
    }
    ::ng-deep .p-button-icon {
      font-size: 1.2rem;
      margin-left: 1rem;
    }
  }
}

::ng-deep .export-dialog {
  .p-dialog {
    height: 128px;
    width: 170px;
    .p-dialog-header {
      display: none;
    }
  }
  .p-dialog .p-dialog-content {
    padding: 0 5px;
  }
}
::ng-deep .download svg {
  width: 26px !important;
  height: 26px !important;
}

.extended-field-header {
  min-width: 180px !important;
}
::ng-deep .work-exception-fix {
  left: 150px !important;
}

.fix-Timeofff-length {
  th {
    width: fit-content !important;
    padding-left: 2px !important;
    padding-right: 2px !important;
  }

  // td :not(:nth-child(1)):not(:nth-child(2)){
  //   justify-content: center !important;
  //   align-items: center !important;
  // }

  td:not(:nth-child(1)):not(:nth-child(2)) {
    justify-content: center !important;
    align-items: center !important;
  }
}

.disable-link {
  pointer-events: none;
  color: gray;
}

.frozen-right {
  position: sticky !important;
  right: 0px !important;
  left: 0px !important;
}
.frozen-right-total {
  position: sticky !important;
  right: 85px !important;
  left: 0px !important;
}

.approved {
  font-size: 18px;
  font-weight: 600px;
  text-align: center;
}

.line-hight-dialgo {
  line-height: 30px;
}

.confirm-dialog-approved {
  ::ng-deep .p-dialog {
    width: 30vw;
    min-width: 300px !important;
  }
}
img {
  cursor: pointer;
}

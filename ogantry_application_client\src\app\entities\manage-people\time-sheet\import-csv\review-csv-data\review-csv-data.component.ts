import { Component, Input, OnInit } from '@angular/core';
import { ManagePeopleService } from '@entities/manage-people/manage-people.service';
import { GlobalDetailsTimeSheetFiles } from '@entities/manage-people/mange-people.model';
import { TimesheetStatus } from '@shared/enum/mange-timesheet.enum';
import { AlertType } from '@shared/models/alert-type.enum';
import { LayoutUtilsService } from '@shared/services/layout-utils.service';
import moment from 'moment';
import { ConfirmationService } from 'primeng/api';
import { forkJoin } from 'rxjs';

@Component({
  selector: 'app-review-csv-data',
  templateUrl: './review-csv-data.component.html',
  styleUrls: ['./review-csv-data.component.scss'],
  providers: [ConfirmationService]
})
export class ReviewCsvDataComponent implements OnInit {
  @Input() updatedCSVData;
  @Input() tableHeader;
  @Input() selectedColumns;
  @Input() uploadedCSVFile: File;
  @Input() timeEntries: GlobalDetailsTimeSheetFiles;

  filterOptions = [{ option: 'Today' }, { option: 'Yesterday' }, { option: 'This Week' }, { option: 'This Month' }, { option: 'This Half Year' }];
  showConformationDialog: boolean;

  constructor(private readonly managePeopleService: ManagePeopleService, private layoutUtilsService: LayoutUtilsService, private confirmationService: ConfirmationService) {}

  ngOnInit(): void {
    this.managePeopleService.saveTimeSheet.subscribe((res) => {
      if (res) {
        this.confirmationService.confirm({
          target: event.target as EventTarget,
          message: 'Are you sure that you want to Save Your Time Sheet Data',
          header: 'Confirmation',
          acceptIcon: 'none',
          rejectIcon: 'none',
          rejectButtonStyleClass: 'p-button-text',
          accept: () => {
            this.saveTimeSheetData();
          }
        });
      }
    });
  }

  saveTimeSheetData() {
    const formattedData = this.updatedCSVData.map((res2) => ({
      time_entry: {
        date: moment(res2[this.selectedColumns.Date]?.value).format('YYYY-MM-DD'),
        hours: parseFloat(res2[this.selectedColumns.Hours]?.value),
        position_id: parseInt(res2[this.selectedColumns.Position]?.positionId),
        status: TimesheetStatus.APPROVED
      }
    }));

    //we are calling API for every time for every Time Entries
    const apiCalls = formattedData.map((data) => this.managePeopleService.saveTimeSheetEntries(data));

    forkJoin(apiCalls).subscribe((responses) => {
      const allSuccess = responses.every((res) => res['success']);

      if (allSuccess) {
        const payload = {
          filePath: this.uploadedCSVFile.name,
          Date: new Date(),
          User: localStorage.getItem('userEmail')
        };
        if (this.timeEntries) {
          this.timeEntries.global_detail.extended_fields.uploadedFiles.push(payload);
          this.managePeopleService.updateTimeSheetEntries(this.timeEntries, this.timeEntries.global_detail.id).subscribe((res) => {
            this.managePeopleService.showTimeSheetEntries.next(true);
            this.layoutUtilsService.showActionNotification('Data Saved Successfully', AlertType.Success);
          });
        } else {
          const param = {
            name: 'ActualsImportLog',
            extended_fields: {
              uploadedFiles: [{ ...payload }]
            }
          };
          this.managePeopleService.addTimeSheetEntires(param).subscribe((res) => {
            this.managePeopleService.showTimeSheetEntries.next(true);
            this.layoutUtilsService.showActionNotification('Data Saved Successfully', AlertType.Success);
          });
        }
      }
    });
  }
}

"use strict";var KTImageInput=function(e,t){var n=this,i=KTUtil.getById(e);KTUtil.getBody();if(i){var a={editMode:!1},r={construct:function(e){return KTUtil.data(i).has("imageinput")?n=KTUtil.data(i).get("imageinput"):(r.init(e),r.build(),KTUtil.data(i).set("imageinput",n)),n},init:function(e){n.element=i,n.events=[],n.input=KTUtil.find(i,'input[type="file"]'),n.wrapper=KTUtil.find(i,".image-input-wrapper"),n.cancel=KTUtil.find(i,'[data-action="cancel"]'),n.remove=KTUtil.find(i,'[data-action="remove"]'),n.src=KTUtil.css(n.wrapper,"backgroundImage"),n.hidden=KTUtil.find(i,'input[type="hidden"]'),n.options=KTUtil.deepExtend({},a,e)},build:function(){KTUtil.addEvent(n.input,"change",function(e){if(e.preventDefault(),n.input&&n.input.files&&n.input.files[0]){var t=new FileReader;t.onload=function(e){KTUtil.css(n.wrapper,"background-image","url("+e.target.result+")")},t.readAsDataURL(n.input.files[0]),KTUtil.addClass(n.element,"image-input-changed"),KTUtil.removeClass(n.element,"image-input-empty"),r.eventTrigger("change")}}),KTUtil.addEvent(n.cancel,"click",function(e){e.preventDefault(),r.eventTrigger("cancel"),KTUtil.removeClass(n.element,"image-input-changed"),KTUtil.removeClass(n.element,"image-input-empty"),KTUtil.css(n.wrapper,"background-image",n.src),n.input.value="",n.hidden&&(n.hidden.value="0")}),KTUtil.addEvent(n.remove,"click",function(e){e.preventDefault(),r.eventTrigger("remove"),KTUtil.removeClass(n.element,"image-input-changed"),KTUtil.addClass(n.element,"image-input-empty"),KTUtil.css(n.wrapper,"background-image","none"),n.input.value="",n.hidden&&(n.hidden.value="1")})},eventTrigger:function(e){for(var t=0;t<n.events.length;t++){var i=n.events[t];if(i.name==e){if(1!=i.one)return i.handler.call(this,n);if(0==i.fired)return n.events[t].fired=!0,i.handler.call(this,n)}}},addEvent:function(e,t,i){return n.events.push({name:e,handler:t,one:i,fired:!1}),n}};return n.setDefaults=function(e){a=e},n.on=function(e,t){return r.addEvent(e,t)},n.one=function(e,t){return r.addEvent(e,t,!0)},r.construct.apply(n,[t]),n}};"undefined"!=typeof module&&void 0!==module.exports&&(module.exports=KTImageInput);

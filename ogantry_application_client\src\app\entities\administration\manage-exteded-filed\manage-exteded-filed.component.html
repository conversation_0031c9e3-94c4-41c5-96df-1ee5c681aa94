<div class="card card-custom gutter-b" id="manageExtendedFiledCategory">
  <app-card-header [cardTitle]="cardTitle" [cardSubTitle]="cardSubTitle" [buttons]="buttons"></app-card-header>
  <div class="card-body">
    <p-table
      responsiveLayout="scroll"
      #dt
      [value]="extendFields"
      [lazy]="true"
      (onLazyLoad)="getGlobalDetailTags()"
      dataKey="id"
      [rows]="10"
      [loading]="loading"
      styleClass="p-datatable-customers"
      [filterDelay]="0"
      [sortField]="'name'"
      [sortOrder]="-1"
    >
      <ng-template pTemplate="header">
        <tr>
          <th id="category" class="header-width">Index</th>
          <th colspan="2">JSON</th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-field let-rowIndex="rowIndex">
        <tr>
          <td>{{ rowIndex }}</td>
          <td colspan="2" class="overflow-ellipsis">
            {{ field.jsonData | json }}
          </td>
          <!-- <td>
            {{ field?.component }}
          </td> -->
          <!-- <td>
            {{ field?.type }}
          </td>
          <td>
            {{ field?.name }}
          </td> -->
        </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="4" class="center-align">No Data found.</td>
        </tr>
      </ng-template>
    </p-table>
  </div>
</div>
<p-dialog header="Delete Project" [(visible)]="showDeleteDialog" [modal]="true" class="confirm-dialog" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
  <h5 class="p-m-0">Are you sure you want to delete this tag?</h5>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center">
      <button type="button" class="btn-cancel" (click)="closeModal()">No</button>
      <button type="button" class="btn-save" (click)="deleteTag()" [isSubmitting]="isSubmitting">Yes</button>
    </div>
  </ng-template>
</p-dialog>

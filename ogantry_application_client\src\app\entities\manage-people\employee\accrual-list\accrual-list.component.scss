#accrual-list {
  ::ng-deep {
    .work_exception-input,
    .start-date-input {
      width: 100% !important;
    }
  }

  .add-new-accruals {
    margin-bottom: 0.5rem;
    text-decoration: underline;
  }

  .accruals-header,
  .accruals-body {
    height: 30px !important;

    .work-exception,
    .start-date,
    .balance {
      width: 20% !important;
    }

    .cycle,
    .rate {
      width: 15% !important;
    }

    .action {
      width: 10% !important;
    }
  }

  ::ng-deep .p-inputtext::placeholder,
  .work_exception-input .p-dropdown .p-placeholder {
    text-align: center;
    font-size: 14px !important;
  }

  .p-button-success,
  .p-button-danger {
    background-color: white !important;
    border: none !important;
    width: auto !important;
  }

  .pointer-none {
    pointer-events: none;
    color: gray;
  }

  ::ng-deep .p-datatable-customers {
    overflow: auto !important;
  }

  .error {
    height: 15px !important;
  }
}

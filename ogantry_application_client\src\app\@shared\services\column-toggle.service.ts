import { Injectable } from '@angular/core';

@Injectable({ providedIn: 'root' })
export class ColumnToggleService {
  constructor() {}

  public setSelectedColumns(value, key): void {
    const pageColumn = {};
    pageColumn[key] = value;
    if (localStorage.getItem('selectedColumnsArray')) {
      const cacheColumn = JSON.parse(localStorage.getItem('selectedColumnsArray'));
      cacheColumn[key] = value;
      localStorage.setItem('selectedColumnsArray', JSON.stringify(cacheColumn));
    } else {
      localStorage.setItem('selectedColumnsArray', JSON.stringify(pageColumn));
    }
  }
}

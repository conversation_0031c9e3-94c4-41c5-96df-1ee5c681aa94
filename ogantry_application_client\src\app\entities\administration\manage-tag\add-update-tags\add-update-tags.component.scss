::ng-deep .dropdown .p-dropdown,
::ng-deep .dropdown .p-multiselect {
  width: 100%;
  height: 100%;
  border-radius: 9px !important;
  border: none !important;
  background-color: #f8f8ff !important;
  min-height: 60px !important;
  padding-top: 1rem;
}

::ng-deep .dropdown .p-dropdown .p-dropdown-label,
::ng-deep .dropdown .p-multiselect .p-multiselect-label {
  color: #495057;
  font-family: Poppins;
  font-size: 16px;
  // font-weight: 500;
  letter-spacing: -0.32px;
  line-height: 25px;
  padding-top: 0.5rem;
  // padding-left: 1rem;
}

::ng-deep .dropdown .p-dropdown .p-dropdown-label.p-placeholder,
::ng-deep .dropdown .p-multiselect .p-multiselect-label.p-placeholder {
  color: #b5b5c3 !important;
}

::ng-deep .p-inputgroup {
  width: 100%;
  border-radius: 9px !important;
  border: none !important;
  background-color: #f8f8ff !important;
  min-height: 60px !important;
}

::ng-deep .p-inputgroup .p-inputtext {
  border: none !important;
  background-color: #f8f8ff !important;
  font-size: 16px;
  // padding-left: 1rem;
}

::ng-deep .p-inputgroup-addon {
  border: 0 !important;
  width: 4rem;
}

::ng-deep .p-inputtext:enabled:focus {
  box-shadow: none;
}

.card-title {
  color: #252525;
  font-family: <PERSON>pins;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 0;
  line-height: 25px;
  text-align: left;
}

.card-subtitle {
  color: #000000;
  font-family: Poppins;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 25px;
  text-align: left;
}
.margin {
  color: #119da4 !important;
}
.card {
  border: 0;
}

.card1 {
  box-sizing: border-box;
  border: 1px solid #119da4 !important;
  border-radius: 8px;
  background-color: #ffffff;
  height: 10rem;
}

.card2 {
  box-sizing: border-box;
  border: 1px solid #dc2f44 !important;
  border-radius: 8px;
  background-color: #ffffff;
  height: 10rem;
}

.card3 {
  box-sizing: border-box;
  border: 1px solid #ffc857 !important;
  border-radius: 8px;
  background-color: #ffffff;
  height: 10rem;
}

::ng-deep textarea.form-control {
  color: #b5b5c3;
  font-family: <PERSON>pins;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: -0.32px;
  line-height: 25px;
}

import { HttpClient, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ClientList } from '@entities/client/client.model';
import { Employees, PositionList } from '@entities/project/project.model';
import { ProjectList } from '@entities/utilization-management/utilization.model';
import { ApiUrl } from '@shared/constants';
import { OGantryHttpResponse } from '@shared/models';
import moment from 'moment';
import { Observable, Subject } from 'rxjs';
import { CalcAccrualObj, CalcAccrualResponse, GlobalDetailTimeSheetEntries, OGPositionList, OGProjectList, timeSheetEntries } from './mange-people.model';

@Injectable({
  providedIn: 'root'
})
export class ManagePeopleService {
  constructor(private readonly http: HttpClient) {}

  saveTimeSheet = new Subject();
  showTimeSheetEntries = new Subject();

  getClientData(queryFilter: any): Observable<HttpResponse<OGantryHttpResponse<ClientList>>> {
    return this.http.get<OGantryHttpResponse<ClientList>>(ApiUrl.client, {
      params: queryFilter,
      observe: 'response'
    });
  }

  getProjectList(): Observable<OGantryHttpResponse<ProjectList>> {
    return this.http.get<OGantryHttpResponse<ProjectList>>(ApiUrl.project);
  }

  getPositionList(): Observable<OGantryHttpResponse<PositionList>> {
    return this.http.get<OGantryHttpResponse<PositionList>>(`${ApiUrl.position}`, { params: { visibility: 'Public' } });
  }

  getPositionActiveEmployee(id, queryFilter): Observable<OGantryHttpResponse<PositionList>> {
    return this.http.get<OGantryHttpResponse<PositionList>>(`${ApiUrl.activeEmployeePosition}/${id}`, { params: queryFilter });
  }

  getEmployeeList(queryFilter): Observable<OGantryHttpResponse<Employees>> {
    return this.http.get<OGantryHttpResponse<Employees>>(`${ApiUrl.employee}/lookup`, { params: queryFilter });
  }

  calcAccruals(queryParams): Observable<OGantryHttpResponse<CalcAccrualResponse>> {
    return this.http.get<OGantryHttpResponse<CalcAccrualResponse>>(`${ApiUrl.calcAccruals}`, { params: queryParams });
  }

  saveTimeSheetEntries(requestObject): Observable<OGantryHttpResponse<timeSheetEntries>> {
    return this.http.post<OGantryHttpResponse<timeSheetEntries>>(`${ApiUrl.timeEntries}`, requestObject);
  }

  getTimeSheetEntries(detailName: string): Observable<OGantryHttpResponse<GlobalDetailTimeSheetEntries>> {
    return this.http.get<OGantryHttpResponse<GlobalDetailTimeSheetEntries>>(ApiUrl.globalDetails, { params: { name: detailName } });
  }

  addTimeSheetEntires(data): Observable<OGantryHttpResponse<GlobalDetailTimeSheetEntries>> {
    return this.http.post<OGantryHttpResponse<GlobalDetailTimeSheetEntries>>(`${ApiUrl.globalDetails}`, { global_detail: data });
  }

  updateTimeSheetEntries(data, id): Observable<OGantryHttpResponse<GlobalDetailTimeSheetEntries>> {
    return this.http.put<OGantryHttpResponse<GlobalDetailTimeSheetEntries>>(`${ApiUrl.globalDetails}/${id}`, { ...data });
  }

  isMatchWithOG(data, selectedOptions, clientList, projectList: OGProjectList[], positionList: OGPositionList[], byPassPosition?: boolean) {
    this.isMatchDateFormat(data, selectedOptions);
    this.checkHours(data, selectedOptions);

    const isMatchedWithClient = this.isMatchWithClient(data, selectedOptions, clientList);

    if (isMatchedWithClient) {
      this.isMatchWithProject(data, selectedOptions, projectList, positionList, byPassPosition);
    } else {
      this.setIsMatchWithOGFlags(data, selectedOptions, false);
    }
  }

  isMatchDateFormat(data, selectedOptions) {
    const dateField = data[selectedOptions.Date];
    dateField.isMatchWithOG = moment(dateField.value, 'MM/DD/YYYY', true).isValid();
  }

  checkHours(data, selectedOptions) {
    const dateField = data[selectedOptions.Hours];
    if (dateField.value > 24 || typeof dateField.value !== 'number') {
      dateField.isMatchWithOG = false;
    } else {
      dateField.isMatchWithOG = true;
    }
  }

  isMatchWithClient(data, selectedOptions, clientList) {
    const clientField = data[selectedOptions.Client];
    const isMatched = clientList.includes(clientField.value);
    clientField.isMatchWithOG = isMatched;
    return isMatched;
  }

  isMatchWithProject(data, selectedOptions, projectList, postionList, byPassPosition?: boolean) {
    const projectField = data[selectedOptions.Project];
    const clientField = data[selectedOptions.Client];

    const isProjectMatched = projectList.some((res) => res.projectName === projectField.value && res.clientName === clientField.value);

    if (!isProjectMatched) {
      this.setIsMatchWithOGFlags(data, selectedOptions, false);
    } else {
      this.isMatchWithPosition(data, selectedOptions, postionList, byPassPosition);
    }

    return (projectField.isMatchWithOG = isProjectMatched);
  }

  isMatchWithPosition(data, selectedOptions, postionList, byPassPosition?: boolean) {
    const positionField = data[selectedOptions.Position];
    const projectField = data[selectedOptions.Project];
    const clientField = data[selectedOptions.Client];
    const employeeField = data[selectedOptions.Employee];
    const isPositionMatched = postionList.some((res) => res.positionName === positionField.value && res.projectName === projectField.value && res.clientName === clientField.value);
    let selectedPos = [];
    if (byPassPosition) {
      selectedPos = postionList.filter((res) => res.projectName === projectField.value && res.clientName === clientField.value && res.employeeFullName === employeeField.value);
    }

    if (!isPositionMatched) {
      this.setIsMatchWithOGFlags(data, selectedOptions, false);
    } else {
      const positionData = postionList.find((res) => res.positionName === positionField.value && res.projectName === projectField.value && res.clientName === clientField.value);
      positionField.positionId = positionData.positionId;
      this.isMatchWithEmployee(data, selectedOptions, postionList);
    }
    positionField.isMatchWithOG = isPositionMatched;
  }

  isMatchWithEmployee(data, selectedOptions, positionData) {
    const positionField = data[selectedOptions.Position];
    const projectField = data[selectedOptions.Project];
    const clientField = data[selectedOptions.Client];
    const employeeField = data[selectedOptions.Employee];
    const isEmployeeMatched = positionData.some(
      (res) =>
        res.positionName === positionField.value && res.projectName === projectField.value && res.clientName === clientField.value && res.employeeFullName === employeeField.value
    );
    employeeField.isMatchWithOG = isEmployeeMatched;
  }

  setIsMatchWithOGFlags(data, selectedOptions, value) {
    data[selectedOptions.Position].isMatchWithOG = value;
    data[selectedOptions.Employee].isMatchWithOG = value;
    data[selectedOptions.Project].isMatchWithOG = value;
  }
}

import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SharedModule } from '@shared/shared.module';
import { UtilizationManagementRoutingModule } from './utilization-management-routing.module';
import { BenchReportComponent } from './bench-report/bench-report.component';
import { TableModule } from 'primeng/table';
import { DropdownModule } from 'primeng/dropdown';
import { InputTextModule } from 'primeng/inputtext';
import { CalendarModule } from 'primeng/calendar';
import { MatSidenavModule } from '@angular/material/sidenav';
import { OpenPositionReportComponent } from './open-position-report/open-position-report.component';
import { MultiSelectModule } from 'primeng/multiselect';
import { DialogModule } from 'primeng/dialog';
import { StaffedPositionReportComponent } from './staffed-position-report/staffed-position-report.component';
import { TreeSelectModule } from 'primeng/treeselect';
import { MbscModule } from '@mobiscroll/angular';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { BadgeModule } from 'primeng/badge';
import { AddCommasToNumbersPipe } from '@shared/pipes/add-commas-to-numbers.pipe';
import { DynamicDialogModule } from 'primeng/dynamicdialog';
import { PaginatorModule } from 'primeng/paginator';
@NgModule({
  declarations: [BenchReportComponent, OpenPositionReportComponent, StaffedPositionReportComponent],
  imports: [
    CommonModule,
    SharedModule,
    UtilizationManagementRoutingModule,
    TableModule,
    DropdownModule,
    InputTextModule,
    CalendarModule,
    MatSidenavModule,
    MultiSelectModule,
    DialogModule,
    TreeSelectModule,
    MbscModule,
    DynamicDialogModule,
    ProgressSpinnerModule,
    PaginatorModule,
    BadgeModule
  ],
  providers: [AddCommasToNumbersPipe]
})
export class UtilizationManagementModule {}

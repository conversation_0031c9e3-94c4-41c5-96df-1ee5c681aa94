import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { WorkExceptionType } from '@entities/administration/administration.model';
import { AdministrationService } from '@entities/administration/administration.service';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { ButtonParams } from '@shared/models';
import { AlertType } from '@shared/models/alert-type.enum';
import { LayoutUtilsService } from '@shared/services/layout-utils.service';
import { LazyLoadEvent } from 'primeng/api';

@Component({
  selector: 'app-manage-work-exception-type',
  templateUrl: './manage-work-exception-type.component.html',
  styleUrls: ['./manage-work-exception-type.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ManageWorkExceptionTypeComponent extends SflBaseComponent implements OnInit {
  exceptionTypes: WorkExceptionType[] = [];
  loading = false;
  cardTitle = 'Manage Work Exception Types';
  cardSubTitle = null;
  buttons: ButtonParams[] = [
    {
      btnClass: 'btn-save',
      btnText: 'Add New',
      redirectPath: this.appRoutes.CREATE_WORK_EXCEPTION_TYPE,
      permissions: [this.permissionModules.MANAGE_WORK_EXCEPTION_TYPE]
    }
  ];

  deleteTypeId: number;
  showDeleteDialog = false;
  constructor(private readonly adminService: AdministrationService, private readonly cdf: ChangeDetectorRef, private readonly layoutUtilsService: LayoutUtilsService) {
    super();
  }

  ngOnInit(): void {}

  getExceptionTypes(event?: LazyLoadEvent) {
    this.exceptionTypes = [];
    this.loading = true;
    this.cdf.detectChanges();
    this.subscriptionManager.add(
      this.adminService.getExceptionTypes().subscribe(
        (res) => {
          this.loading = false;
          if (res.data.work_exception_types) {
            this.exceptionTypes = res.data.work_exception_types;
          }
          this.cdf.detectChanges();
        },
        () => (this.loading = false)
      )
    );
  }

  confirmDeleteType(id: number) {
    this.deleteTypeId = id;
    this.showDeleteDialog = true;
  }

  closeModal() {
    this.deleteTypeId = null;
    this.showDeleteDialog = false;
  }

  deleteType() {
    this.isSubmitting = true;
    this.subscriptionManager.add(
      this.adminService.deleteWorkExceptionType(this.deleteTypeId).subscribe(
        (res) => {
          this.isSubmitting = false;
          this.closeModal();
          this.layoutUtilsService.showActionNotification('Work Exception Type has been archived successsfully', AlertType.Success);
          this.getExceptionTypes();
          this.cdf.detectChanges();
        },
        () => (this.isSubmitting = false)
      )
    );
  }
}

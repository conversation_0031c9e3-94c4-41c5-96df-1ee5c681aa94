@import '../../../../../assets/sass/components/variables.bootstrap';

#time-sheet-actual {
  .no-log-msg {
    color: #757575;
    font-size: 24px;
    font-weight: 400;
  }

  .no-log-info {
    color: #000;
    font-size: 12px;
    font-weight: 400;
  }
  .imported-history-table-wrapper {
    .action-col-wrapper {
      max-width: 100px;
      display: flex;
      justify-content: center;
    }

    ::ng-deep .p-datatable .p-datatable-thead > tr > th {
      height: 49px;
      color: $primary;
      font-family: Poppins;
      font-size: 14px;
      font-weight: 500;
      letter-spacing: 0;
      line-height: 21px;
      border-bottom: none;
      padding: 0.5rem 0.5rem;
      background-color: #ecedf6 !important;
    }

    ::ng-deep .p-datatable .p-datatable-tbody > tr > td {
      height: 48px;
      color: $black;
      font-family: Poppins;
      font-size: 12px;
      letter-spacing: 0;
      line-height: 15px;
      padding: 0.5rem 0.5rem;
      font-weight: 500;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    ::ng-deep .p-datatable {
      height: 100%;

      .p-datatable-wrapper {
        min-height: 100%;
        overflow-y: auto !important;
        .p-datatable-thead {
          .p-inputtext {
            width: 100%;
            border: 1px solid #4b3f72;
          }
        }
      }
    }
  }

  ::ng-deep .p-datatable-scrollable-both .p-datatable-thead > tr > th,
  .p-datatable-scrollable-both .p-datatable-tbody > tr > td,
  .p-datatable-scrollable-both .p-datatable-tfoot > tr > td,
  .p-datatable-scrollable-horizontal .p-datatable-thead > tr > th .p-datatable-scrollable-horizontal .p-datatable-tbody > tr > td,
  .p-datatable-scrollable-horizontal .p-datatable-tfoot > tr > td,
  .p-datatable-scrollable-both .p-datatable-thead > tr > th {
    flex: 1 1 0 !important;
  }

  ::ng-deep .p-datatable-scrollable-both .p-datatable-tbody > tr > td,
  .p-datatable-scrollable-both .p-datatable-tfoot > tr > td,
  .p-datatable-scrollable-horizontal .p-datatable-thead > tr > th .p-datatable-scrollable-horizontal .p-datatable-tbody > tr > td,
  .p-datatable-scrollable-horizontal .p-datatable-tfoot > tr > td {
    flex: 1 1 0 !important;
  }
}

::ng-deep .p-dialog {
  &.p-confirm-dialog {
    min-width: 400px;
  }
}

::ng-deep .upload-file-dialog .p-dialog {
  width: 27rem !important;

  .p-dialog-content {
    padding: 20px 10px;

    .uploaded-file-name {
      font-size: 14px;
    }

    .upload-csv-btn {
      .p-button:focus {
        box-shadow: none !important;
      }

      .p-fileupload-buttonbar,
      .p-fileupload-content {
        padding: 0pc;
        background: transparent;
        border-color: transparent;
        display: flex;
        justify-content: center;
      }

      p-progressbar {
        display: none;
      }

      .p-fileupload-row > div:first-child {
        display: none;
      }

      .p-fileupload-row > div:nth-child(3) {
        display: none;
      }

      .p-fileupload-row > div:last-child {
        .p-button-icon-only {
          .p-button-icon {
            font-size: 16px;
          }
        }
      }

      .p-fileupload-filename {
        width: 100%;
        font-size: 14px;
      }
    }

    .p-fileupload-choose:not(.p-disabled):hover {
      background-color: #4b3f72;
      border-color: #4b3f72;
    }
  }
}

::ng-deep .field-mapping-dialog .p-dialog {
  width: 40rem !important;

  .mapping-msg-wrapper {
    color: #000;
    font-size: 12px;
  }

  .field-mapping-label {
    margin-bottom: 0px;
    color: #000;
    font-size: 14px;
  }

  .OG-fields-dropdown-wrapper {
    ::ng-deep .p-dropdown {
      width: 170px;
    }
  }

  .ng-valid {
    ::ng-deep .p-dropdown:not(.p-disabled):hover {
      border-color: #ced4da !important;
    }

    ::ng-deep .p-dropdown:not(.p-disabled).p-focus {
      border-color: #ced4da !important;
    }
  }

  ::ng-deep .p-checkbox .p-checkbox-box.p-highlight {
    border-color: #827da0 !important;
    background: #827da0 !important;
  }

  ::ng-deep .p-checkbox:not(.p-checkbox-disabled) .p-checkbox-box:hover {
    border-color: #827da0 !important;
  }

  ::ng-deep .p-dropdown:not(.p-disabled).p-focus {
    box-shadow: none;
  }
}

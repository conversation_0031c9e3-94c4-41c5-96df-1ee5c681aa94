#create_role_form {
  // .dropdown .p-dropdown{
  //   width: 100%;
  //   height: 100%;
  //   border-radius: 9px !important;
  //   border: none !important;
  //   background-color: #f8f8ff !important;
  //   min-height: 30px !important;
  //  }

  ::ng-deep .p-dropdown {
    background-color: #f8f8ff !important;
    width: 100%;
    border: none !important;
  }

  ::ng-deep .p-dropdown:hover {
    background-color: #f8f8ff !important;
    border: none;
  }

  .custom-dropdown {
    border-radius: 9px !important;
    border: none !important;
    font-size: 16px !important;
    background-color: #f8f8ff !important;
    min-height: 55px !important;
  }

  ::ng-deep .p-inputtext {
    font-size: 16px;
  }

  ::ng-deep .p-inputtext:enabled:focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 1px #4b3f72;
    border-color: #4b3f72;
    font-size: 16px;
  }

  ::ng-deep .p-inputtext:enabled:hover {
    border-color: #4b3f72;
  }

  ::ng-deep .p-dropdown-label .p-inputtext {
    border-color: #4b3f72;
  }

  // ::ng-deep .dropdown .p-dropdown,
  // ::ng-deep .p-multiselect {
  //   width: 100%;
  //   height: 100%;
  //   border-radius: 9px !important;
  //   border: none !important;
  //   background-color: #f8f8ff !important;
  //   min-height: 60px !important;
  //   padding-top: 1rem;
  // }

  // ::ng-deep .dropdown .p-dropdown,
  //  ::ng-deep .p-multiselect {
  //   width: 100%;
  //   height: 100%;
  //   border-radius: 9px !important;
  //   border: none !important;
  //   background-color: #f8f8ff !important;
  //   min-height: 30px !important;
  // }
  //  ::ng-deep .dropdown .p-dropdown .p-dropdown-label,
  //  ::ng-deep .p-multiselect .p-multiselect-label {
  //   color: #000000;
  //   font-family: Poppins;
  //   font-size: 16px;
  //   font-weight: 500;
  //   letter-spacing: -0.32px;
  //   line-height: 25px;
  //   padding-top: 0.5rem;
  //   padding-left: 1rem;
  // }
  // ::ng-deep .dropdown .p-dropdown .p-dropdown-label.p-placeholder,
  // ::ng-deep .p-multiselect .p-multiselect-label.p-placeholder {
  //   color: #b5b5c3 !important;
  // }

  .radio-btn input[type='radio'] {
    appearance: none;
  }

  /* The container */
  .container1 {
    display: flex;
    position: absolute;
    // padding-left: 30px;
    margin-bottom: 0px;
    cursor: pointer;
    font-size: 20px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    align-items: center;
  }
  /* Hide the browser's default radio button */
  .container1 input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
  }
  /* Create a custom radio button */
  .checkmark {
    position: absolute;
    // top: 0;
    left: 0;
    height: 16px;
    width: 16px;
    border: 1px solid #4b3f72;
    border-radius: 50%;
  }

  /* When the radio button is selected, add background */
  .container1 input:checked ~ .checkmark {
    background-color: #4b3f72;
  }
  /* Create the indicator (the dot/circle - hidden when not checked) */
  .checkmark:after {
    content: '';
    position: absolute;
    display: none;
  }
  /* Show the indicator (dot/circle) when checked */
  .container1 input:checked ~ .checkmark:after {
    display: block;
  }
  /* Style the indicator (dot/circle) */
  .container1 .checkmark:after {
    top: 3.5px;
    left: 3.5px;
    width: 7px;
    height: 7px;
    border-radius: 50%;
    background: white;
  }

  input[type='radio'] {
    appearance: none;
    cursor: pointer;
  }

  .checkbox-wrapper {
    white-space: nowrap;
  }
  .checkbox {
    vertical-align: middle;
    display: inline-block;
    appearance: auto;
  }
  .checkbox-label {
    white-space: normal;
    display: inline-block;
    font-size: 14px;
  }
  input[type='radio']:checked:before {
    background: #4b3f72;
    color: #ffffff;
    content: '\2713';
    text-align: center;
    appearance: auto;
  }

  input[type='radio']:before {
    border: 1px solid #4b3f72;
    border-radius: 1rem;
    content: '\00a0';
    display: inline-block;
    font: 16px/1em sans-serif;
    height: 16px;
    margin: 0 0.25em 0 0;
    padding: 0;
    vertical-align: top;
    width: 16px;
    appearance: auto;
  }

  .radio-btn input[type='radio']:checked:before {
    background: #4b3f72;
    color: #f6f5f9;
    content: '\25CB';
    font-size: 15px;
    text-align: center;
    appearance: auto;
  }

  .radio-btn input[type='radio']:checked:after {
    top: 3.5px;
    left: 3.5px;
    width: 7px;
    height: 7px;
    border-radius: 50%;
    background: white;
  }

  .radio-btn input[type='radio']:before {
    border: 1px solid #4b3f72;
    border-radius: 1rem;
    content: '\00a0';
    display: inline-block;
    font: 16px/1em sans-serif;
    height: 16px;
    margin: 0 0.25em 0 0;
    padding: 0;
    vertical-align: top;
    width: 16px;
    appearance: auto;
  }

  .form-check-label {
    color: #000000;
    font-family: Poppins;
    font-size: 14px;
    letter-spacing: 0;
    line-height: 21px;
  }

  .content-center {
    width: 80%;
  }

  .header-width-extra {
    width: 40%;
  }

  .header-width {
    width: 18%;
  }

  .icon-width {
    width: 6%;
  }

  .icon-background {
    background-color: #4b3f72 !important;
    padding: 0.2rem;
  }

  ::ng-deep .p-datatable .p-datatable-thead > tr > th {
    height: 20px;
    color: #000000;
    font-family: Poppins;
    font-size: 14px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 21px;
    border-bottom: none;
    background-color: #ecedf6 !important;
  }

  ::ng-deep .p-datatable .p-datatable-tbody > tr > td {
    height: 20px;
    color: #4b3f72;
    font-family: Poppins;
    font-size: 14px;
    letter-spacing: 0;
    line-height: 15px;
    padding: 0.5rem 1rem;
    border: none;
  }

  .align-center {
    text-align: center !important;
  }

  ::ng-deep .pi-icon .pi {
    font-size: 0.5rem;
  }

  ::ng-deep .svg-icon-white .svg-icon svg g [fill] {
    fill: white !important;
  }

  ::ng-deep .p-button {
    color: #4b3f72;
    width: 2.5rem;
    margin-left: 0.5rem;
  }

  ::ng-deep .p-button:enabled:hover {
    color: #4b3f72;
  }

  ::ng-deep .p-button:focus {
    box-shadow: 0 0 0 0rem transparent;
    color: #4b3f72;
  }

  ::ng-deep .p-datatable-tbody tr:last-child > td {
    border-bottom: none;
  }
  ::ng-deep .p-datatable-wrapper {
    border: 1px solid #edeff3;
  }

  ::ng-deep .expanded-table .p-datatable-wrapper {
    border: none !important;
  }
}
.card.card-custom {
  height: auto;
}

@media (max-width: 1199.98px) {
  .content-center {
    width: 100% !important;
  }
}

::ng-deep .p-datatable .p-datatable-tbody > tr {
  border-bottom: 1px solid #f0f1fb;
  vertical-align: initial;
}

::ng-deep .p-datatable .p-datatable-tbody > tr:hover {
  background-color: #f0f1fb !important;
}

.odd-row {
  background-color: #f7f7f7 !important;
}

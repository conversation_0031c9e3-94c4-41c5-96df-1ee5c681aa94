<div class="card card-custom gutter-b" id="create_role_form">
  <app-card-header [cardTitle]="cardTitle" [cardSubTitle]="cardSubTitle" [buttons]="buttons"></app-card-header>
  <div class="card-body create-card">
    <form class="form" autocomplete="off" [formGroup]="createTagCategoryForm" autocomplete="off" novalidate="novalidate" (ngSubmit)="createTagCategoryForm.valid && onSave()">
      <div id="client-detail">
        <kt-auth-notice></kt-auth-notice>
        <div class="mx-auto content-center">
          <div class="row">
            <div class="col-6 pr-md-5">
              <div class="form-group first">
                <label class="form-label">Tag Category</label>
                <input type="text" class="form-control custom" placeholder="e.g. Sports" formControlName="name" />
                <app-form-error [validation]="'required'" [form]="createTagCategoryForm" [controlName]="'name'" [fieldLabel]="'Category Name'"></app-form-error>
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>

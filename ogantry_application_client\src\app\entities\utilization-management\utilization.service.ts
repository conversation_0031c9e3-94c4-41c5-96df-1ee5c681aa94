import { Injectable } from '@angular/core';
import { HttpClient, HttpParams, HttpResponse } from '@angular/common/http';
import { Observable, Subject } from 'rxjs';
import { ApiUrl, AppConstants } from '@shared/constants';
import { OGantryHttpResponse } from '@shared/models';
import * as XLSX from 'xlsx';
import { EmployeeTypesList, GlobalDetailList, EmployeeWiseData, Ids, OpenPositionReportList, ProjectList, RegionList, EmployeeData, PNLPlugs, PNLPlug } from './utilization.model';
import * as FileSaver from 'file-saver';
import jsPDF from 'jspdf';
import 'jspdf-autotable';
import { Position, PositionList, Project } from '@entities/project/project.model';
import { ISavedFilter, PositionTypeList } from '@entities/administration/administration.model';
import { ClientList } from '@entities/client/client.model';
import { Params } from '@angular/router';
import * as ExcelJS from 'exceljs';

@Injectable({
  providedIn: 'root'
})
export class UtilizationService {
  showNewSharedFilter = new Subject<string>();
  showTagsForEditPosition = new Subject<boolean>();

  constructor(private readonly http: HttpClient) {}

  getBenchReport(queryFilter: any): Observable<HttpResponse<OGantryHttpResponse<EmployeeWiseData>>> {
    return this.http.get<OGantryHttpResponse<EmployeeWiseData>>(ApiUrl.benchReport, { params: queryFilter, observe: 'response' });
  }

  getOpenPositionReport(queryFilter: any): Observable<HttpResponse<OGantryHttpResponse<OpenPositionReportList>>> {
    return this.http.get<OGantryHttpResponse<OpenPositionReportList>>(ApiUrl.openPositionReport, { params: queryFilter, observe: 'response' });
  }

  getEmployeeTypes(): Observable<OGantryHttpResponse<EmployeeTypesList>> {
    return this.http.get<OGantryHttpResponse<EmployeeTypesList>>(ApiUrl.employeeTypes);
  }

  getRegions(): Observable<OGantryHttpResponse<RegionList>> {
    return this.http.get<OGantryHttpResponse<RegionList>>(ApiUrl.regions);
  }

  getGlobalDetails(detailName): Observable<OGantryHttpResponse<GlobalDetailList>> {
    return this.http.get<OGantryHttpResponse<GlobalDetailList>>(ApiUrl.globalDetails, { params: { name: detailName } });
  }

  getGroup(requestObject) {
    return this.http.get(ApiUrl.saveFilter, { params: requestObject });
  }

  getPositionList(): Observable<OGantryHttpResponse<PositionList>> {
    return this.http.get<OGantryHttpResponse<PositionList>>(`${ApiUrl.position}`, { params: { visibility: 'Public' } });
  }

  getProjectList(): Observable<OGantryHttpResponse<ProjectList>> {
    return this.http.get<OGantryHttpResponse<ProjectList>>(ApiUrl.project);
  }

  getEmployeeIds(name: string): Observable<Ids> {
    return this.http.get<Ids>(`${ApiUrl.employee}/ids?${name}`);
  }

  getClientIds(name: string): Observable<Ids> {
    return this.http.get<Ids>(`${ApiUrl.client}/ids?${name}`);
  }

  getProjectIds(name: string): Observable<Ids> {
    return this.http.get<Ids>(`${ApiUrl.project}/ids?${name}`);
  }

  exportPdf(col: any[], data: any[], filename: string, no: number) {
    const doc = new jsPDF('l', 'pt', '', true);
    const colLength: number = col.length;
    const targetKeyProject = '1~project';
    const netProfitValue = 'Net Profit';

    doc['autoTable'](col, data, {
      startY: 40,
      margin: 0,
      tableWidth: 'screenwidth',
      cellWidth: 'screenwidth',
      showHead: 'everyPage',
      tableLineColor: 200,
      tableLineWidth: 0,
      didParseCell: function (table) {
        if (
          table.section === 'body' &&
          (table?.cell?.raw?.includes('%') || table?.cell?.raw?.includes('$') || table?.cell?.raw?.includes('--') || table.column.dataKey.startsWith('Hours In'))
        ) {
          table.cell.styles.halign = 'right';
        }
        if (table.column.dataKey === targetKeyProject) {
          if (table?.cell?.raw === 'Revenue' || table?.cell?.raw === netProfitValue) {
            Object.keys(table.row.cells).forEach((key) => {
              table.row.cells[key].styles.fontStyle = 'bold';
            });
          }
        }
        if (table.section === 'head') {
          for (let i = no; i < colLength; i++) {
            if (table.column.index === i) {
              table.cell.styles.halign = 'right';
            }
          }
        }
        if (colLength > 6) {
          table.cell.styles.fontSize = 5;
        }
      },
      styles: {
        overflowX: 'scroll',
        overflow: 'linebreak',
        cellWidth: 'wrap',
        fontSize: 10,
        cellPadding: 2,
        overflowColumns: 'linebreak'
      }
    });
    doc.save(`${filename}.pdf`);
  }
  exportPdfManageTimesheet(col: any[], data: any[], filename: string, no: number, startDate?: string, endDate?: string) {
    const doc = new jsPDF('l', 'mm', [450, 500], true);
    startDate && endDate ? doc.text(`Date Range: ${startDate} to ${endDate}`, 15, 30) : '';
    const colLength: number = col.length;
    doc['autoTable'](col, data, {
      startY: 40,
      margin: 0,
      tableWidth: 'screenwidth',
      cellWidth: 'screenwidth',
      showHead: 'everyPage',
      tableLineColor: 200,
      tableLineWidth: 0,
      didParseCell: function (table) {
        if (
          table.section === 'body' &&
          (table?.cell?.raw?.toString().includes('%') ||
            table?.cell?.raw?.toString().includes('$') ||
            table?.cell?.raw?.toString().includes('--') ||
            table.column.dataKey.toString().startsWith('Hours In'))
        ) {
          table.cell.styles.halign = 'right';
        }
        if (table.section === 'head') {
          for (let i = no; i < colLength; i++) {
            if (table.column.index === i) {
              table.cell.styles.halign = 'right';
            }
          }
        }
        if (colLength > 6) {
          table.cell.styles.fontSize = 5;
        }
      },
      styles: {
        overflowX: 'scroll',
        overflow: 'linebreak',
        cellWidth: 'wrap',
        fontSize: 10,
        cellPadding: 2,
        overflowColumns: 'linebreak'
      }
    });
    doc.save(`${filename}.pdf`);
  }

  exportToCsv(rows: object[], fileName: string, columns?: string[], startDate?: string, endDate?: string): string {
    if (!rows || !rows.length) {
      return;
    }
    const dateRangeRow = startDate && endDate ? [`Date Range:`, `${startDate} to ${endDate}`, ...new Array(columns.length - 3).fill('')] : '';
    const separator = ',';
    const keys = [];
    columns.forEach((col) => {
      if (rows[0][col] !== undefined) {
        keys.push(col);
      }
    });
    let csvContent =
      keys.join(separator) +
      '\n' +
      rows
        .map((row) => {
          return keys
            .map((k) => {
              let cell = row[k] === null || row[k] === undefined ? '' : row[k];
              cell = cell instanceof Date ? cell.toLocaleString() : cell.toString().replace(/"/g, '""');
              if (cell.search(/("|,|\n)/g) >= 0) {
                cell = `"${cell}"`;
              }
              return cell;
            })
            .join(separator);
        })
        .join('\n');
    dateRangeRow ? (csvContent = dateRangeRow + '\n' + csvContent) : '';
    this.saveAsFile(csvContent, `${fileName}.csv`, 'text/plain;charset=utf-8');
  }

  private saveAsFile(buffer: any, fileName: string, fileType: string): void {
    const data: Blob = new Blob([buffer], { type: fileType });
    FileSaver.saveAs(data, fileName);
  }

  exportExcel(headers, reportData, filename, originalExportReportData = []) {
    import('xlsx').then((xlsx) => {
      if (originalExportReportData.length) {
        const data1 = [...headers, ...originalExportReportData];
        const worksheet1 = xlsx.utils.json_to_sheet(data1, {
          skipHeader: true
        });
        const data2 = [...headers, ...reportData];
        const worksheet2 = xlsx.utils.json_to_sheet(data2, {
          skipHeader: true
        });
        const workbook = {
          Sheets: {
            'Hierarchical View': worksheet1,
            'Filter View': worksheet2
          },
          SheetNames: ['Hierarchical View', 'Filter View']
        };
        const excelBuffer: any = xlsx.write(workbook, {
          bookType: 'xlsx',
          type: 'array'
        });
        this.saveAsExcelFile(excelBuffer, filename);
      } else {
        const data = [...headers, ...reportData];
        const worksheet = xlsx.utils.json_to_sheet(data, { skipHeader: true });
        const workbook = { Sheets: { data: worksheet }, SheetNames: ['data'] };
        const excelBuffer: any = xlsx.write(workbook, {
          bookType: 'xlsx',
          type: 'array'
        });
        this.saveAsExcelFile(excelBuffer, filename);
      }
    });
  }

  /**
   * Enhanced Excel export functionality using ExcelJS library
   * Primarily used for Profit & Loss (P&L) report formatting
   */
  async exportFormattedExcel(headers, reportData, filename, originalExportReportData = []): Promise<void> {
    const workbook = new ExcelJS.Workbook();
    const skipHeaderName = ['1~project', '2~type'];
    const removeColForFinancialView = ['3~subType', '4~name'];
    const financialSummary = 'Financial Summary';

    const createAndStyleWorksheet = (workbook, name, data) => {
      const worksheet = workbook.addWorksheet(name);

      let headerObject = headers[0];

      //In the financial view removed columns and convert header toUppercase
      if (name === financialSummary) {
        headerObject = Object.keys(headers[0])
          .filter((headerKey) => !removeColForFinancialView.includes(headerKey))
          .reduce((obj, key) => {
            obj[key] = headers[0][key].toString().toUpperCase();
            return obj;
          }, {});

        skipHeaderName.forEach((headerKey) => {
          if (headerObject.hasOwnProperty(headerKey)) {
            headerObject[headerKey] = '';
          }
        });
      }
      const headerKeys = Object.keys(headerObject);

      worksheet.columns = headerKeys.map((key) => ({
        header: headerObject[key],
        key: key,
        width: 15
      }));

      worksheet.addRows(data);

      let headerRow = worksheet.getRow(1);
      // Add style to column headers
      headerRow.eachCell((cell, colNumber) => {
        if (!cell.value) return;

        cell.font = {
          bold: true,
          color: { argb: 'FF000000' } // black
        };

        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFD3D3D3' }
        };

        cell.alignment = {
          vertical: 'middle',
          horizontal: 'center'
        };
      });

      const targetKeyProject = '1~project';
      const targetKeyType = '2~type';
      const acceptProjectType = ['Revenue Adjustment', 'Bench', 'Projects', 'Work Exceptions', 'COGS Adjustment'];
      const netProfitValue = 'SG&A';
      const COGS = 'COGS Adjustment';

      worksheet.getColumn('2~type').width = 25;

      //Remove rows that are not required for the Financial Summary view
      if (name === financialSummary) {
        const rowsToRemove: number[] = [];
        worksheet.eachRow((row, rowNumber) => {
          if (rowNumber === 1) return;
          const projectCell = row.getCell(targetKeyProject);
          const typeCell = row.getCell(targetKeyType);
          if ((!projectCell.value && !typeCell.value) || (!projectCell.value && !acceptProjectType.includes(typeCell.value))) {
            rowsToRemove.push(rowNumber);
          }
        });
        rowsToRemove.reverse().forEach((rowNumber) => {
          worksheet.spliceRows(rowNumber, 1);
        });
      } else {
        worksheet.getColumn('3~subType').width = 25;
        worksheet.getColumn('4~name').width = 20;
      }

      // Add styles to cells & rows based on conditions
      worksheet.eachRow((row, rowNumber) => {
        if (rowNumber === 1) return; // Skip header row

        const projectCell = row.getCell(targetKeyProject);
        const typeCell = row.getCell(targetKeyType);

        row.eachCell((cell, colNumber) => {
          if (cell.value.toString().includes('%') || cell.value.toString().includes('$')) {
            cell.alignment = {
              vertical: 'middle',
              horizontal: 'right'
            };

            const cellValue = cell.value.toString();

            if (cellValue.includes('$')) {
              const numericValue = parseFloat(cellValue?.replace(AppConstants.nonNumericRegex, ''));
              cell.value = numericValue;
              cell.numFmt = '"$"#,##0.00';
            } else if (cellValue.includes('%')) {
              const cleanedValue = cellValue.replace(AppConstants.nonNumericRegex, '');
              const numericValue = parseFloat(cleanedValue) / 100;
              cell.value = numericValue;
              const decimalMatch = cleanedValue.match(AppConstants.regexForDecimal);
              if (decimalMatch) {
                cell.numFmt = '0.0%';
              } else {
                cell.numFmt = '0%';
              }
            }
          }
        });

        if (projectCell.value === 'Revenue' || projectCell.value === 'Net Profit') {
          row.eachCell((cell) => {
            cell.font = {
              bold: true,
              color: { argb: 'FF000000' } // black
            };
          });
        } else if (projectCell.value === netProfitValue) {
          row.eachCell((cell) => {
            if (!cell.value || cell.value === netProfitValue) return;
            cell.border = {
              bottom: { style: 'double', color: { argb: '000000' } }
            };
          });
        } else if (typeCell.value === COGS) {
          row.eachCell((cell) => {
            if (!cell.value || cell.value === COGS) return;
            cell.border = {
              bottom: { style: 'thin', color: { argb: '000000' } }
            };
          });
        }
      });

      return worksheet;
    };

    if (originalExportReportData.length) {
      createAndStyleWorksheet(workbook, 'Financial Summary', originalExportReportData);
      createAndStyleWorksheet(workbook, 'Hierarchical View', originalExportReportData);
      createAndStyleWorksheet(workbook, 'Filter View', reportData);
    } else {
      createAndStyleWorksheet(workbook, 'Data', reportData);
    }

    const buffer = await workbook.xlsx.writeBuffer();
    this.saveAsExcelFile(buffer, filename);
  }

  saveAsExcelFile(buffer: any, fileName: string): void {
    import('file-saver').then((FileSaver) => {
      const EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
      const EXCEL_EXTENSION = '.xlsx';
      this.saveAsFile(buffer, fileName + '_export_' + new Date().getTime() + EXCEL_EXTENSION, EXCEL_TYPE);
    });
  }

  getEmployee(id): Observable<OGantryHttpResponse<EmployeeData>> {
    return this.http.get<OGantryHttpResponse<EmployeeData>>(`${ApiUrl.employee}/${id}`);
  }

  getProject(id): Observable<OGantryHttpResponse<Project>> {
    return this.http.get<OGantryHttpResponse<Project>>(`${ApiUrl.project}/${id}`);
  }

  getPositionTypes(): Observable<OGantryHttpResponse<PositionTypeList>> {
    return this.http.get<OGantryHttpResponse<PositionTypeList>>(`${ApiUrl.positionType}`);
  }
  createFilter(requestObject): Observable<ISavedFilter> {
    return this.http.post<ISavedFilter>(ApiUrl.saveFilter, {
      query_filter: requestObject
    });
  }
  updateFilter(id, requestObject): Observable<ISavedFilter> {
    return this.http.put<ISavedFilter>(`${ApiUrl.saveFilter}/${id}`, {
      query_filter: requestObject.query_filter
    });
  }

  deleteStoredFilters(id) {
    return this.http.delete(`${ApiUrl.saveFilter}/${id}`);
  }
  getStoredFilters(requestObject) {
    return this.http.get(ApiUrl.saveFilter, { params: requestObject });
  }
  getActiveEmployees(id, params): Observable<HttpResponse<OGantryHttpResponse<PositionList>>> {
    return this.http.get<OGantryHttpResponse<PositionList>>(`${ApiUrl.activeEmployeePosition}/${id}`, { params: params, observe: 'response' });
  }
  getClientData(): Observable<HttpResponse<OGantryHttpResponse<ClientList>>> {
    return this.http.get<OGantryHttpResponse<ClientList>>(ApiUrl.client, {
      params: { order_by: 'asc:name' },
      observe: 'response'
    });
  }
  createPNLPlug(plugObj): Observable<OGantryHttpResponse<PNLPlug>> {
    return this.http.post<OGantryHttpResponse<PNLPlug>>(ApiUrl.pnlPlug, {
      pnl_plug: plugObj
    });
  }
  updatePNLPlug(plugObj, id): Observable<OGantryHttpResponse<PNLPlug>> {
    return this.http.put<OGantryHttpResponse<PNLPlug>>(`${ApiUrl.pnlPlug}/${id}`, { pnl_plug: plugObj });
  }
  getPNLPlugList(params): Observable<HttpResponse<OGantryHttpResponse<PNLPlugs>>> {
    return this.http.get<OGantryHttpResponse<PNLPlugs>>(ApiUrl.pnlPlug, {
      params: params,
      observe: 'response'
    });
  }

  updatePosition(positionObject): Observable<OGantryHttpResponse<Position>> {
    if (positionObject?.id) {
      return this.http.put<OGantryHttpResponse<Position>>(`${ApiUrl.position}/${positionObject.id}`, { position: positionObject });
    }
    return this.http.post<OGantryHttpResponse<Position>>(`${ApiUrl.position}`, {
      position: positionObject
    });
  }

  getPositionById(id: number, params: HttpParams): Observable<OGantryHttpResponse<any>> {
    const url = `${ApiUrl.timeEntriesByPostion}/${id}`;
    return this.http.get<OGantryHttpResponse<any>>(url, { params });
  }

  extractCommentsByPosition(apiResponse) {
    const commentsByPosition = {};

    // Iterate over positions
    apiResponse.position.forEach((positionData) => {
      const position = positionData.position;
      const positionId = position.id;
      const timeEntries = position.time_entries || [];

      // Map comments to position_id
      commentsByPosition[positionId] = timeEntries.map((entry) => ({
        date: entry.time_entry.date,
        comment: entry.time_entry.comment
      }));
    });

    return commentsByPosition;
  }

  exportExcelWithComments(headers, reportData, filename, originalExportReportData = [], comments = [], startDate?: string, endDate?: string) {
    import('xlsx').then((xlsx) => {
      const createWorksheetWithComments = (data, comments) => {
        // Create worksheet
        const worksheet = xlsx.utils.json_to_sheet(data, { skipHeader: true });

        // Add comments if provided
        if (comments.length) {
          Object.keys(worksheet).forEach((cell) => {
            if (cell.startsWith('!')) return; // Skip metadata like !ref
            const cellAddress = xlsx.utils.decode_cell(cell);
            const row = cellAddress.r;
            const col = cellAddress.c;

            if (row >= 2) {
              // Check if comments are provided for this cell
              if (comments[row - 1] && comments[row - 1][col]) {
                if (!worksheet[cell].c) worksheet[cell].c = [];
                worksheet[cell].c.push({
                  a: 'Author', // Optional author
                  t: comments[row - 1][col] // Comment text
                });
                worksheet[cell].c.hidden = true;
              }
            }
          });

          // Enable comments in the worksheet
          worksheet['!comments'] = [];
        }

        return worksheet;
      };

      if (startDate && endDate) {
        let dateRangeRow = {};
        const keys = Object.keys(headers[0]);
        dateRangeRow[keys[0]] = 'Date Range:';
        dateRangeRow[keys[1]] = `${startDate} to ${endDate}`;

        keys.slice(2).forEach((key) => {
          dateRangeRow[key] = '';
        });

        headers.unshift(dateRangeRow);
      }

      if (originalExportReportData.length) {
        const data1 = [...headers, ...originalExportReportData];
        const worksheet1 = createWorksheetWithComments(data1, comments);
        const data2 = [...headers, ...reportData];
        const worksheet2 = createWorksheetWithComments(data2, comments);

        const workbook = {
          Sheets: {
            'Hierarchical View': worksheet1,
            'Filter View': worksheet2
          },
          SheetNames: ['Hierarchical View', 'Filter View']
        };
        const excelBuffer: any = xlsx.write(workbook, {
          bookType: 'xlsx',
          type: 'array'
        });
        this.saveAsExcelFile(excelBuffer, filename);
      } else {
        const data = [...headers, ...reportData];
        const worksheet = createWorksheetWithComments(data, comments);
        const workbook = { Sheets: { data: worksheet }, SheetNames: ['data'] };
        const excelBuffer: any = xlsx.write(workbook, {
          bookType: 'xlsx',
          type: 'array'
        });
        this.saveAsExcelFile(excelBuffer, filename);
      }
    });
  }
}

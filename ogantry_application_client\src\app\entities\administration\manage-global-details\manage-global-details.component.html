<div class="card card-custom gutter-b" id="manageGlobalDetailField">
  <app-card-header [cardTitle]="cardTitle" [buttons]="buttons"></app-card-header>
  <div class="card-body">
    <p-table
      responsiveLayout="scroll"
      #dt
      [value]="globalDetailsFields"
      [lazy]="true"
      (onLazyLoad)="getGlobalDetail()"
      dataKey="id"
      [rows]="10"
      [loading]="loading"
      styleClass="p-datatable-customers"
      [filterDelay]="0"
      [sortField]="'name'"
      [sortOrder]="-1"
    >
      <ng-template pTemplate="header">
        <tr>
          <th id="category" class="header-width">Name</th>
          <th class="json-value" colspan="2">JSON</th>
          <th>Action</th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-field let-rowIndex="rowIndex">
        <tr>
          <td>{{ field?.global_detail?.name }}</td>
          <td colspan="2" class="overflow-ellipsis">
            {{ field?.global_detail?.extended_fields | json }}
          </td>
          <td class="action-width">
            <a title="Edit" class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary cursor-pointer" (click)="onEditField(field?.global_detail?.id)">
              <span inlineSVG="assets/media/svg/icons/edit.svg" cacheSVG="true" class="svg-icon svg-icon-md"> </span>
            </a>
          </td>
        </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="4" class="center-align">No Data found.</td>
        </tr>
      </ng-template>
    </p-table>
  </div>
</div>

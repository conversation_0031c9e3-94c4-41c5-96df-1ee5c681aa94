import { Component, OnInit } from '@angular/core';
import { AdministrationService } from '../administration.service';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { ButtonParams } from '@shared/models';
import { ActivatedRoute, Route, Router } from '@angular/router';
import { AdminOnlyPermission } from '@shared/models/permission.enum';

@Component({
  selector: 'app-manage-global-details',
  templateUrl: './manage-global-details.component.html',
  styleUrls: ['./manage-global-details.component.scss']
})
export class ManageGlobalDetailsComponent extends SflBaseComponent implements OnInit {
  loading = false;
  globalDetailsFields: any;

  cardTitle = 'Manage Global Detail';
  buttons: ButtonParams[] = [
    {
      btnClass: 'btn-save',
      btnText: 'Add New',
      redirectPath: this.appRoutes.CREATE_GLOBAL_DETIAL,
      permissions: [AdminOnlyPermission.MANAGE_GLOBAL_DETAIL]
    }
  ];

  constructor(private readonly adminService: AdministrationService, private readonly router: Router) {
    super();
  }

  ngOnInit(): void {
    this.getGlobalDetail();
  }

  getGlobalDetail(): void {
    this.loading = true;
    this.subscriptionManager.add(
      this.adminService.getGlobalDetailsFields().subscribe(
        (res) => {
          this.loading = false;
          if (res?.data?.global_details) {
            this.globalDetailsFields = res?.data?.global_details || [];
          }
        },
        () => (this.loading = false)
      )
    );
  }

  onEditField(id: number): void {
    this.router.navigate([this.appRoutes.CREATE_GLOBAL_DETIAL, id]);
  }
}

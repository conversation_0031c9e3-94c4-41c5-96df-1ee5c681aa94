import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
// import { Employee } from '../administration.model';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { ButtonParams, OGantryHttpResponse } from '@shared/models';
import { AdministrationService } from '@entities/administration/administration.service';
import { KtDialogService } from '@shared/services';
import { APP_ROUTES } from '@shared/constants';
import { AuthNoticeService } from '@auth/auth-notice/auth-notice.service';
import { DatePipe } from '@angular/common';
import moment from 'moment';
import { Employee } from '@entities/administration/administration.model';

@Component({
  selector: 'app-terminate-employee',
  templateUrl: './terminate-employee.component.html',
  styleUrls: ['./terminate-employee.component.scss'],
  providers: [DatePipe]
})
export class TerminateEmployeeComponent extends SflBaseComponent implements OnInit {
  cardTitle = 'Terminate Employee';
  cardSubTitle = null;
  buttons: ButtonParams[] = [];
  terminateEmployeeForm: FormGroup;
  employeeId: number;
  employee: Employee;
  showDialogBox = false;
  maxDate = null;
  minDate = null;
  showAccordian = false;
  constructor(
    private readonly administrationService: AdministrationService,
    private readonly authNoticeService: AuthNoticeService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly ktDialogService: KtDialogService,
    private readonly datePipe: DatePipe,
    private readonly router: Router
  ) {
    super();
  }

  ngOnInit(): void {
    this.loading$.next(false);
    this.initForm();
    this.getParams();
  }

  ngOnDestroy() {
    this.authNoticeService.setNotice(null);
    this.ktDialogService.hide();
  }

  initForm() {
    this.terminateEmployeeForm = new FormGroup({
      termination_date: new FormControl('', Validators.required)
    });
  }
  getEmployee() {
    this.loading$.next(true);
    this.subscriptionManager.add(
      this.administrationService.getEmployee(this.employeeId).subscribe(
        (res) => {
          this.employee = res.data;
          this.maxDate = moment(this.employee.employee.end_date).toDate();
          this.minDate = moment(this.employee.employee.start_date).toDate();
          this.loading$.next(false);
        },
        () => this.loading$.next(false)
      )
    );
  }
  getParams() {
    this.activatedRoute.params.subscribe((params: Params) => {
      this.employeeId = params.employeeId;
      if (this.employeeId) {
        this.getEmployee();
        this.showAccordian = true;
      }
      this.setBtnParams();
    });
  }
  setBtnParams() {
    this.buttons = [
      {
        btnClass: 'btn btn-cancel',
        btnText: 'Cancel',
        redirectPath: this.appRoutes.MANAGE_EMPLOYEE
      },
      {
        btnClass: 'btn-save',
        btnText: 'Save',
        btnType: 'submit',
        action: this.showDialog.bind(this),
        loading: this.isSubmitting
      }
    ];
  }

  terminateEmployeeApi() {
    this.isSubmitting = true;
    const employeeData = this.terminateEmployeeForm.value;
    employeeData.termination_date = this.datePipe.transform(new Date(employeeData.termination_date), 'yyyy-MM-dd');
    this.subscriptionManager.add(
      this.administrationService.terminateEmployee(employeeData, this.employeeId).subscribe(
        (res) => {
          this.closeModal();
          this.isSubmitting = false;
          this.router.navigateByUrl(APP_ROUTES.MANAGE_EMPLOYEE);
        },
        (err) => this.onError(err)
      )
    );
  }

  onError(err) {
    this.isSubmitting = false;
    this.closeModal();
    const error: OGantryHttpResponse<Employee> = err.error;
    this.authNoticeService.setNotice(error.errors, 'danger');
  }

  closeModal() {
    this.showDialogBox = false;
  }
  showDialog() {
    if (!this.checkFormForValidation(this.terminateEmployeeForm)) {
      this.showDialogBox = true;
    }
  }

  getSkillSet(employee) {
    let a = [];
    employee?.employee?.position_types?.map((type) => {
      a.push(type.position_type.name);
    });
    return a.join(',').replace(',', ', ');
  }
}

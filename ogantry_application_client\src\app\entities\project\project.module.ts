import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { ProjectRoutingModule } from './project-routing.module';
import { CreateProjectComponent } from './create-project/create-project.component';
import { ManageProjectComponent } from './manage-project/manage-project.component';
import { TableModule } from 'primeng/table';
import { DropdownModule } from 'primeng/dropdown';
import { InputTextModule } from 'primeng/inputtext';
import { SharedModule } from '@shared/shared.module';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { InlineSVGModule } from 'ng-inline-svg';
import { MatSidenavModule } from '@angular/material/sidenav';
import { CalendarModule } from 'primeng/calendar';
import { PartialsModule } from '@entities/partials/partials.module';
import { ProjectCostComponent } from './project-cost/project-cost.component';
import { ProjectSetupComponent } from './project-setup/project-setup.component';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatSelectModule } from '@angular/material/select';
import { PositionSetupComponent } from './position-setup/position-setup.component';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { FinancialReviewComponent } from './financial-review/financial-review.component';
import { TreeTableModule } from 'primeng/treetable';
import { ProjectOverviewComponent } from './project-overview/project-overview.component';
import { DialogModule } from 'primeng/dialog';
import { MultiSelectModule } from 'primeng/multiselect';
import { InputNumberModule } from 'primeng/inputnumber';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { BadgeModule } from 'primeng/badge';
import { TreeSelectModule } from 'primeng/treeselect';
import { CheckboxModule } from 'primeng/checkbox';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ChipModule } from 'primeng/chip';

@NgModule({
  declarations: [
    CreateProjectComponent,
    ManageProjectComponent,
    ProjectCostComponent,
    ProjectSetupComponent,
    PositionSetupComponent,
    FinancialReviewComponent,
    ProjectOverviewComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    ProjectRoutingModule,
    SharedModule,
    TableModule,
    DropdownModule,
    InputTextModule,
    MatSlideToggleModule,
    InlineSVGModule,
    MatSidenavModule,
    CalendarModule,
    PartialsModule,
    MatFormFieldModule,
    MatDatepickerModule,
    MatSelectModule,
    NgbModule,
    TreeTableModule,
    DialogModule,
    MultiSelectModule,
    InputNumberModule,
    InputTextareaModule,
    BadgeModule,
    TreeSelectModule,
    CheckboxModule,
    SharedModule,
    ConfirmDialogModule,
    ChipModule
  ]
})
export class ProjectModule { }

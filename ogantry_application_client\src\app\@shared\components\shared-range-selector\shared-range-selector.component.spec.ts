import { ComponentFixture, TestBed } from '@angular/core/testing';

import { SharedRangeSelectorComponent } from './shared-range-selector.component';

describe('SharedRangeSelectorComponent', () => {
  let component: SharedRangeSelectorComponent;
  let fixture: ComponentFixture<SharedRangeSelectorComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [SharedRangeSelectorComponent]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(SharedRangeSelectorComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});

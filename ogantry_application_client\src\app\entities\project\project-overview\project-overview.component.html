<div class="card">
  <div class="card-body pt-0">
    <div class="row">
      <div class="col-4">
        <div class="card card1">
          <div class="card-body">
            <div class="card-title mb-12">
              <a href="javascript:;void" class="btn-sm">
                <span title="Add Expense" [inlineSVG]="'assets/media/svg/icons/check.svg'" cacheSVG="true" class="svg-icon"> </span>
              </a>
              Project Margin
            </div>
            <div class="card-subtitle ml-3">Over Target</div>
          </div>
        </div>
      </div>
      <div class="col-4">
        <div class="card card2">
          <div class="card-body">
            <div class="card-title">
              <div class="row">
                <div class="col-8">
                  <a href="javascript:;void" class="btn-sm">
                    <span title="Add Expense" [inlineSVG]="'assets/media/svg/icons/uncheck.svg'" cacheSVG="true" class="svg-icon"> </span>
                  </a>
                  Project Expenses
                </div>
                <div class="col-4">
                  <button type="button" class="btn-save float-right" (click)="showAcknowledgeInput()">Acknowledge</button>
                </div>
              </div>
            </div>
            <div class="card-subtitle ml-3">Below Range</div>
          </div>
        </div>
      </div>
      <div class="col-4">
        <div class="card card3">
          <div class="card-body">
            <div class="card-title mb-12">
              <a href="javascript:;void" class="btn-sm">
                <span title="Add Expense" [inlineSVG]="'assets/media/svg/icons/warning.svg'" cacheSVG="true" class="svg-icon"> </span>
              </a>
              Warning
            </div>
            <div class="card-subtitle ml-3">Multiple Partially alocated people</div>
          </div>
        </div>
      </div>
    </div>
    <div class="row mt-4" *ngIf="showAcknowledge">
      <div class="col-12">
        <div class="form-group">
          <textarea class="form-control custom" placeholder="Type acknowledge..." rows="4"></textarea>
        </div>
      </div>
    </div>
  </div>
</div>

@import 'src/assets/sass/components/variables.demo';

#project_setup_form {
  ::ng-deep .kt-wizard-v4 .kt-wizard-v4__wrapper .kt-form {
    width: 100% !important;
  }

  .add-new {
    text-decoration: underline;
    margin-left: 1rem;
  }

  .form-check-label {
    height: 20px;
    color: $black;
    font-family: Poppins;
    font-size: 15px;
    letter-spacing: 0;
    line-height: 21px;
    margin-right: 2rem;
  }

  ::ng-deep .mat-date-range-input-start-wrapper .mat-date-range-input-inner {
    top: -2px !important;
  }

  .form-field {
    padding-top: 20px;
  }

  .btn-plus {
    color: $primary-inverse !important;
    background-color: $primary !important;
    border-color: $primary !important;
  }

  .add-expense {
    text-align: left;
    padding-top: 0.5rem;
  }

  .project-expense {
    height: 19px;
    color: $black;
    font-family: Poppins;
    font-size: 13px;
    font-weight: 500;
    letter-spacing: -0.26px;
    line-height: 20px;
    margin-left: 1rem;
  }

  .align-right {
    text-align: right;
  }

  input[type='radio'] {
    display: none;
  }

  input[type='radio']:checked + label:before {
    background: $primary;
    color: $primary-inverse;
    content: '\2713';
    text-align: center;
  }

  input[type='radio'] + label:before {
    border: 1px solid $primary;
    border-radius: 1rem;
    content: '\00a0';
    display: inline-block;
    font: 16px/1em sans-serif;
    height: 16px;
    margin: 0.2em 0.25em 0 0;
    padding: 0;
    vertical-align: top;
    width: 16px;
  }

  .input-group-prepend {
    background-color: $light-purple-bg;
    border: none;
    border-radius: 9px;
  }

  .input-group-text {
    border: none;
    color: $medium-gray;
  }

  .amount-form-control {
    border: none;
    background-color: $light-bg;
    min-height: 60px !important;
    justify-content: left;
    display: flex;
    padding: 1rem;
  }

  ::ng-deep .p-inputnumber-input {
    background-color: $light-bg;
    padding: 0;
  }

  ::ng-deep .p-inputtext:enabled:hover,
  ::ng-deep .p-inputtext:enabled:focus {
    border: none;
    box-shadow: none !important;
  }

  .btn.dropdown-btn {
    color: $light-gray;
    border: 0;
    background-color: $light-bg;
    height: 44px;
    width: 100%;
    text-align: left;
  }

  .btn i {
    font-size: 1rem;
  }

  .dropdown-text {
    color: $black !important;
  }

  i {
    height: 18px;
    color: $light-gray !important;
    float: right;
  }

  .btn.btn-outline-primary:hover,
  .btn.btn-outline-primary.dropdown-toggle {
    border: 0;
    background-color: $light-bg !important;
  }

  .show,
  .btn.btn-outline-primary:hover {
    color: $light-gray !important;
  }

  .dropdown,
  .dropdown-menu {
    width: 100%;
  }

  .card-body {
    padding: 1rem;
    padding-right: 3rem;
    padding-left: 3rem;
  }

  ::ng-deep .client-dropdown2 {
    min-height: 60px !important;
    .dropdown-menu.show {
      height: 12rem;
      overflow-y: auto;
    }
  }
  .typehead {
    height: 48px;
    border: 0;
    min-height: 60px !important;
    background-color: $light-bg !important;
  }

  ::ng-deep .dropdown-menu {
    max-height: 18rem;
    overflow-y: auto;
    min-width: 94% !important;
  }
  .btn-success {
    border-color: $primary !important;
    background-color: $primary !important;
  }

  .btn-success:hover {
    background-color: $primary-hover !important;
    border-color: $primary-hover !important;
  }

  .teamRate {
    height: 40px;
    width: 466px;
    color: $black;
    font-family: Poppins;
    font-size: 14px;
    letter-spacing: 0;
    line-height: 21px;
  }

  .dropdown-menu::-webkit-scrollbar {
    width: 0.5em !important;
  }

  .dropdown-menu::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3) !important;
  }

  .dropdown-menu::-webkit-scrollbar-thumb {
    background-color: darkgray !important;
    outline: 1px solid slategray !important;
  }

  .header-width {
    width: 17%;
  }

  .action-width {
    width: 15%;
  }

  .center-align {
    text-align: center !important;
  }

  .editRow {
    border: 1px solid #ced4da !important;
  }

  .scrollable-content {
    max-height: calc((var(--fixed-content-height, 1vh) * 100) - 120px);
    overflow: auto;
  }
  .btn-pos {
    position: fixed;
    top: calc(100vh - 105px);
    right: 0;
  }
  .btn-height {
    height: 40px;
    line-height: 1.2;
  }

  ::ng-deep .position-rel small {
    position: absolute;
  }

  ::ng-deep .p-datatable .p-datatable-loading-overlay {
    top: 80px;
    background-color: transparent;
  }

  ::ng-deep .p-datatable .p-datatable-thead > tr > th {
    height: 20px;
    color: $primary;
    font-family: Poppins;
    font-size: 14px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 21px;
    border-bottom: none;
    padding: 0.5rem 0.5rem;
    background-color: $light-purple-bg-2 !important;
  }

  ::ng-deep .p-datatable .p-datatable-tbody > tr > td {
    height: 20px;
    width: 123px;
    color: $black;
    font-family: Poppins;
    font-size: 12px;
    letter-spacing: 0;
    line-height: 15px;
    padding: 0.5rem 0.5rem;
    font-weight: 500;
  }

  ::ng-deep .position-rel .p-calendar .p-inputtext {
    border: 1px solid #ced4da !important;
    border-radius: 6px !important;
  }
  ::ng-deep .accordion .card .card-header {
    background-color: transparent;
    padding: 0.5rem;
  }
  ::ng-deep .accordion > .card {
    border: 0;
  }
  h5 {
    color: $black;
    font-family: Poppins;
    font-size: 14px;
    font-weight: 600;
    letter-spacing: 0;
    line-height: 21px;
  }
  .overlay {
    position: relative;
    .overlay-wrapper {
      opacity: 0.3;
    }
    i {
      color: $primary !important;
    }
  }
  .overlay .overlay-layer {
    opacity: 1;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    align-items: center;
    background-color: transparent;
    flex-direction: column;
    .text {
      height: 20px;
      width: 586px;
      color: $primary;
      font-family: Poppins;
      font-size: 14px;
      font-weight: 500;
      letter-spacing: 0;
      line-height: 21px;
      text-align: center;
    }
  }
  .header-width-plus {
    width: 5%;
  }

  ::ng-deep .svg-icon-white .svg-icon svg g [fill] {
    fill: $primary-inverse !important;
  }
  .background-white {
    background-color: $primary-inverse !important;
  }
}
@media (max-width: 767.98px) {
  .card-body {
    padding: 15px !important;
  }
  .form-check-inline {
    padding-bottom: 15px;
  }
}

@media (max-width: 1199.98px) {
  .card-body {
    padding: 15px !important;
  }
}

@media (max-width: 500px) {
  .btn-pos {
    top: auto !important;
    position: relative !important;
  }
  .scrollable-content {
    max-height: calc((var(--fixed-content-height, 1vh) * 100) - 215px) !important;
    overflow: auto !important;
  }
}

.adjust-revenue-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 10px;
  background-color: $primary;
  border-color: $primary;
  color: $primary-inverse;
  font-size: 14px;
  font-weight: 500;

  i {
    color: $primary-inverse !important;
    font-size: 18px;
  }

  &:hover {
    background-color: $primary-hover;
    border-color: $primary-hover;
  }
}

::ng-deep .lock-dialog {
  ::ng-deep .p-dialog {
    width: 30vw;
  }
  .form-group {
    padding-bottom: 0;
    &:first-child {
      padding-top: 0;
    }
  }
  .text {
    color: $medium-gray;
    font-family: Poppins;
    font-size: 12px;
    letter-spacing: 0;
    line-height: 18px;
  }
  ::ng-deep .p-inputgroup {
    border-radius: 9px !important;
    border: none !important;
    background-color: $light-bg !important;
    height: 60px;
    width: 100%;
  }

  ::ng-deep .p-inputgroup .p-inputtext {
    border: none !important;
    background-color: $light-bg !important;
    padding-left: 1rem;
  }

  ::ng-deep .p-inputgroup-addon {
    border: 0 !important;
    width: 4rem;
  }

  ::ng-deep .p-inputtext:enabled:focus {
    box-shadow: none;
  }
}

::ng-deep .retainer-plug-dialog {
  width: 40vw !important;
  min-width: 400px !important;

  .p-dialog-header .p-dialog-title {
    color: $black;
    font-weight: 500;
  }
  .p-dialog-content {
    max-height: 60vh;
    overflow-y: auto;
    padding: 0.5rem 1.5rem;
  }

  ::ng-deep .month-option-item {
    display: flex;
    justify-content: space-between !important;
    align-items: center;
    width: 100% !important;

    .month-option-amount {
      margin-left: 20px;
      font-weight: 500;
    }
  }

  .required-field {
    color: $danger;
    margin-left: 2px;
  }

  .form-group {
    padding-bottom: 0.5rem;
    margin-bottom: 0.5rem;
    width: 100%;
    &:first-child {
      padding-top: 0;
    }
  }

  .text {
    color: $medium-gray;
    font-family: Poppins;
    font-size: 12px;
    letter-spacing: 0;
    line-height: 18px;
  }

  ::ng-deep .p-inputgroup {
    border-radius: 9px !important;
    border: none !important;
    background-color: $light-bg !important;
    height: 60px;
    width: 100%;
  }

  ::ng-deep .p-inputgroup .p-inputtext {
    border: none !important;
    background-color: $light-bg !important;
    padding-left: 1rem;
  }

  ::ng-deep .p-inputgroup-addon {
    border: 0 !important;
    width: 4rem;
  }

  ::ng-deep .p-inputtext:enabled:focus {
    box-shadow: none;
  }

  .full-width {
    width: 100% !important;
  }

  ::ng-deep .p-multiselect {
    width: 100%;
    border-radius: 9px !important;
    border: none !important;
    background-color: $light-bg !important;
    min-height: 60px;

    .p-multiselect-label {
      padding: 1rem;
      color: $black;
      font-family: Poppins;
    }

    .p-multiselect-trigger {
      width: 3rem;
      color: $medium-gray;
    }

    &.p-multiselect-chip .p-multiselect-token {
      background-color: $light-purple-bg-2;
      color: $primary;
      border-radius: 16px;
      padding: 0.25rem 0.75rem;
      margin-right: 0.5rem;

      .p-multiselect-token-icon {
        margin-left: 0.5rem;
        color: $primary;
      }
    }
  }

  ::ng-deep .p-multiselect-panel {
    border-radius: 9px;
    width: 100%;

    .p-multiselect-items {
      padding: 0.5rem 0;

      .p-multiselect-item {
        padding: 0.75rem 1.25rem;
        color: $black;
        font-family: Poppins;

        &.p-highlight {
          background-color: $light-purple-bg-2;
          color: $primary;
        }

        &:hover {
          background-color: $light-bg;
        }
      }
    }

    .p-multiselect-header {
      padding: 0.75rem 1.25rem;
      border-bottom: 1px solid $light-purple-bg-2;

      .p-multiselect-filter-container {
        margin-bottom: 0;

        .p-inputtext {
          padding: 0.5rem;
          border-radius: 4px;
        }

        .p-multiselect-filter-icon {
          color: $medium-gray;
          margin-right: 0.5rem;
        }
      }
    }
  }

  ::ng-deep .p-inputtextarea,
  ::ng-deep .p-inputtextarea:focus {
    height: 80px;
    width: 100%;
    border-radius: 9px;
    background-color: $light-bg;
    border: none;
    padding: 1rem;
  }

  h5 {
    color: $black;
    font-family: Poppins;
    font-size: 14px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 21px;
    margin-bottom: 0.5rem;
    margin-top: 0;
  }

  .alert {
    border-radius: 4px;
    padding: 10px 15px;
    margin-top: 15px;

    &.alert-danger {
      background-color: $danger-light-bg;
      border-color: $danger-light-border;
      color: $danger-dark;
    }

    &.alert-info {
      background-color: $info-light-bg;
      border-color: $info-light-border;
      color: $info-dark;
    }
  }

  .item-selected {
    max-height: 200px !important;
    overflow-y: auto !important;
  }
}

.month-section {
  max-width: 300px !important;
  min-width: 300px !important;
}

.month-calendar-wrapper {
  width: 100%;
  max-width: 400px !important;

  .calendar-day-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;

    .calendar-day {
      width: 24px;
      height: 24px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 50%;

      &.selected-month {
        background-color: $primary;
        color: $primary-inverse;
      }
    }
  }

  .selected-months-footer {
    padding: 8px;
    border-top: 1px solid $calendar-border-color;

    .selected-months-label {
      font-weight: 500;
      margin-bottom: 8px;
      color: $primary;
    }

    .selected-months-chips {
      display: flex;
      flex-wrap: wrap;
      gap: 5px;
    }
  }

  .selected-months-display {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-top: 8px;
    border: 2px solid $primary;
    padding: 5px;
    min-height: 40px;
    height: auto !important;
  }
}

::ng-deep .month-chip.p-chip {
  background-color: $primary-light;
  color: $primary;
  font-weight: 500;

  .p-chip-remove-icon {
    color: $primary;
  }

  &:hover {
    background-color: $chip-hover-bg;
  }
}

#custom-month-edit > p-calendar {
  ::ng-deep input {
    visibility: hidden !important;
  }
}

::ng-deep .selected-month-yellow {
  background-color: $primary !important;
  color: white !important;
  font-weight: bold;
  border-radius: 4px;
  outline: 1px solid transparent;
  padding: 1px;
}

::ng-deep .disabled-month {
  pointer-events: none;
  background-color: $disabled-gray !important;
  opacity: 0.4;
  padding: 0.7rem;
  border: 1px solid;
}

.fix-calender-pos {
  right: 0px;
  position: absolute;
  top: 0px;
}

.apply-bg-white {
  background-color: $primary-inverse !important;
}

import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { AdministrationService } from '@entities/administration/administration.service';
import { EmployeeType } from '@entities/utilization-management/utilization.model';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { ButtonParams } from '@shared/models';
import { AlertType } from '@shared/models/alert-type.enum';
import { LayoutUtilsService } from '@shared/services/layout-utils.service';
import { LazyLoadEvent } from 'primeng/api';

@Component({
  selector: 'app-manage-employee-type',
  templateUrl: './manage-employee-type.component.html',
  styleUrls: ['./manage-employee-type.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ManageEmployeeTypeComponent extends SflBaseComponent implements OnInit {
  employeeTypes: EmployeeType[] = [];
  loading = false;
  cardTitle = 'Manage Employee Types';
  cardSubTitle = null;
  buttons: ButtonParams[] = [
    {
      btnClass: 'btn-save',
      btnText: 'Add New',
      redirectPath: this.appRoutes.CREATE_EMPLOYEE_TYPE,
      permissions: [this.permissionModules.MANAGE_EMPLOYEE_TYPE]
    }
  ];

  deleteTypeId: number;
  showDeleteDialog = false;
  constructor(private readonly adminService: AdministrationService, private readonly cdf: ChangeDetectorRef, private readonly layoutUtilsService: LayoutUtilsService) {
    super();
  }

  ngOnInit(): void {}

  getEmployeeTypes(event?: LazyLoadEvent) {
    this.employeeTypes = [];
    this.loading = true;
    this.cdf.detectChanges();
    this.subscriptionManager.add(
      this.adminService.getEmployeeTypes().subscribe(
        (res) => {
          this.loading = false;
          if (res?.body?.data?.employee_types) {
            this.employeeTypes = res.body.data.employee_types;
          }
          this.cdf.detectChanges();
        },
        () => (this.loading = false)
      )
    );
  }

  confirmDeleteType(id: number) {
    this.deleteTypeId = id;
    this.showDeleteDialog = true;
  }

  closeModal() {
    this.deleteTypeId = null;
    this.showDeleteDialog = false;
  }

  deleteType() {
    this.isSubmitting = true;
    this.subscriptionManager.add(
      this.adminService.deleteEmployeeType(this.deleteTypeId).subscribe(
        (res) => {
          this.isSubmitting = false;
          this.closeModal();
          this.layoutUtilsService.showActionNotification('Employee Type has been archived successsfully', AlertType.Success);
          this.getEmployeeTypes();
          this.cdf.detectChanges();
        },
        () => {
          this.closeModal();
          this.cdf.detectChanges();
          this.layoutUtilsService.showActionNotification('You cannot delete an employee type that is attached to an employee', AlertType.Error);
          this.isSubmitting = false;
        }
      )
    );
  }
}

<div class="card card-custom gutter-b" id="manageExpenseType">
  <app-card-header [cardTitle]="cardTitle" [cardSubTitle]="cardSubTitle" [buttons]="buttons"></app-card-header>
  <div class="card-body">
    <p-table
      responsiveLayout="scroll"
      #dt
      [value]="exceptionTypes"
      [lazy]="true"
      (onLazyLoad)="getExceptionTypes($event)"
      dataKey="id"
      [loading]="loading"
      styleClass="p-datatable-customers"
      [filterDelay]="0"
    >
      <ng-template pTemplate="header">
        <tr class="sticky-row-1">
          <th id="name" class="header-width">Work Exception Type</th>
          <th id="actions" class="header-width text-center" colspan="2">Actions</th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-type>
        <tr>
          <td>
            {{ type?.work_exception_type?.name }}
          </td>
          <td class="text-right pr-0">
            <a
              class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary"
              *hasAnyPermission="permissionModules.MANAGE_WORK_EXCEPTION_TYPE; disableEvent: true"
              [routerLink]="[appRoutes.EDIT_WORK_EXCEPTION_TYPE, type?.work_exception_type?.id]"
            >
              <span title="Edit" [inlineSVG]="'assets/media/svg/icons/edit.svg'" cacheSVG="true" class="svg-icon svg-icon-md"> </span>
            </a>
          </td>
          <td>
            <a
              class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary"
              *hasAnyPermission="permissionModules.MANAGE_WORK_EXCEPTION_TYPE; disableEvent: true"
              (click)="confirmDeleteType(type?.work_exception_type?.id)"
            >
              <span title="Archive" [inlineSVG]="'assets/media/svg/icons/archive.svg'" cacheSVG="true" class="svg-icon svg-icon-md"> </span>
            </a>
          </td>
        </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="3" class="center-align">No Work Exception Type found.</td>
        </tr>
      </ng-template>
    </p-table>
  </div>
</div>

<p-dialog
  header="Delete Work Exception Type"
  [(visible)]="showDeleteDialog"
  [modal]="true"
  class="confirm-dialog-expense"
  [baseZIndex]="10000"
  [draggable]="false"
  [resizable]="false"
>
  <h5 class="p-m-0">Are you sure you want to delete this work exception type?</h5>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center">
      <button type="button" class="btn-cancel" (click)="closeModal()">No</button>
      <button type="button" class="btn-save" (click)="deleteType()" [isSubmitting]="isSubmitting">Yes</button>
    </div>
  </ng-template>
</p-dialog>

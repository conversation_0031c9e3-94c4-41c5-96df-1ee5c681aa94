import { AfterViewInit, Component, ElementRef, Input, OnChanges, OnInit, SimpleChanges, ViewChild } from '@angular/core';
import { ManagePeopleService } from '@entities/manage-people/manage-people.service';
import { GlobalDetailsTimeSheetFiles, OGPositionList, OGProjectList } from '@entities/manage-people/mange-people.model';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';

@Component({
  selector: 'app-import-csv',
  templateUrl: './import-csv.component.html',
  styleUrls: ['./import-csv.component.scss']
})
export class ImportCsvComponent extends SflBaseComponent implements OnInit, AfterViewInit, OnChanges {
  @Input() selectedHeaderAlongWithOGFields: any;
  @Input() CSVData: any;
  @Input() columnMapping: any;
  @Input() OGClientList: any;
  @Input() OGProjectList: OGProjectList[];
  @Input() OGPositionList: OGPositionList[];
  @Input() uploadedCSVFile: File[];
  @Input() timeEntriesGlobalDetails: GlobalDetailsTimeSheetFiles;
  @Input() formMapping: any;
  cardTitle = 'Timesheet';
  cardSubTitle = null;
  activeTab;
  wizard = new KTWizard();
  @ViewChild('wizard', { static: true }) el: ElementRef;
  isDataMatched: boolean;

  constructor(private readonly managePeopleService: ManagePeopleService) {
    super();
  }
  ngOnChanges(changes: SimpleChanges): void {
    if (changes.hasOwnProperty('CSVData') && !changes.CSVData.firstChange) {
      this.wizard.goTo(1);
      this.activeTab = 1;

      const isAllDataCorrect = this.checkIsAllDataISVlidated();
      if (!isAllDataCorrect) {
        this.wizard.goTo(2);
        this.activeTab = 2;
      }
    }
  }

  ngOnInit(): void {}

  ngAfterViewInit(): void {
    if (this.el) {
      this.wizard = new KTWizard(this.el.nativeElement, {
        startStep: 1
      });
    }
    this.activeTab = this.wizard.currentStep;
    if (this.CSVData.length >= 1) {
      const isAllDataCorrect = this.checkIsAllDataISVlidated();
      if (!isAllDataCorrect) {
        this.wizard.goTo(2);
        this.activeTab = 2;
      }
    }
  }
  goToNextStep(step) {
    this.wizard.goTo(step);
    this.activeTab = step;
  }

  previousStep() {
    this.wizard.goPrev();
    this.activeTab = this.wizard.currentStep;
  }

  nextStep() {
    this.wizard.goNext();
    this.activeTab = this.wizard.currentStep;
  }

  saveTimeSheetData() {
    this.managePeopleService.saveTimeSheet.next(true);
  }

  checkIsAllDataISVlidated(event?: any) {
    //we need disable the next button for secound tab untill user correct all the data
    const isAllDataMatchWithOG = this.CSVData.every((data) => {
      const isDataMacthWithOG = this.selectedHeaderAlongWithOGFields.every((res) => {
        return data[res.columnField].isMatchWithOG;
      });
      return isDataMacthWithOG;
    });
    this.isDataMatched = isAllDataMatchWithOG;
    if (event && this.isDataMatched) {
      this.wizard.goTo(2);
      this.activeTab = 2;
    }
    return !isAllDataMatchWithOG;
  }
}

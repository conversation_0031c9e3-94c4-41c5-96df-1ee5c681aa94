"use strict";var KTLayoutExamples={init:function(e,l){!function(e){var l=e;void 0===l&&(l=document.querySelectorAll(".example:not(.example-compact):not(.example-hover):not(.example-basic)"));for(var t=0;t<l.length;++t){var i=l[t],o=KTUtil.find(i,".example-copy");new ClipboardJS(o,{target:function(e){var l=e.closest(".example"),t=KTUtil.find(l,".example-code .tab-pane.active");return t||(t=KTUtil.find(l,".example-code")),t}}).on("success",function(e){KTUtil.addClass(e.trigger,"example-copied"),e.clearSelection(),setTimeout(function(){KTUtil.removeClass(e.trigger,"example-copied")},2e3)})}}(e),function(e){if(void 0===(l=e))var l=document.querySelectorAll(".example.example-compact");for(var t=0;t<l.length;++t){var i=l[t],o=KTUtil.find(i,".example-toggle"),a=KTUtil.find(i,".example-copy");KTUtil.addEvent(o,"click",function(){var e=this.closest(".example"),l=KTUtil.find(e,".example-code"),t=this;KTUtil.hasClass(this,"example-toggled")?KTUtil.slideUp(l,300,function(){KTUtil.removeClass(t,"example-toggled"),KTUtil.removeClass(l,"example-code-on"),KTUtil.hide(l)}):(KTUtil.addClass(l,"example-code-on"),KTUtil.addClass(this,"example-toggled"),KTUtil.slideDown(l,300,function(){KTUtil.show(l)}))}),new ClipboardJS(a,{target:function(e){var l=e.closest(".example"),t=KTUtil.find(l,".example-code .tab-pane.active");return t||(t=KTUtil.find(l,".example-code")),t}}).on("success",function(e){KTUtil.addClass(e.trigger,"example-copied"),e.clearSelection(),setTimeout(function(){KTUtil.removeClass(e.trigger,"example-copied")},2e3)})}}(e)}};"undefined"!=typeof module&&void 0!==module.exports&&(module.exports=KTLayoutExamples);

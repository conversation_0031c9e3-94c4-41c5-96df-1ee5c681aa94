// Angular
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MyProfileRoutingModule } from './my-profile-routing.module';

// Metronic
import { MyProfileComponent } from './my-profile.component';

import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { InlineSVGModule } from 'ng-inline-svg';
import { PartialsModule } from '@entities/partials/partials.module';
import { SharedModule } from '@shared/shared.module';

@NgModule({
  declarations: [MyProfileComponent],
  imports: [CommonModule, FormsModule, PartialsModule, SharedModule, MyProfileRoutingModule, MatSelectModule, MatInputModule, InlineSVGModule]
})
export class MyProfileModule {}

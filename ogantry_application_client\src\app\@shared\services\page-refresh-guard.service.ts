import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivate, Router, RouterStateSnapshot } from '@angular/router';
import { APP_ROUTES } from '../constants';
import { browserRefresh } from './../../app.component';

@Injectable({
  providedIn: 'root'
})
export class PageRefreshGuard implements CanActivate {
  constructor(private readonly router: Router) {}

  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean {
    if (!browserRefresh) {
      return true;
    }
    if (browserRefresh && (route.routeConfig.path === 'role/create' || route.routeConfig.path === 'role/update/:roleId')) {
      this.router.navigateByUrl(APP_ROUTES.MANAGE_ROLE);
    }
    // tagging
    if (browserRefresh && (route.routeConfig.path === 'tag/create' || route.routeConfig.path === 'tag/:tagId')) {
      this.router.navigateByUrl(APP_ROUTES.MANAGE_TAG);
    }
    if (browserRefresh && route.routeConfig.path === 'extended-Field/create') {
      this.router.navigateByUrl(APP_ROUTES.MANAGE_Extend_Field);
    }
  }
}

import { PositionSetupComponent } from './ogantry_application_client/src/app/entities/project/position-setup/position-setup.component';
import { AlertType } from './ogantry_application_client/src/app/shared/models/alert-type.enum';

describe('PositionSetupComponent - Duplicate Month Prevention', () => {
  let component: PositionSetupComponent;
  let layoutUtilsServiceMock: any;

  beforeEach(() => {
    layoutUtilsServiceMock = {
      showActionNotification: jasmine.createSpy('showActionNotification')
    };

    component = new PositionSetupComponent(
      null, null, null, null, null, 
      layoutUtilsServiceMock, null, null, null, null, null, null
    );
  });

  it('should remove duplicate months when onMonthSelect is called', () => {
    // Create test dates with duplicates
    const jan2023 = new Date(2023, 0, 1); // January 2023
    const feb2023 = new Date(2023, 1, 1); // February 2023
    const janDuplicate = new Date(2023, 0, 15); // Another January 2023
    
    // Set up the component with duplicate months
    component.selectedMonths = [jan2023, feb2023, janDuplicate];
    
    // Call the method that should remove duplicates
    component.onMonthSelect();
    
    // Verify that duplicates were removed
    expect(component.selectedMonths.length).toBe(2);
    
    // Verify that the selectedMonth array has the correct values
    expect(component.selectedMonth.length).toBe(2);
    expect(component.selectedMonth[0].month).toBe(1); // January (0-based index + 1)
    expect(component.selectedMonth[0].year).toBe(2023);
    expect(component.selectedMonth[1].month).toBe(2); // February (0-based index + 1)
    expect(component.selectedMonth[1].year).toBe(2023);
    
    // Verify that a notification was shown
    expect(layoutUtilsServiceMock.showActionNotification).toHaveBeenCalledWith(
      'Duplicate months were removed', 
      AlertType.Info
    );
  });

  it('should not show notification when no duplicates exist', () => {
    // Create test dates without duplicates
    const jan2023 = new Date(2023, 0, 1); // January 2023
    const feb2023 = new Date(2023, 1, 1); // February 2023
    const mar2023 = new Date(2023, 2, 1); // March 2023
    
    // Set up the component without duplicate months
    component.selectedMonths = [jan2023, feb2023, mar2023];
    
    // Call the method
    component.onMonthSelect();
    
    // Verify that no duplicates were removed
    expect(component.selectedMonths.length).toBe(3);
    
    // Verify that the selectedMonth array has the correct values
    expect(component.selectedMonth.length).toBe(3);
    
    // Verify that no notification was shown
    expect(layoutUtilsServiceMock.showActionNotification).not.toHaveBeenCalled();
  });
});

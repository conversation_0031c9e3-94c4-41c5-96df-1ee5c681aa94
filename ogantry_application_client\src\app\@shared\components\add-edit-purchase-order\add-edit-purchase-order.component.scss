.add-contact .card-body.create-card {
  box-shadow: none;
  margin: 0;
  padding: 20px !important;
}

.form-check {
  bottom: 4px;
  position: absolute;
}

.form-group {
  padding-bottom: 0;
  &:first-child {
    padding-top: 0;
  }
}

::ng-deep .range-calender .p-calendar-w-btn {
  height: 100%;
  width: 100%;
}

::ng-deep .range-calender .p-calendar-w-btn .p-inputtext {
  border: none !important;
  background-color: #f8f8ff !important;
  height: 100%;
  font-size: 16px;
}

::ng-deep p-calendar button {
  min-height: 60px !important;
  min-width: 40px !important;
}

.postion-dolor-amount {
  position: absolute;
  top: 40px;
  right: 10px;
}

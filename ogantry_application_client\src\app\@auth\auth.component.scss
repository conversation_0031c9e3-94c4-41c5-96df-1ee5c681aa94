// import login css
@import './../../assets/sass/pages/login/login-3.scss';

kt-auth,
#kt_login_wrapper {
  height: 100%;

  @media (min-width: 992px) {
    .login.login-3 .login-form {
      width: 100%;
      max-width: 450px;
    }
  }

  .login-form {
    width: 100%;
    max-width: 450px;

    .mat-form-field {
      width: 100%;
    }
  }
}

kt-auth,
.login {
  height: 100%;

  .login-form {
    width: 100%;
    max-width: 450px;

    .mat-form-field {
      width: 100%;
    }
  }

  // add extra right padding when displaying spinner in button
  .spinner {
    padding-right: 3rem !important;
  }
}

kt-login,
kt-register,
kt-forgot-password {
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  flex: 1 0 auto;
  height: 100%;
  display: flex !important;
}

.height-100 {
  height: 100%;
}

.tagline {
  color: $white;
  font-size: 2rem;
  line-height: 46px;
  text-align: center;
  margin-top: 35px;
  max-height: 100px;
}

.tagline-wrapper {
  margin-top: 10%;
  justify-content: center;
}

.landing-aside-bg {
  margin-bottom: 2%;
  text-align: center;
  padding-top: 15%;

  img {
    max-width: 400px;
  }
}

@media (max-width: 768px) {
  .desktop-layout {
    display: none !important;
  }
  .tagline {
    margin-top: 15px;
    font-size: 1.5rem;
    text-align: left;
  }

  .bg-image {
    margin-top: 10px;
    text-align: center;
    img {
      max-width: 50%;
    }
  }
}

@media (width: 768px) {
  img {
    max-width: 80% !important;
  }
}

@media (min-width: 769px) {
  .mobile-layout {
    display: none !important;
  }
}

<div class="card card-custom gutter-b" id="manageWorkException">
  <app-card-header [cardTitle]="cardTitle" [cardSubTitle]="cardSubTitle" [buttons]="buttons"></app-card-header>
  <div class="card-body">
    <p-table
      responsiveLayout="scroll"
      #dt
      [value]="workExceptionsData"
      [sortField]="sortFieldName"
      [sortOrder]="sortOrderNumber"
      [lazy]="true"
      (onSort)="sortColumn()"
      (onPage)="pageChange()"
      (onLazyLoad)="getManageWorkExceptions($event)"
      dataKey="id"
      [rows]="10"
      [showCurrentPageReport]="true"
      [totalRecords]="totalRecords"
      [rowsPerPageOptions]="[10, 25, 50]"
      [loading]="loading"
      styleClass="p-datatable-customers"
      [paginator]="loading ? false : true"
      currentPageReportTemplate="Displaying {first} - {last} of {totalRecords} records"
      [filterDelay]="0"
    >
      <ng-template pTemplate="header">
        <tr class="sticky-row-1">
          <th id="name" pSortableColumn="description" class="header-width">Work Exception Name<p-sortIcon field="description"></p-sortIcon></th>
          <th id="date" pSortableColumn="date" class="header-width">Date<p-sortIcon field="date"></p-sortIcon></th>
          <th id="e_name" pSortableColumn="employee_first_name" class="header-width">First Name<p-sortIcon field="employee_first_name"></p-sortIcon></th>
          <th id="e_name" pSortableColumn="employee_last_name" class="header-width">Last Name<p-sortIcon field="employee_last_name"></p-sortIcon></th>
          <th id="type" pSortableColumn="type_name" class="header-width">Type<p-sortIcon field="type_name"></p-sortIcon></th>
          <th id="actions" class="header-width text-center" colspan="2">Actions</th>
        </tr>
        <tr class="sticky-row-2">
          <th id="searchException">
            <span class="p-input-icon-right">
              <em class="pi pi-times" *ngIf="dataFilter.description" (click)="clearFilter('description')"></em>
              <input pInputText type="text" class="p-column-filter" placeholder="Search" [(ngModel)]="dataFilter.description" (input)="filter()" />
            </span>
          </th>
          <th id="searchStartDate">
            <p-calendar appendTo="body" [(ngModel)]="dataFilter.date" (onSelect)="filter()" showButtonBar="true" (onClearClick)="clearDate()" [readonlyInput]="true"></p-calendar>
          </th>
          <th id="searchEmployee">
            <span class="p-input-icon-right">
              <em class="pi pi-times" *ngIf="dataFilter.employee_first_name" (click)="clearFilter('employee_first_name')"></em>
              <input pInputText type="text" class="p-column-filter" placeholder="Search" [(ngModel)]="dataFilter.employee_first_name" (input)="filter()" />
            </span>
          </th>
          <th id="searchEmployee">
            <span class="p-input-icon-right">
              <em class="pi pi-times" *ngIf="dataFilter.employee_last_name_search" (click)="clearFilter('employee_last_name_search')"></em>
              <input pInputText type="text" class="p-column-filter" placeholder="Search" [(ngModel)]="dataFilter.employee_last_name_search" (input)="filter()" />
            </span>
          </th>
          <th id="searchType">
            <p-dropdown
              appendTo="body"
              name="type_name"
              [options]="exceptionTypes"
              styleClass="p-column-filter pi-icon"
              placeholder="Select"
              [(ngModel)]="dataFilter.type_name"
              (onChange)="filter()"
            >
            </p-dropdown>
          </th>
          <th class="text-center" id="resetFilter" colspan="2">
            <a title="Reset Filter" (click)="resetFilters()" class="btn btn-icon btn-icon-light btn-sm icon-background">
              <span [inlineSVG]="'assets/media/svg/icons/clear-filter.svg'" cacheSVG="true" class="svg-icon svg-icon-md"> </span>
            </a>
          </th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-exception>
        <tr>
          <td>
            <span [title]="exception?.work_exception?.description"> {{ exception?.work_exception?.description }} </span>
          </td>
          <td class="text-number-right">{{ exception?.work_exception?.date | date : 'MM/dd/yyyy' }}</td>
          <td>
            <span [title]="exception?.work_exception?.employee?.first_name"> {{ exception?.work_exception?.employee?.first_name }} </span>
          </td>
          <td>
            <span [title]="exception?.work_exception?.employee?.last_name"> {{ exception?.work_exception?.employee?.last_name }} </span>
          </td>
          <td>
            <span [title]="exception?.work_exception?.work_exception_type?.name"> {{ exception?.work_exception?.work_exception_type?.name }} </span>
          </td>
          <td class="text-right pr-0">
            <a
              class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary"
              *hasAnyPermission="permissionModules.MANAGE_WORK_EXCEPTION; disableEvent: true"
              [routerLink]="[appRoutes.EDIT_WORK_EXCEPTION, exception?.work_exception?.id]"
            >
              <span title="Edit" [inlineSVG]="'assets/media/svg/icons/edit.svg'" cacheSVG="true" class="svg-icon svg-icon-md"> </span>
            </a>
          </td>
          <td>
            <a
              class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary"
              *hasAnyPermission="permissionModules.MANAGE_WORK_EXCEPTION; disableEvent: true"
              (click)="confirmDeleteException(exception?.work_exception?.id, exception?.work_exception?.employee ? true : false)"
            >
              <span title="Archive" [inlineSVG]="'assets/media/svg/icons/archive.svg'" cacheSVG="true" class="svg-icon svg-icon-md"> </span>
            </a>
          </td>
        </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="6" class="center-align">No Work Exceptions found.</td>
        </tr>
      </ng-template>
    </p-table>
  </div>
</div>

<p-dialog header="Delete Work Exception" [(visible)]="showDeleteDialog" [modal]="true" class="confirm-dialog" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
  <h5 class="p-m-0">Are you sure you want to delete this work Exception?</h5>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center">
      <button type="button" class="btn-cancel" (click)="closeModal()">No</button>
      <button type="button" class="btn-save" (click)="deleteWorkException()" [isSubmitting]="isSubmitting">Yes</button>
    </div>
  </ng-template>
</p-dialog>

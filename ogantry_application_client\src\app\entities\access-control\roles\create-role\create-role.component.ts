import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Params } from '@angular/router';
import { AuthNoticeService } from '@auth/auth-notice/auth-notice.service';
import { AuthService } from '@auth/index';
import { GlobalDetailList } from '@entities/administration/administration.model';
import { AdministrationService } from '@entities/administration/administration.service';
import { NavigationConfig } from '@shared/_config/naivgation.config';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { ButtonParams } from '@shared/models';
import { SubMenuItem } from '@shared/models/custom/navigation.modal';
import { initialPermissions, Permissions, Modules, ModuleNames } from '@shared/models/permission.enum';

@Component({
  selector: 'app-create-role',
  templateUrl: './create-role.component.html',
  styleUrls: ['./create-role.component.scss'],
  providers: [NavigationConfig]
})
export class CreateRoleComponent extends SflBaseComponent implements OnInit {
  permissions = [];
  showNavigationErrorMessage = false;
  cardTitle = 'Create Role';
  cardSubTitle = null;
  buttons: ButtonParams[] = [];
  projectStatus = false;
  groupedItems: any[] = [];
  intersection = [];
  constructor(
    private readonly administrationService: AdministrationService,
    private readonly authNoticeService: AuthNoticeService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly authService: AuthService,
    private readonly navConfig: NavigationConfig
  ) {
    super();
    this.transformNavigation();
  }
  rolesConst = JSON.parse(JSON.stringify(initialPermissions));
  permissionsEnum = Permissions;
  projectStatusPermission = null;
  createRoleForm: FormGroup;
  roleId: number;
  blockUpdateDelete = true;
  selectedValue: any;
  ngOnInit(): void {
    this.initForm();
    this.getParams();
    this.blockUpdateDelete = history.state.blockUpdateDelete;
  }

  getParams() {
    this.activatedRoute.params.subscribe((params: Params) => {
      this.roleId = params.roleId;
      if (this.roleId) {
        this.cardTitle = 'Edit Role';
        this.setRoleForm();
      } else {
        this.rolesConst = JSON.parse(JSON.stringify(initialPermissions));
      }
      this.setBtnParams();
    });
  }

  setRoleForm() {
    const roleData = this.administrationService.roles?.extended_fields?.roles?.filter((role) => role.id === Number(this.roleId));
    if (roleData?.length) {
      if (
        roleData[0]?.permissions[2]?.feature == 'Utilization Management' &&
        roleData[0]?.permissions[2]?.subfeature[1]?.feature == 'Lost Revenue Permissions' &&
        roleData[0]?.permissions[2]?.subfeature[1]?.id == 9
      ) {
        roleData[0].permissions[2].subfeature.splice(1, 1);
      }
    }
    if (roleData.length) {
      if (roleData[0]?.navigation?.value) {
        this.setSelectedValue(roleData[0]?.navigation);
      }
      this.createRoleForm.controls['name'].setValue(roleData[0].name);
      this.createRoleForm.controls['description'].setValue(roleData[0].description);
      this.createRoleForm.controls['id'].setValue(roleData[0].id);
      this.createRoleForm.controls['isDefault'].setValue(roleData[0].isDefault);
      this.rolesConst = roleData[0].permissions;
      const extraFeat = initialPermissions.filter((ip) => !this.rolesConst.some((role) => role.id === ip.id));
      this.intersection = [];
      this.rolesConst.map((role) => {
        let initialRole: any = initialPermissions.filter((ip) => ip.id === role.id)[0];
        if (role.subfeature.length !== initialRole.subfeature.length) {
          // used to find out the permissions which may have been moved thoese list of permissions we will get in intersection array.
          const interItems = role.subfeature.filter(({ id: id1 }) => !initialRole.subfeature.some(({ id: id2 }) => id2 === id1));
          if (interItems.length) {
            this.intersection = [...interItems];
          }

          role.subfeature = role.subfeature.filter((roleSubFeat) => {
            return initialRole.subfeature.some((initialSubFeat) => {
              return roleSubFeat.name === initialSubFeat.name;
            });
          });
          let hash = Object.create(null);
          role.subfeature.forEach((a) => {
            hash[a.name] = true;
          });
          initialRole.subfeature.forEach((a) => {
            hash[a.name] || role.subfeature.push(a);
          });
        }
      });

      // if any new menu is added adding them in proper sequence order
      extraFeat.map((ef) => {
        const elementPos = this.rolesConst
          .map(function (x) {
            return x.sequence;
          })
          .indexOf(ef.sequence);
        this.rolesConst.splice(elementPos, 0, ef);
      });

      // used to loop thru all the intersection we found against the permissions.
      // This will match the id of permission from intersection and compare it with roles array, wherever we found that id,
      //we need to update it with the permissions in the intersection array element, this way it will preserve the permission selection.
      let result = null;
      for (const inter of this.intersection) {
        for (let obj of this.rolesConst) {
          result = this.checkPermissionId(obj, inter.id, inter);
          if (result) {
            obj.expand = true;
            break;
          }
        }
      }
      this.rolesConst.sort((a, b) => a.sequence - b.sequence);
    }
  }
  checkPermissionId(obj, targetId, inter) {
    if (obj.id === targetId) {
      obj.permission = inter.permission;
      return obj;
    }
    if (obj.subfeature) {
      for (let item of obj.subfeature) {
        let check = this.checkPermissionId(item, targetId, inter);
        if (check) {
          check.permission = inter.permission;
          return check;
        }
      }
    }
    return null;
  }

  initForm() {
    this.createRoleForm = new FormGroup({
      id: new FormControl(Math.floor(Math.random() * 90 + 10)),
      name: new FormControl('', Validators.required),
      description: new FormControl('', Validators.required),
      isDefault: new FormControl(false)
    });
  }

  setBtnParams() {
    this.buttons = [
      {
        btnClass: 'btn btn-cancel',
        btnText: 'Cancel',
        redirectPath: this.appRoutes.MANAGE_ROLE
      },
      {
        btnClass: 'btn-save',
        btnText: 'Save',
        btnType: 'submit',
        action: this.onSave.bind(this),
        loading: this.isSubmitting
      }
    ];
  }

  toggleButton(role) {
    role.expand = !role.expand;
  }

  onSuccess(role, successMsg: string) {
    this.isSubmitting = false;
    this.administrationService.roles = role.global_detail;
    this.authNoticeService.setNotice(successMsg, 'success');
  }

  onError(err) {
    this.isSubmitting = false;
    const error = err.error;
    this.authNoticeService.setNotice(error.errors, 'danger');
  }

  onSave() {
    if (!this.checkFormForValidation(this.createRoleForm)) {
      let role: any;
      if (this.selectedValue) {
        role = { ...this.createRoleForm.value, navigation: this.selectedValue };
        this.checkSelectedNavigationHasPermission();
        if (this.checkSelectedNavigationHasPermission()) {
          console.log('in loop');
          return;
        }
      } else {
        role = this.createRoleForm.value;
      }
      const roleData = this.getExtendedData(role);
      this.isSubmitting = true;
      if (this.administrationService.roles?.id) {
        this.subscriptionManager.add(
          this.administrationService.updateRoles(roleData, this.administrationService.roles.id).subscribe(
            (res) => {
              if (this.roleId) {
                this.onSuccess(res.data, 'Role updated successfully');
              } else {
                this.onSuccess(res.data, 'Role created successfully');
              }
              const globalDetailsList: GlobalDetailList = {
                global_details: [res?.data]
              };
              // as soon as the user adds up a new role we would be updating our local/ cache roles with newly/ updated global details.
              this.authService.globalDetailsList$.next(globalDetailsList);
            },
            (err) => this.onError(err)
          )
        );
      } else {
        this.subscriptionManager.add(
          this.administrationService.addRoles(roleData).subscribe(
            (res) => {
              this.onSuccess(res.data, 'Role created successfully');
            },
            (err) => this.onError(err)
          )
        );
      }
    }
  }

  getExtendedData(roleValue) {
    const returnData = { name: '', extended_fields: { roles: [] } };
    roleValue = { ...roleValue, permissions: this.rolesConst };
    if (this.administrationService.roles?.id) {
      returnData.name = this.administrationService.roles.name;
      if (this.administrationService.roles && this.administrationService.roles?.extended_fields?.roles?.length) {
        this.administrationService.roles.extended_fields.roles = this.administrationService.roles.extended_fields.roles.filter((role) => role.id !== roleValue.id);
        returnData.extended_fields.roles = this.administrationService.roles.extended_fields.roles;
        returnData.extended_fields.roles.push(roleValue);
      } else {
        returnData.extended_fields.roles = [roleValue];
      }
    } else {
      returnData.name = 'UserRolePermissions';
      returnData.extended_fields.roles = [roleValue];
    }
    return returnData;
  }

  ngOnDestroy() {
    this.authNoticeService.setNotice(null);
  }

  permissionCheckProject(event, role, subFeature, innerSubFeature, manageFlag = false) {
    const permissionValue = event.target.defaultValue;

    const isChecked = innerSubFeature.permission.includes(permissionValue);
    const isEarlyFullAccess =
      innerSubFeature.permission.includes(this.permissionsEnum.CAN_VIEW + permissionValue.split(' ')[1]) &&
      innerSubFeature.permission.includes(this.permissionsEnum.CAN_MANAGE + permissionValue.split(' ')[1]);

    if (isChecked && isChecked && (!isEarlyFullAccess || permissionValue.startsWith('Manage '))) {
      innerSubFeature.permission = innerSubFeature.permission.filter((value) => value !== permissionValue);
    } else {
      innerSubFeature.permission = [];
      innerSubFeature.permission.push(permissionValue);

      // For Full Access
      if (manageFlag) {
        const value = this.permissionsEnum.CAN_VIEW + permissionValue.split(this.permissionsEnum.CAN_MANAGE)[1];
        innerSubFeature.permission.push(value);
      }
    }

    // if (subFeature.feature === "Manage Projects") {
    //   const noAccessPermission = this.permissionsEnum.NO_ACCESS + subFeature.subfeature[0].name.split(this.permissionsEnum.CAN_MANAGE)[1];

    //   if (subFeature.permission.includes("No Access Project")) {
    //     subFeature.subfeature.forEach(sub => {
    //       sub.isHide = true;
    //       sub.permission = [noAccessPermission];
    //     });
    //   } else {
    //     // subFeature.subfeature.forEach(sub => {
    //     //   sub.isHide = false;
    //     //   sub.permission = [];
    //     // });
    //   }
    // }
  }

  permissionCheck(event, role, subFeature, manageFlag = false) {
    if (role?.allFeatures?.length) {
      role.allFeatures = [];
    }

    const permissionValue = event.target.defaultValue;

    subFeature.permission = [];
    subFeature.permission.push(permissionValue);

    if (manageFlag) {
      if (subFeature.name === Modules.MANAGE_TAG || subFeature.name === Modules.MANAGE_TAG_CATEGORY) {
        subFeature.permission.push(this.permissionsEnum.CAN_VIEW + ModuleNames.MANAGE_TAG_CATEGORY);
        return;
      }
      const value = this.permissionsEnum.CAN_VIEW + permissionValue.split(this.permissionsEnum.CAN_MANAGE)[1];
      subFeature.permission.push(value);
    }
  }

  AllPermissionCheck(event, role, manageFlag = false) {
    role.subfeature.map((feat) => {
      feat.permission = [];
    });
    const accessArray = event.target.defaultValue.split(' ');
    let access = '';
    if (accessArray.length > 1) {
      access = accessArray[0] + ' ' + accessArray[1] + ' ';
      if (access !== this.permissionsEnum.NO_ACCESS) {
        access = accessArray[0] + ' ';
      }
    }
    role.allFeatures = [];
    role.allFeatures.push(access);
    role.subfeature.map((feat) => {
      feat.permission.push(access + feat.module);
      if (manageFlag) {
        const value = this.permissionsEnum.CAN_VIEW + feat.module;
        feat.permission.push(value);
      }
    });
  }

  getProjectStaffing(event) {
    return event.id === 1;
  }

  changeProjectStatus(event) {
    const projectStaffingObj = this.rolesConst.filter(this.getProjectStaffing);
    if (event.target.checked) {
      this.projectStatus = true;
      projectStaffingObj[0].subfeature.map((obj) => {
        if (obj.name === Modules.MANAGE_PROJECT) {
          obj.permission.push(Modules.MANAGE_PROJECT_STATUS);
        }
      });
    } else {
      this.projectStatus = false;
      const index = projectStaffingObj[0].subfeature.filter((obj) => obj.name === Modules.MANAGE_PROJECT)[0].permission.findIndex((p) => p === Modules.MANAGE_PROJECT_STATUS);

      if (index !== -1) {
        projectStaffingObj[0].subfeature.map((obj) => {
          if (obj.name === Modules.MANAGE_PROJECT) {
            obj.permission.splice(index, 1);
          }
        });
      }
    }
    const otherScreenPermissions = this.rolesConst.filter((role) => role.id !== 1);
    this.rolesConst = [];
    this.rolesConst = [...projectStaffingObj, ...otherScreenPermissions];
  }

  transformNavigation(): void {
    const navItems = this.navConfig.defaults.aside.items;
    this.groupedItems = navItems.map((item) => ({
      group: item.title,
      items: item.submenu.map((sub) => ({
        title: sub.title,
        value: sub
      }))
    }));
  }

  onSelect(event: any): void {
    this.selectedValue = event.value;
  }

  setSelectedValue(value: SubMenuItem): void {
    this.selectedValue = value;
  }

  extractPermissions(subfeatures: any): void {
    subfeatures.forEach((subfeature) => {
      if (subfeature.permission && subfeature.permission.length > 0) {
        this.permissions.push(...subfeature.permission);
      }
      if (subfeature.includePermissions && subfeature.includePermissions.length > 0) {
        this.permissions.push(...subfeature.includePermissions);
      }
      if (subfeature.subfeature) {
        this.extractPermissions(subfeature.subfeature);
      }
    });
  }

  checkSelectedNavigationHasPermission(): boolean {
    if (this.createRoleForm.value.id && this.selectedValue) {
      const PermissionValue = this.getPermissionArray();
      const result = this.selectedValue?.value?.permissionModules?.every((item) => PermissionValue.includes(item)) as boolean;
      return (this.showNavigationErrorMessage = !result);
    }
  }

  getPermissionArray(): Array<any> {
    this.permissions = [];
    this.rolesConst?.forEach((item) => {
      if (item.subfeature) {
        this.extractPermissions(item.subfeature);
      }
    });
    return this.permissions;
  }

  checkNoAccessPermission(permission: Array<string>, moduleNames: string): boolean {
    return permission?.length && moduleNames ? (permission?.includes(this.permissionsEnum.NO_ACCESS + moduleNames) ? true : false) : true;
  }

  checkInitialPermission(modulePermission: string[], permission: string): boolean {
    if (modulePermission && modulePermission?.length && permission) {
      return !!modulePermission.includes(permission);
    }
    return true;
  }
}

import { Component, OnInit } from '@angular/core';
import { FormArray, FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Params } from '@angular/router';
import { AuthNoticeService } from '@auth/auth-notice/auth-notice.service';
import { SubCategory, TagCategory } from '@entities/administration/administration.model';
import { AdministrationService } from '@entities/administration/administration.service';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { ButtonParams } from '@shared/models';
import { Utility } from '@shared/utils/utils';

@Component({
  selector: 'app-add-update-sub-category',
  templateUrl: './add-update-sub-category.component.html',
  styleUrls: ['./add-update-sub-category.component.scss']
})
export class AddUpdateSubCategoryComponent extends SflBaseComponent implements OnInit {
  cardTitle = 'Create Tag Sub Category';
  cardSubTitle = null;
  buttons: ButtonParams[] = [];
  constructor(
    private readonly administrationService: AdministrationService,
    private readonly authNoticeService: AuthNoticeService,
    private readonly activatedRoute: ActivatedRoute
  ) {
    super();
  }
  createTagSubCategoryForm: FormGroup;
  subCategoryId: number;
  parentCategories = [];
  categoryMasterData: TagCategory[] = [];
  parentCategoryIndexBeingUpdated: number;
  childCategoryIndexBeingUpdated: number;

  ngOnInit(): void {
    this.initForm();
    this.getParams();
    this.setCategoryMasterData();
  }

  setCategoryMasterData() {
    this.categoryMasterData = this.administrationService.tagCategories?.extended_fields?.tagCategory;
  }

  getParams() {
    this.activatedRoute.params.subscribe((params: Params) => {
      this.subCategoryId = params.categoryId;
      if (this.subCategoryId) {
        this.cardTitle = 'Edit Tag Sub Category';
        this.setCategoryForm();
      }
      this.setBtnParams();
    });
  }

  setCategoryForm() {
    // const tagCategoryData = this.administrationService.tagCategories?.extended_fields?.tagCategory?.filter(category => category.id === this.subCategoryId);
    this.parentCategoryIndexBeingUpdated = this.administrationService.tagCategories?.extended_fields?.tagCategory?.findIndex(
      (category) => category?.subTagCategory?.findIndex((childCategory) => childCategory.id === this.subCategoryId) > -1
    );
    const tagCategoryData = this.administrationService.tagCategories?.extended_fields?.tagCategory[this.parentCategoryIndexBeingUpdated];
    // need to grab the child node index so that one we would be updating
    this.childCategoryIndexBeingUpdated = tagCategoryData?.subTagCategory?.findIndex((node) => node?.id === this.subCategoryId);
    if (tagCategoryData) {
      this.createTagSubCategoryForm.controls['name'].setValue(tagCategoryData?.subTagCategory[this.childCategoryIndexBeingUpdated]?.name);
      this.createTagSubCategoryForm.controls['id'].setValue(tagCategoryData?.subTagCategory[this.childCategoryIndexBeingUpdated]?.id);
      this.createTagSubCategoryForm.controls['parentCategoryId'].setValue(tagCategoryData?.subTagCategory[this.childCategoryIndexBeingUpdated]?.parentCategoryId);
      this.createTagSubCategoryForm.controls['tags'] = new FormArray([]);
    }
  }

  onParentCategoryUpdate() {
    this.parentCategoryIndexBeingUpdated = this.administrationService.tagCategories?.extended_fields?.tagCategory?.findIndex(
      (category) => category?.id === this.createTagSubCategoryForm.get('parentCategoryId').value
    );
  }

  initForm() {
    this.createTagSubCategoryForm = new FormGroup({
      id: new FormControl(Utility.getUniqueId(4)),
      name: new FormControl('', Validators.required),
      parentCategoryId: new FormControl('', Validators.required),
      tags: new FormArray([])
    });
  }

  setBtnParams() {
    this.buttons = [
      {
        btnClass: 'btn btn-cancel',
        btnText: 'Cancel',
        redirectPath: this.appRoutes.MANAGE_TAG_SUB_CATEGORY
      },
      {
        btnClass: 'btn-save',
        btnText: 'Save',
        btnType: 'submit',
        action: this.onSave.bind(this),
        loading: this.isSubmitting
      }
    ];
  }

  toggleButton(role) {
    role.expand = !role.expand;
  }

  onSuccess(role, successMsg: string) {
    this.isSubmitting = false;
    this.setBtnParams();
    this.administrationService.tagCategories = role.global_detail;
    this.authNoticeService.setNotice(successMsg, 'success');
  }

  onError(err) {
    this.isSubmitting = false;
    this.setBtnParams();
    const error = err.error;
    this.authNoticeService.setNotice(error.errors, 'danger');
  }

  onSave() {
    if (!this.checkFormForValidation(this.createTagSubCategoryForm)) {
      this.isSubmitting = true;
      this.setBtnParams();
      this.createTagSubCategoryForm.value.name = this.createTagSubCategoryForm.value.name.replace(/ /g, '-');
      const categoryData = this.getExtendedData(this.createTagSubCategoryForm.value);
      if (this.administrationService.subCategories?.id) {
        this.subscriptionManager.add(
          this.administrationService.updateCategory(categoryData, this.administrationService.subCategories.id).subscribe(
            (res) => {
              if (this.subCategoryId) {
                this.onSuccess(res.data, 'Tag sub category updated successfully');
              } else {
                this.onSuccess(res.data, 'Tag sub category created successfully');
              }
            },
            (err) => this.onError(err)
          )
        );
      } else {
        this.subscriptionManager.add(
          this.administrationService.addTagSubCategory(categoryData).subscribe(
            (res) => {
              this.onSuccess(res.data, 'Tag sub category created successfully');
            },
            (err) => this.onError(err)
          )
        );
      }
    }
  }

  getExtendedData(subCategoryFormValue: SubCategory) {
    const returnData = { name: '', extended_fields: { subCategory: [] } };
    subCategoryFormValue = { ...subCategoryFormValue };
    if (this.administrationService.subCategories?.id) {
      returnData.name = this.administrationService.subCategories.name;
      if (this.administrationService.subCategories && this.administrationService.subCategories?.extended_fields?.subCategory?.length) {
        // checking here if its an update for existing item or adding a new item
        if (this.subCategoryId) {
          const updatingndex = this.administrationService.subCategories.extended_fields.subCategory.findIndex((subCateId) => subCateId.id === subCategoryFormValue.id);
          returnData.extended_fields.subCategory = this.administrationService.subCategories.extended_fields.subCategory;
          returnData.extended_fields.subCategory[updatingndex].id = subCategoryFormValue.id;
          returnData.extended_fields.subCategory[updatingndex].name = subCategoryFormValue.name;
          returnData.extended_fields.subCategory[updatingndex].parentCategoryId = subCategoryFormValue.parentCategoryId;
          returnData.extended_fields.subCategory[updatingndex].description = subCategoryFormValue.description;
          returnData.extended_fields.subCategory[updatingndex].tags = [];
        } else {
          this.administrationService.subCategories.extended_fields.subCategories = this.administrationService.subCategories.extended_fields.subCategory.filter(
            (subCateId) => subCateId.id !== subCategoryFormValue.id
          );
          returnData.extended_fields.subCategory = this.administrationService.subCategories.extended_fields.subCategory;
          returnData.extended_fields.subCategory.push(subCategoryFormValue);
        }
      } else {
        returnData.extended_fields.subCategory = [subCategoryFormValue];
      }
    } else {
      returnData.name = 'SubCategoryManagement';
      returnData.extended_fields.subCategory = [subCategoryFormValue];
    }
    return returnData;
  }

  ngOnDestroy() {
    this.authNoticeService.setNotice(null);
  }
}

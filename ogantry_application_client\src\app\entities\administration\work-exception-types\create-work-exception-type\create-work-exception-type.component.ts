import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Params } from '@angular/router';
import { AuthNoticeService } from '@auth/auth-notice/auth-notice.service';
import { WorkExceptionType } from '@entities/administration/administration.model';
import { AdministrationService } from '@entities/administration/administration.service';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { ButtonParams, OGantryHttpResponse } from '@shared/models';
import { KtDialogService } from '@shared/services';

@Component({
  selector: 'app-create-work-exception-type',
  templateUrl: './create-work-exception-type.component.html',
  styleUrls: ['./create-work-exception-type.component.scss']
})
export class CreateWorkExceptionTypeComponent extends SflBaseComponent implements OnInit {
  cardTitle = 'Add Work Exception Type';
  cardSubTitle = null;
  buttons: ButtonParams[] = [];
  createWorkExceptionTypeForm: FormGroup;
  typeId: number;
  workExceptionType: WorkExceptionType;
  constructor(
    private readonly administrationService: AdministrationService,
    private readonly authNoticeService: AuthNoticeService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly ktDialogService: KtDialogService
  ) {
    super();
  }
  ngOnInit(): void {
    this.loading$.next(false);
    this.initForm();
    this.getParams();
  }

  getParams() {
    this.activatedRoute.params.subscribe((params: Params) => {
      this.typeId = params.exceptionId;
      if (this.typeId) {
        this.cardTitle = 'Edit Work Exception Type';
        this.getExpenseType();
      }
      this.setBtnParams();
    });
  }

  getExpenseType() {
    this.loading$.next(true);
    this.subscriptionManager.add(
      this.administrationService.getWorkExceptionType(this.typeId).subscribe(
        (res) => {
          this.workExceptionType = res.data;
          this.setWorkExceptionTypeForm();
          this.loading$.next(false);
        },
        () => this.loading$.next(false)
      )
    );
  }

  initForm() {
    this.createWorkExceptionTypeForm = new FormGroup({
      name: new FormControl('', Validators.required)
    });
  }

  setBtnParams() {
    this.buttons = [
      {
        btnClass: 'btn btn-cancel',
        btnText: 'Cancel',
        redirectPath: this.appRoutes.MANAGE_WORK_EXCEPTION_TYPE
      },
      {
        btnClass: 'btn-save',
        btnText: 'Save',
        btnType: 'submit',
        action: this.typeId ? this.onEdit.bind(this) : this.onSave.bind(this),
        loading: this.isSubmitting
      }
    ];
  }

  onEdit() {
    if (!this.checkFormForValidation(this.createWorkExceptionTypeForm)) {
      this.isSubmitting = true;
      this.setBtnParams();
      this.subscriptionManager.add(
        this.administrationService.updateWorkExceptionType(this.createWorkExceptionTypeForm.value, this.typeId).subscribe(
          (res) => {
            this.onSuccess(res.data, 'Work Exception Type updated successfully');
          },
          (err) => this.onError(err)
        )
      );
    }
  }

  onSave() {
    if (!this.checkFormForValidation(this.createWorkExceptionTypeForm)) {
      this.isSubmitting = true;
      this.setBtnParams();
      this.subscriptionManager.add(
        this.administrationService.createWorkExceptionType(this.createWorkExceptionTypeForm.value).subscribe(
          (res) => {
            this.onSuccess(res.data, 'Work Exception Type created successfully');
          },
          (err) => this.onError(err)
        )
      );
    }
  }

  ngOnDestroy() {
    this.authNoticeService.setNotice(null);
    this.ktDialogService.hide();
  }

  onSuccess(exceptionType: WorkExceptionType, successMsg: string) {
    this.isSubmitting = false;
    this.setBtnParams();
    this.workExceptionType = exceptionType;
    this.typeId = this.workExceptionType.work_exception_type.id;
    this.setBtnParams();
    this.authNoticeService.setNotice(successMsg, 'success');
  }

  onError(err) {
    this.isSubmitting = false;
    this.setBtnParams();
    const error: OGantryHttpResponse<WorkExceptionType> = err.error;
    this.authNoticeService.setNotice(error.errors, 'danger');
  }

  setWorkExceptionTypeForm() {
    const expenseType = this.workExceptionType.work_exception_type;
    this.createWorkExceptionTypeForm.controls['name'].setValue(expenseType.name);
  }
}

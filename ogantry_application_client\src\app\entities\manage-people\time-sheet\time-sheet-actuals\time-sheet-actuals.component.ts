import { Component, OnInit, ViewChild } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ManagePeopleService } from '@entities/manage-people/manage-people.service';
import { GlobalDetailsTimeSheetFiles, OGPositionList, OGProjectList } from '@entities/manage-people/mange-people.model';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { ButtonParams } from '@shared/models';
import { Papa as ngxCSVDataParser } from 'ngx-papaparse';
import { ConfirmationService } from 'primeng/api';
import { FileUpload } from 'primeng/fileupload';
import { Observable, Subject, of } from 'rxjs';

const height = 'calc((var(--fixed-content-height, 1vh) * 100) - 180px)';

@Component({
  selector: 'app-time-sheet-actuals',
  templateUrl: './time-sheet-actuals.component.html',
  styleUrls: ['./time-sheet-actuals.component.scss'],
  providers: [ConfirmationService]
})
export class TimeSheetActualsComponent extends SflBaseComponent implements OnInit {
  positionISOptional = false;
  cardTitle = 'Import Actuals';
  cardSubTitle = null;
  showFileUploadDialog = false;
  fieldMappingDialog = false;
  errorInFileRead = false;
  timeSheetImported: boolean;
  height = height;
  resultData: any;
  showNoDataMsg;
  OGClientList = [];
  OGProjectList: OGProjectList[] = [];
  OGPositionList: OGPositionList[] = [];

  tableHistoryData = [];
  historyTableHeader = [
    {
      field: 'Date',
      header: 'Updated Date',
      inputPlaceHolder: 'Updated Date'
    },
    {
      field: 'filePath',
      header: 'File Name',
      inputPlaceHolder: 'Enter Your File Name'
    },
    { field: 'username', header: 'User Name' }
  ];
  buttons: ButtonParams[] = [
    {
      btnClass: 'btn-save',
      btnText: 'Import Timesheet',
      title: 'Import Timesheet ',
      action: this.showUploadDialog.bind(this),
      permissions: [this.permissionModules.MANAGE_IMPORT_ACTUALS]
    }
  ];
  tableHeader = [];

  // upload dialog
  uploadedCsvHeader = [];
  @ViewChild('fileUpload') fileUpload: FileUpload;
  uploadedCSVFile: any;

  //mapping dialog
  columnMappingFields = [
    {
      label: 'Client'
    },
    {
      label: 'Project'
    },
    {
      label: 'Position'
    },
    {
      label: 'Employee'
    },
    {
      label: 'Date'
    },
    {
      label: 'Hours'
    }
  ];
  columnMappingForm: FormGroup;
  uploadedCSVFileData: any;
  globalDetailsForTimeEntries: GlobalDetailsTimeSheetFiles;

  constructor(
    private fb: FormBuilder,
    private ngxCSVDataParser: ngxCSVDataParser,
    private managePeopleService: ManagePeopleService,
    private confirmationService: ConfirmationService
  ) {
    super();
  }

  ngOnInit(): void {
    this.initializeFormArray();
    this.getOGLists();
    this.managePeopleService.showTimeSheetEntries.subscribe((res: boolean) => {
      this.uploadedCSVFile = null;
      this.uploadedCsvHeader = [];
      this.onFileRemove();
      this.buttons = [
        {
          btnClass: 'btn-save',
          btnText: 'Import Timesheet',
          title: 'Import Timesheet ',
          action: this.showUploadDialog.bind(this),
          permissions: [this.permissionModules.MANAGE_IMPORT_ACTUALS]
        }
      ];
      this.getUploadedTimeSheetEntries();
    });
  }

  getOGLists() {
    this.getOGClient();
    this.getOGProject();
    this.getOGPosition();
    this.getUploadedTimeSheetEntries();
  }

  getUploadedTimeSheetEntries() {
    this.managePeopleService.getTimeSheetEntries('ActualsImportLog').subscribe((res) => {
      if (res.data.global_details[0].global_detail.extended_fields.uploadedFiles.length >= 1) {
        this.globalDetailsForTimeEntries = res.data.global_details[0];
        this.timeSheetImported = !res;
        this.showNoDataMsg = !res;
        this.tableHistoryData = res.data.global_details[0].global_detail.extended_fields.uploadedFiles;
        this.tableHistoryData.sort((a: any, b: any) => {
          const dateA = new Date(a.Date);
          const dateB = new Date(b.Date);
          return dateB.getTime() - dateA.getTime();
        });
      }
    });
  }

  initializeFormArray() {
    this.columnMappingForm = this.fb.group({
      Client: ['', Validators.required],
      Project: ['', Validators.required],
      Position: ['', Validators.required],
      Employee: ['', Validators.required],
      Date: ['', Validators.required],
      Hours: ['', Validators.required]
    });
  }

  showUploadDialog() {
    this.showFileUploadDialog = true;
  }

  importFile() {
    const reader = new FileReader();

    reader.onload = () => {
      const csvData = reader.result as string;
      this.ngxCSVDataParser.parse(csvData, {
        header: true,
        dynamicTyping: true,
        skipEmptyLines: true,

        complete: (result) => {
          this.resultData = result;
          this.uploadedCSVFileData = result.data.filter((row) => Object.values(row).some((value) => value !== null && value !== ''));
        },

        transformHeader: (header: string) => {
          if (!this.uploadedCsvHeader.includes(header)) {
            this.uploadedCsvHeader.push(header);
          }
          const savedSelectedColumn = JSON.parse(localStorage.getItem('selectedColumnMappingForTimeSheet'));
          if (savedSelectedColumn) {
            if (savedSelectedColumn.uploadedFileName === this.uploadedCSVFile.name) {
              this.columnMappingForm.patchValue(savedSelectedColumn.selectedColumns);
              this.updatePositionValidator();
            }
          }
          return header;
        },

        error: (error) => {
          console.error('CSV Parsing Error:', error.message);
        }
      });
    };

    reader.readAsText(this.uploadedCSVFile);
    this.tableHeader = [];
    this.showFileUploadDialog = false;
    this.fieldMappingDialog = true;
  }

  hideUploadModal() {
    this.showFileUploadDialog = false;
  }
  hideFieldMappingDialog() {
    this.fieldMappingDialog = false;
  }

  submitFieldMapping() {
    const nameControl = this.columnMappingForm.get('Position');
    nameControl.enable();
    const errorState = this.resultData.data.filter((row) => {
      if (row[this.columnMappingForm.value.Date] !== null || row[this.columnMappingForm.value.Hours] !== null) {
        if (
          row[this.columnMappingForm.value.Client] === null ||
          row[this.columnMappingForm.value.Employee] === null ||
          row[this.columnMappingForm.value.Project] === null ||
          this.checkPositionIsCompulsory(row)
        ) {
          return row;
        }
      }
    });
    if (errorState?.length) {
      this.errorInFileRead = true;
      this.fieldMappingDialog = false;
      return;
    }
    this.fieldMappingDialog = false;
    this.timeSheetImported = true;
    const selectedFileData = {
      uploadedFileName: this.uploadedCSVFile.name,
      selectedColumns: this.columnMappingForm.value
    };
    localStorage.setItem('selectedColumnMappingForTimeSheet', JSON.stringify(selectedFileData));

    this.tableHeader = [];
    this.buttons = [
      {
        btnClass: 'btn-save',
        btnText: 'Import Timesheet',
        title: 'Import Timesheet ',
        action: this.showAlertForReimport.bind(this),
        permissions: [this.permissionModules.MANAGE_IMPORT_ACTUALS]
      }
    ];
    this.fileUpload.clear();
    this.dataMapping();
  }

  showAlertForReimport() {
    return this.showAlert({
      message: `Your changes haven't been saved. Do you want to proceed further?`,
      header: 'Alert',
      actions: { acceptAction: this.showUploadDialog.bind(this) }
    });
  }

  onDropdownChange(event: any, field: string) {
    if (event.value === null) {
      this.columnMappingForm.get(field).setValue(null);
    }
  }

  getOGClient() {
    this.subscriptionManager.add(
      this.managePeopleService.getClientData({ is_active: true, order_by: 'asc:name' }).subscribe((res) => {
        res.body.data.customers.map((cust) => {
          this.OGClientList.push(cust.customer.name);
        });
      })
    );
  }

  getOGProject() {
    this.subscriptionManager.add(
      this.managePeopleService.getProjectList().subscribe((res) => {
        res.data.projects.forEach((project) => {
          this.OGProjectList.push({
            projectName: project.project.name,
            clientName: project.project['customer'].name
          });
        });
      })
    );
  }

  getOGPosition() {
    this.subscriptionManager.add(
      this.managePeopleService.getPositionList().subscribe((res) => {
        res?.data?.positions?.map((position) => {
          if (position.position.name !== 'project_expenses') {
            this.OGPositionList.push({
              clientName: position.position.project.customer.name,
              positionId: position.position.id,
              positionName: position.position.name,
              projectName: position.position.project.name,
              employeeFullName: position.position.employee.first_name + ' ' + position.position.employee.last_name,
              startDate: position.position.start_date,
              endDate: position.position.end_date
            });
          }
        });
      })
    );
  }

  dataMapping() {
    if (this.positionISOptional) {
      this.updateMissingPosition(this.OGPositionList);
    }
    this.mapTableHeader();
    this.uploadedCSVFileData = this.mapCSVData();
  }

  mapTableHeader() {
    this.tableHeader = Object.keys(this.columnMappingForm.value).map((key) => ({
      header: key,
      columnField: this.columnMappingForm.value[key]
    }));
  }

  mapCSVData() {
    return this.uploadedCSVFileData.map((rowData) => {
      const CSVDataMapping = this.mapCSVRowData(rowData);

      const selectedOptions = this.columnMappingForm.value;

      this.managePeopleService.isMatchWithOG(CSVDataMapping, selectedOptions, this.OGClientList, this.OGProjectList, this.OGPositionList, true);

      return CSVDataMapping;
    });
  }

  mapCSVRowData(rowData) {
    const CSVDataMapping = {};

    Object.keys(this.columnMappingForm.value).forEach((key) => {
      const formControlValue = this.columnMappingForm.value[key];

      if (rowData.hasOwnProperty(formControlValue)) {
        CSVDataMapping[formControlValue] = {
          value: rowData[formControlValue],
          isMatchWithOG: null
        };
        if (formControlValue === 'Position') {
          CSVDataMapping[formControlValue]['positionid'] = rowData;
        }
      }
    });

    return CSVDataMapping;
  }

  onCsvUpload(event: any) {
    const file = event.files[0];
    this.uploadedCsvHeader = [];
    this.uploadedCSVFile = event.files[0];
  }

  onFileRemove(event?: any) {
    this.uploadedCSVFile = null;
  }

  showAlert({ message, header, actions }: { message: string; header: string; actions?: { acceptAction: () => void; rejectAction?: () => void } }): Observable<boolean> {
    const subject = new Subject<boolean>();

    this.confirmationService.confirm({
      message: message,
      header: header,
      icon: 'pi pi-info-circle',
      acceptIcon: 'none',
      rejectIcon: 'none',
      rejectButtonStyleClass: 'p-button-text',
      accept: () => {
        if (actions?.acceptAction) {
          actions?.acceptAction();
        }
        subject.next(true);
        subject.complete();
      },
      reject: () => {
        subject.next(false);
        subject.complete();
      },
      key: 'positionDialog'
    });

    return subject.asObservable();
  }

  showAlertForCanDeactivate({
    message,
    header,
    actions
  }: {
    message: string;
    header: string;
    actions?: { acceptAction: () => void; rejectAction?: () => void };
  }): Observable<boolean> {
    const subject = new Subject<boolean>();

    this.confirmationService.confirm({
      message: message,
      header: header,
      icon: 'pi pi-info-circle',
      acceptIcon: 'none',
      rejectIcon: 'none',
      rejectButtonStyleClass: 'p-button-text',
      acceptLabel: 'Abandon Changes',
      rejectLabel: 'Stay Here',
      accept: () => {
        if (actions?.acceptAction) {
          actions?.acceptAction();
        }
        subject.next(true);
        subject.complete();
      },
      reject: () => {
        subject.next(false);
        subject.complete();
      },
      key: 'positionDialog'
    });

    return subject.asObservable();
  }

  canDeactivate(): Observable<boolean> | boolean {
    if (this.timeSheetImported) {
      return this.showAlertForCanDeactivate({
        message: `Your changes have not been saved.`,
        header: 'Alert'
      });
    } else {
      return of(true);
    }
  }

  updatePositionValidator(): void {
    const nameControl = this.columnMappingForm.get('Position');
    this.positionISOptional ? this.disableController(nameControl) : this.enableController(nameControl);
    nameControl.updateValueAndValidity();
  }

  disableController(nameControl: AbstractControl): void {
    nameControl.enable();
    // nameControl.clearValidators();
    this.uploadedCsvHeader.push('Position');
    this.columnMappingForm.patchValue({
      Position: 'Position'
    });
    nameControl.updateValueAndValidity();
    nameControl.disable({ emitEvent: false });
  }

  enableController(nameControl: AbstractControl): void {
    nameControl.enable();
    this.uploadedCsvHeader = this.uploadedCsvHeader.filter((data) => data !== 'Position');
    this.columnMappingForm.patchValue({
      Position: null
    });
    this.columnMappingForm.markAllAsTouched();
    this.columnMappingForm.markAsDirty();
    // nameControl.setValidators([Validators.required]);
    nameControl.updateValueAndValidity();
  }

  // OGPositionList
  //

  updateMissingPosition(postionList) {
    this.uploadedCSVFileData = this.uploadedCSVFileData.map((row) => {
      let selectedPos = postionList.filter((res) => res.projectName === row['Project Name'] && res.clientName === row['Client Name'] && res.employeeFullName === row['Employee']);
      if (selectedPos.length === 0) {
        row.Position = null;
        return row;
      } else if (selectedPos.length === 1) {
        row.Position = selectedPos[0].positionName;
        return row;
      } else if (selectedPos.length > 1) {
        selectedPos = selectedPos.filter((res) => {
          this.isDateInRange(res.Date, res.start_date, res.end_date);
        });
        // row.positionId = selectedPos[0].positionId;
        if (selectedPos.length > 1) {
          row.Position = selectedPos[0].positionName;
        }
        return row;
      }
    });
  }
  isDateInRange(dateStr: string, startDateStr: string, endDateStr: string): boolean {
    // Parse the dates into Date objects
    const date = new Date(dateStr);
    const startDate = new Date(startDateStr);
    const endDate = new Date(endDateStr);

    // Check if the date is greater than or equal to the start date
    // and less than or equal to the end date
    return date >= startDate && date <= endDate;
  }

  checkPositionIsCompulsory(row): boolean {
    return this.positionISOptional ? false : row[this.columnMappingForm.value.Position] === null ? true : false;
  }
  // Example usage
  // const dateToCheck = "2024-10-01"; // Change this to your desired date
  // const startDate = "2024-09-01";
  // const endDate = "2024-09-30";

  // if (isDateInRange(dateToCheck, startDate, endDate)) {
  //   console.log(`${dateToCheck} is within the range of ${startDate} and ${endDate}`);
  // } else {
  //   console.log(`${dateToCheck} is not within the range of ${startDate} and ${endDate}`);
  // }
}

import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Params } from '@angular/router';
import { AuthNoticeService } from '@auth/auth-notice/auth-notice.service';
import { AdministrationService } from '@entities/administration/administration.service';
import { DailyExpenseType } from '@entities/project/project.model';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { ButtonParams, OGantryHttpResponse } from '@shared/models';
import { KtDialogService } from '@shared/services';

@Component({
  selector: 'app-create-expense-types',
  templateUrl: './create-expense-types.component.html',
  styleUrls: ['./create-expense-types.component.scss']
})
export class CreateExpenseTypesComponent extends SflBaseComponent implements OnInit {
  cardTitle = 'Add Daily Expense Type';
  cardSubTitle = null;
  buttons: ButtonParams[] = [];
  createExpenseTypeForm: FormGroup;
  typeId: number;
  expenseType: DailyExpenseType;
  constructor(
    private readonly administrationService: AdministrationService,
    private readonly authNoticeService: AuthNoticeService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly ktDialogService: KtDialogService
  ) {
    super();
  }
  ngOnInit(): void {
    this.loading$.next(false);
    this.initForm();
    this.getParams();
  }

  getParams() {
    this.activatedRoute.params.subscribe((params: Params) => {
      this.typeId = params.typeId;
      if (this.typeId) {
        this.cardTitle = 'Edit Daily Expense Type';
        this.getExpenseType();
      }
      this.setBtnParams();
    });
  }

  getExpenseType() {
    this.loading$.next(true);
    this.subscriptionManager.add(
      this.administrationService.getExpenseType(this.typeId).subscribe(
        (res) => {
          this.expenseType = res.data;
          this.setExpenseTypeForm();
          this.loading$.next(false);
        },
        () => this.loading$.next(false)
      )
    );
  }

  initForm() {
    this.createExpenseTypeForm = new FormGroup({
      name: new FormControl('', Validators.required)
    });
  }

  setBtnParams() {
    this.buttons = [
      {
        btnClass: 'btn btn-cancel',
        btnText: 'Cancel',
        redirectPath: this.appRoutes.MANAGE_EXPENSE_TYP
      },
      {
        btnClass: 'btn-save',
        btnText: 'Save',
        btnType: 'submit',
        action: this.typeId ? this.onEdit.bind(this) : this.onSave.bind(this),
        loading: this.isSubmitting
      }
    ];
  }

  onEdit() {
    if (!this.checkFormForValidation(this.createExpenseTypeForm)) {
      this.isSubmitting = true;
      this.setBtnParams();
      this.subscriptionManager.add(
        this.administrationService.updateExpenseType(this.createExpenseTypeForm.value, this.typeId).subscribe(
          (res) => {
            this.onSuccess(res.data, 'Daily Expense Type updated successfully');
          },
          (err) => this.onError(err)
        )
      );
    }
  }

  onSave() {
    if (!this.checkFormForValidation(this.createExpenseTypeForm)) {
      this.isSubmitting = true;
      this.setBtnParams();
      this.subscriptionManager.add(
        this.administrationService.createExpenseType(this.createExpenseTypeForm.value).subscribe(
          (res) => {
            this.onSuccess(res.data, 'Daily Expense Type created successfully');
          },
          (err) => this.onError(err)
        )
      );
    }
  }

  ngOnDestroy() {
    this.authNoticeService.setNotice(null);
    this.ktDialogService.hide();
  }

  onSuccess(expenseType: DailyExpenseType, successMsg: string) {
    this.isSubmitting = false;
    this.setBtnParams();
    this.expenseType = expenseType;
    this.typeId = this.expenseType.daily_expense_type.id;
    this.setBtnParams();
    this.authNoticeService.setNotice(successMsg, 'success');
  }

  onError(err) {
    this.isSubmitting = false;
    this.setBtnParams();
    const error: OGantryHttpResponse<DailyExpenseType> = err.error;
    this.authNoticeService.setNotice(error.errors, 'danger');
  }

  setExpenseTypeForm() {
    const expenseType = this.expenseType.daily_expense_type;
    this.createExpenseTypeForm.controls['name'].setValue(expenseType.name);
  }
}

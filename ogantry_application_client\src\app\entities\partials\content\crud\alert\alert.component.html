<div class="mat-alert  mat-alert-{{ type }}" role="alert">
  <div class="mat-alert-icon">
    <em class="la la-warning"></em>
  </div>
  <div class="mat-alert-text">
    <ng-content></ng-content>
  </div>
  <div class="mat-alert-close" *ngIf="showCloseButton">
    <button type="button" mat-icon-button color="warn" (click)="closeAlert()">
      <mat-icon class="material-icons">clear</mat-icon>
    </button>
  </div>
</div>

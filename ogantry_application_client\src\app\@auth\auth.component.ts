// Angular
import { Component, ElementRef, OnDestroy, OnInit, Renderer2, ViewEncapsulation } from '@angular/core';
import { AuthNoticeService } from './auth-notice/auth-notice.service';
import { LayoutConfigService } from '@shared/services/layout-config.service';
import { TranslationService } from '@shared/services/translation.service';
import { AuthService } from './auth.service';
import { Subscription } from 'rxjs';
const version = require('../../../package.json');

@Component({
  selector: 'kt-auth',
  templateUrl: './auth.component.html',
  styleUrls: ['./auth.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class AuthComponent implements OnInit, OnDestroy {
  // Public properties
  today: number = Date.now();
  headerLogo: string;
  currentApplicationVersion = version.version;
  backendVersion = '';
  subscriptionManager: Subscription = new Subscription();

  /**
   * Component constructor
   *
   * @param el
   * @param render
   * @param layoutConfigService: LayoutConfigService
   * @param authNoticeService: authNoticeService
   * @param translationService: TranslationService
   * @param splashScreenService: SplashScreenService
   */
  constructor(
    private readonly el: ElementRef,
    private readonly render: Renderer2,
    private readonly layoutConfigService: LayoutConfigService,
    public readonly authNoticeService: AuthNoticeService,
    private readonly translationService: TranslationService,
    private readonly authService: AuthService
  ) {}

  /**
   * @ Lifecycle sequences => https://angular.io/guide/lifecycle-hooks
   */

  /**
   * On init
   */
  ngOnInit(): void {
    this.translationService.setLanguage(this.translationService.getSelectedLanguage());
    this.headerLogo = this.layoutConfigService.getLogo();
    this.subscriptionManager.add(
      this.authService.getBackendBuildVersion().subscribe((res) => {
        this.backendVersion = res?.data?.release_version?.version;
      })
    );
  }

  ngOnDestroy(): void {
    this.subscriptionManager.unsubscribe();
  }

  /**
   * Load CSS for this specific page only, and destroy when navigate away
   * @param styleUrl
   */
  private loadCSS(styleUrl: string) {
    return new Promise((resolve, reject) => {
      const styleElement = document.createElement('link');
      styleElement.href = styleUrl;
      styleElement.type = 'text/css';
      styleElement.rel = 'stylesheet';
      styleElement.onload = resolve;
      this.render.appendChild(this.el.nativeElement, styleElement);
    });
  }
}

{"name": "ogantry", "version": "2.2.6", "scripts": {"ng": "ng", "start": "ng serve", "prebuild": "npm --no-git-tag-version version patch", "build": "ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "postinstall": "ngcc --properties es2015 browser module main --first-only --create-ivy-entry-points", "bundle-report": "webpack-bundle-analyzer dist/stats.json", "rtl": "webpack", "generatereport": "ngxtslintreport", "prettier": "prettier --write ."}, "private": true, "dependencies": {"@angular/animations": "12.2.3", "@angular/cdk": "^12.2.3", "@angular/common": "12.2.3", "@angular/compiler": "12.2.3", "@angular/core": "12.2.3", "@angular/forms": "12.2.3", "@angular/localize": "^12.2.3", "@angular/platform-browser": "12.2.3", "@angular/platform-browser-dynamic": "12.2.3", "@angular/platform-server": "12.2.3", "@angular/router": "12.2.3", "@angular/service-worker": "12.2.3", "@fortawesome/angular-fontawesome": "^0.10.1", "@fortawesome/fontawesome-free": "6.1.0", "@fortawesome/fontawesome-svg-core": "6.1.0", "@fortawesome/free-regular-svg-icons": "6.1.0", "@fortawesome/free-solid-svg-icons": "6.1.0", "@mobiscroll/angular": "5.24.1", "@ng-bootstrap/ng-bootstrap": "7.0.0", "@ngx-loading-bar/core": "4.2.0", "@ngx-translate/core": "12.1.2", "@sentry/browser": "^5.21.1", "@types/lodash": "4.14.150", "bootstrap": "4.5.0", "chart.js": "2.9.3", "chartist": "0.11.4", "classlist.js": "1.1.20150312", "clipboard": "2.0.6", "core-js": "3.6.5", "crypto-js": "^4.0.0", "exceljs": "3.10.0", "file-saver": "^2.0.5", "jspdf": "^2.2.0", "jspdf-autotable": "^3.5.13", "lodash": "^4.17.20", "material-design-icons": "3.0.1", "moment": "2.24.0", "ng-inline-svg": "10.0.0", "ngx-papaparse": "^5.0.0", "ngx-perfect-scrollbar": "9.0.0", "ngx-tslint-report": "^1.2.0", "object-path": "^0.11.8", "popper.js": "^1.16.1", "prettier": "2.8.8", "primeflex": "^2.0.0", "primeicons": "^4.0.0", "primeng": "^13.0.4", "rxjs": "6.5.5", "sass": "^1.38.2", "socicon": "3.0.5", "tooltip.js": "^1.3.3", "tslib": "^2.0.0", "web-animations-js": "2.3.2", "xlsx": "^0.17.2", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "^12.2.3", "@angular/cli": "12.2.3", "@angular/compiler-cli": "12.2.3", "@angular/language-service": "12.2.3", "@angular/material": "^12.2.3", "@angular/material-moment-adapter": "10.1.3", "@types/chartist": "0.9.47", "@types/jasmine": "~3.6.0", "@types/jasminewd2": "2.0.3", "@types/node": "12.11.1", "@types/object-path": "0.11.0", "codelyzer": "^6.0.0", "css-loader": "^3.4.2", "jasmine-core": "~3.6.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.3.4", "karma-chrome-launcher": "~3.1.0", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "^1.5.0", "protractor": "~7.0.0", "ts-node": "8.3.0", "tslint": "~6.1.0", "typescript": "4.3.5", "webpack-bundle-analyzer": "3.6.1", "webpack-cli": "3.3.11", "webpack-messages": "2.0.4", "webpack-rtl-plugin": "2.0.0"}, "browser": {"crypto": false}, "resolutions": {"exceljs/tmp": "0.1.0"}}
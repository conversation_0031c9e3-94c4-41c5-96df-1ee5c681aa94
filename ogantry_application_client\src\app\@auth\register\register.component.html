<div class="d-flex flex-column-fluid flex-center mt-30 mt-lg-0">
  <!--begin::Signup-->
  <div class="login-form login-signup" style="display: block">
    <div class="text-center mb-10 mb-lg-20">
      <h3 class="font-size-h1">Sign Up</h3>
      <p class="text-muted font-weight-bold">Enter your details to create your account</p>
    </div>

    <kt-auth-notice></kt-auth-notice>

    <form class="form" [formGroup]="registerForm" autocomplete="off">
      <div class="form-group">
        <mat-form-field>
          <mat-label>Fullname</mat-label>
          <input matInput type="text" placeholder="Fullname" formControlName="fullname" />
          <mat-error *ngIf="isControlHasError(registerForm, 'fullname', 'required')">
            <strong>{{ 'AUTH.VALIDATION.REQUIRED_FIELD' | translate }}</strong>
          </mat-error>
          <mat-error *ngIf="isControlHasError(registerForm, 'fullname', 'minlength')">
            <strong>{{ 'AUTH.VALIDATION.MIN_LENGTH_FIELD' | translate }} 3</strong>
          </mat-error>
          <mat-error *ngIf="isControlHasError(registerForm, 'fullname', 'maxlength')">
            <strong>{{ 'AUTH.VALIDATION.MAX_LENGTH_FIELD' | translate }} 100</strong>
          </mat-error>
        </mat-form-field>
      </div>
      <div class="form-group">
        <mat-form-field>
          <mat-label>{{ 'AUTH.INPUT.EMAIL' | translate }}</mat-label>
          <input matInput type="email" placeholder="{{ 'AUTH.INPUT.EMAIL' | translate }}" formControlName="email" autocomplete="off" />
          <mat-error *ngIf="isControlHasError(registerForm, 'email', 'required')">
            <strong>{{ 'AUTH.VALIDATION.REQUIRED_FIELD' | translate }}</strong>
          </mat-error>
          <mat-error *ngIf="isControlHasError(registerForm, 'email', 'email')">
            <strong>{{ 'AUTH.VALIDATION.INVALID_FIELD' | translate }}</strong>
          </mat-error>
          <mat-error *ngIf="isControlHasError(registerForm, 'email', 'minlength')">
            <strong>{{ 'AUTH.VALIDATION.MIN_LENGTH_FIELD' | translate }} 3</strong>
          </mat-error>
          <mat-error *ngIf="isControlHasError(registerForm, 'email', 'maxlength')">
            <strong>{{ 'AUTH.VALIDATION.MAX_LENGTH_FIELD' | translate }} 320</strong>
          </mat-error>
        </mat-form-field>
      </div>
      <div class="form-group">
        <mat-form-field>
          <mat-label>Username</mat-label>
          <input matInput type="text" placeholder="Username" formControlName="username" />
          <mat-error *ngIf="isControlHasError(registerForm, 'username', 'required')">
            <strong>{{ 'AUTH.VALIDATION.REQUIRED_FIELD' | translate }}</strong>
          </mat-error>
          <mat-error *ngIf="isControlHasError(registerForm, 'username', 'minlength')">
            <strong>{{ 'AUTH.VALIDATION.MIN_LENGTH_FIELD' | translate }} 3</strong>
          </mat-error>
          <mat-error *ngIf="isControlHasError(registerForm, 'username', 'maxlength')">
            <strong>{{ 'AUTH.VALIDATION.MAX_LENGTH_FIELD' | translate }} 100</strong>
          </mat-error>
        </mat-form-field>
      </div>
      <div class="form-group">
        <mat-form-field>
          <mat-label>{{ 'AUTH.INPUT.PASSWORD' | translate }}</mat-label>
          <input matInput type="password" placeholder="{{ 'AUTH.INPUT.PASSWORD' | translate }}" formControlName="password" autocomplete="off" />
          <mat-error *ngIf="isControlHasError(registerForm, 'password', 'required')">
            <strong>{{ 'AUTH.VALIDATION.REQUIRED_FIELD' | translate }}</strong>
          </mat-error>
          <mat-error *ngIf="isControlHasError(registerForm, 'password', 'minlength')">
            <strong>{{ 'AUTH.VALIDATION.MIN_LENGTH_FIELD' | translate }} 3</strong>
          </mat-error>
          <mat-error *ngIf="isControlHasError(registerForm, 'password', 'maxlength')">
            <strong>{{ 'AUTH.VALIDATION.MAX_LENGTH_FIELD' | translate }} 100</strong>
          </mat-error>
        </mat-form-field>
      </div>
      <div class="form-group">
        <mat-form-field>
          <mat-label>{{ 'AUTH.INPUT.CONFIRM_PASSWORD' | translate }}</mat-label>
          <input matInput type="password" placeholder="{{ 'AUTH.INPUT.CONFIRM_PASSWORD' | translate }}" formControlName="confirmPassword" autocomplete="off" />
          <mat-error *ngIf="isControlHasError(registerForm, 'confirmPassword', 'required')">
            <strong>{{ 'AUTH.VALIDATION.REQUIRED_FIELD' | translate }}</strong>
          </mat-error>
          <mat-error *ngIf="isControlHasError(registerForm, 'confirmPassword', 'minlength')">
            <strong>{{ 'AUTH.VALIDATION.MIN_LENGTH_FIELD' | translate }} 3</strong>
          </mat-error>
          <mat-error *ngIf="isControlHasError(registerForm, 'confirmPassword', 'maxlength')">
            <strong>{{ 'AUTH.VALIDATION.MAX_LENGTH_FIELD' | translate }} 100</strong>
          </mat-error>
          <mat-error *ngIf="registerForm.get('confirmPassword').errors && registerForm.get('confirmPassword').errors.ConfirmPassword">
            <strong>Passsword and Confirm Password didn't match.</strong>
          </mat-error>
        </mat-form-field>
      </div>
      <div class="form-group">
        <mat-checkbox name="agree" formControlName="agree"> I agree the <a href="javascript:;">terms & conditions</a> </mat-checkbox>
      </div>

      <!--begin::Action-->
      <div class="form-group d-flex flex-wrap justify-content-end">
        <a [routerLink]="appRoutes.LOGIN" id="kt_login_signup_cancel" class="btn btn-light-primary font-weight-bold px-9 py-4 my-3 mx-4">Cancel</a>
        <button
          (click)="submit()"
          id="kt_login_signup_submit"
          class="btn btn-primary font-weight-bold px-9 py-4 my-3"
          [ngClass]="{ 'spinner spinner-right spinner-md spinner-light': loading }"
        >
          {{ 'AUTH.GENERAL.SUBMIT_BUTTON' | translate }}
        </button>
      </div>
      <!--end::Action-->
    </form>
  </div>
  <!--end::Signup-->
</div>

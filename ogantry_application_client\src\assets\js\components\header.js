"use strict";var KTHeader=function(e,t){var r=this,n=KTUtil.getById(e),o=KTUtil.getBody();if(void 0!==n){var i={offset:{desktop:!0,tabletAndMobile:!0},releseOnReverse:{desktop:!1,tabletAndMobile:!1}},l={construct:function(e){return KTUtil.data(n).has("header")?r=KTUtil.data(n).get("header"):(l.init(e),l.build(),KTUtil.data(n).set("header",r)),r},init:function(e){r.events=[],r.options=KTUtil.deepExtend({},i,e)},build:function(){var e=!0,t=0;window.addEventListener("scroll",function(){var n,i=0;KTUtil.isBreakpointDown("lg")&&!1===r.options.offset.tabletAndMobile||KTUtil.isBreakpointUp("lg")&&!1===r.options.offset.desktop||(KTUtil.isBreakpointUp("lg")?i=r.options.offset.desktop:KTUtil.isBreakpointDown("lg")&&(i=r.options.offset.tabletAndMobile),n=KTUtil.getScrollTop(),KTUtil.isBreakpointDown("lg")&&r.options.releseOnReverse.tabletAndMobile||KTUtil.isBreakpointUp("lg")&&r.options.releseOnReverse.desktop?(n>i&&t<n?(!1===o.hasAttribute("data-header-scroll")&&o.setAttribute("data-header-scroll","on"),e&&(l.eventTrigger("scrollOn",r),e=!1)):(!0===o.hasAttribute("data-header-scroll")&&o.removeAttribute("data-header-scroll"),0==e&&(l.eventTrigger("scrollOff",r),e=!0)),t=n):n>i?(!1===o.hasAttribute("data-header-scroll")&&o.setAttribute("data-header-scroll","on"),e&&(l.eventTrigger("scrollOn",r),e=!1)):(!0===o.hasAttribute("data-header-scroll")&&o.removeAttribute("data-header-scroll"),0==e&&(l.eventTrigger("scrollOff",r),e=!0)))})},eventTrigger:function(e,t){for(var n=0;n<r.events.length;n++){var o=r.events[n];if(o.name==e){if(1!=o.one)return o.handler.call(this,r,t);if(0==o.fired)return r.events[n].fired=!0,o.handler.call(this,r,t)}}},addEvent:function(e,t,n){r.events.push({name:e,handler:t,one:n,fired:!1})}};return r.setDefaults=function(e){i=e},r.on=function(e,t){return l.addEvent(e,t)},l.construct.apply(r,[t]),!0,r}};"undefined"!=typeof module&&void 0!==module.exports&&(module.exports=KTHeader);

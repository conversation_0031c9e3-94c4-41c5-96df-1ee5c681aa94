<div class="d-flex flex-column-fluid flex-center mt-lg-0 login-page">
  <!--begin::Signin-->
  <div class="login-form login-signin">
    <div class="mb-4 pb-2">
      <h3 class="font-size-h1 d-flex justify-content-center">Welcome to OGantry</h3>
    </div>

    <kt-auth-notice></kt-auth-notice>

    <!--begin::Form-->
    <form class="form" [formGroup]="loginForm" autocomplete="off" novalidate="novalidate" id="kt_login_form">
      <ng-container *ngIf="showPasswordBasedLogin">
        <ng-container [ngTemplateOutlet]="passwordBasedLogin"> </ng-container>
        <div class="form-group d-flex flex-wrap justify-content-between align-items-center">
          <ng-template [ngTemplateOutlet]="signInPasswordBtn"></ng-template>
          <div class="or"><span>OR</span></div>
          <ng-template [ngTemplateOutlet]="socialMediaLogin"></ng-template>
        </div>
      </ng-container>
      <ng-container *ngIf="!showPasswordBasedLogin">
        <div class="form-group d-flex flex-wrap justify-content-between align-items-center">
          <ng-container [ngTemplateOutlet]="socialMediaLogin"></ng-container>
          <div class="or"><span>OR</span></div>
          <ng-template [ngTemplateOutlet]="signInPasswordBtn"></ng-template>
        </div>
      </ng-container>
    </form>
    <!--end::Form-->
  </div>
  <!--end::Signin-->
</div>

<ng-template #passwordBasedLogin [formGroup]="loginForm">
  <div class="form-group">
    <label class="form-label">Email</label>
    <input type="email" class="form-control custom" required placeholder="Email" formControlName="email" />
    <small *ngIf="isControlHasError(loginForm, 'email', 'required')" class="form-text text-danger"> Email is required </small>
    <small *ngIf="isControlHasError(loginForm, 'email', 'pattern')" class="form-text text-danger">
      {{ 'AUTH.VALIDATION.INVALID_FIELD' | translate }}
    </small>
  </div>
  <div class="form-group">
    <div>
      <label class="form-label">Password</label>
    </div>
    <input type="password" class="form-control custom" placeholder="Password" required formControlName="password" autocomplete="off" sflPasswordEye />
    <small *ngIf="isControlHasError(loginForm, 'password', 'required')" class="form-text text-danger"> Password is required </small>
  </div>
</ng-template>

<ng-template #socialMediaLogin>
  <button
    id="kt_login_signin_submit_google"
    (click)="initiateSocialLogin(socialLoginOpts.GOOGLE)"
    [isSubmitting]="buttonProgress.googleLogin"
    class="custom btn btn-outline-primary font-weight-bold px-6 py-4"
  >
    <span><img src="assets/media/svg/social-icons/google.svg" alt="google" class="pr-3" /></span>Sign in with Google
  </button>
  <button
    id="kt_login_signin_submit_google"
    (click)="initiateSocialLogin(socialLoginOpts.MICROSOFT)"
    [isSubmitting]="buttonProgress.microsoftLogin"
    class="custom btn btn-outline-primary font-weight-bold px-6 py-4"
  >
    <span><img src="assets/media/svg/social-icons/microsoft.svg" alt="microsoft" class="pr-3" /></span>Sign in with Microsoft
  </button>
</ng-template>

<ng-template #signInPasswordBtn>
  <button id="kt_login_signin_submit" (click)="submit()" [isSubmitting]="isSubmitting" class="custom btn btn-primary font-weight-bold px-9 py-4">
    {{ showPasswordBasedLogin ? 'Sign In' : 'Sign In With Email' }}
  </button>
</ng-template>

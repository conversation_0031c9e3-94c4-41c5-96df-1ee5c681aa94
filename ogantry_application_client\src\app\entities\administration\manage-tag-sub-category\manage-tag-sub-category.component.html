<div class="card card-custom gutter-b" id="manageTagCategory">
  <app-card-header [cardTitle]="cardTitle" [cardSubTitle]="cardSubTitle" [buttons]="buttons"></app-card-header>
  <div class="card-body">
    <p-table
      [value]="tagCategories"
      dataKey="id"
      [lazy]="true"
      [rows]="10"
      [loading]="loading"
      styleClass="p-datatable-customers"
      [filterDelay]="0"
      [sortField]="'name'"
      [sortOrder]="-1"
    >
      <ng-template pTemplate="header">
        <tr>
          <th id="expand" style="width: 5rem"></th>
          <th id="cate-name">Category</th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-tagCategory let-expanded="expanded">
        <tr>
          <td>
            <button
              type="button"
              pButton
              pRipple
              [pRowToggler]="tagCategory"
              class="p-button-text p-button-rounded p-button-plain"
              [icon]="expanded ? 'pi pi-chevron-down' : 'pi pi-chevron-right'"
            ></button>
          </td>
          <td>{{ tagCategory.name }}</td>
        </tr>
      </ng-template>
      <ng-template pTemplate="rowexpansion" let-subTagCategory>
        <tr>
          <td colspan="7">
            <div class="sub-category-section">
              <p-table [value]="subTagCategory?.subTagCategory" dataKey="id">
                <ng-template pTemplate="header">
                  <tr>
                    <th id="sub-cate-name">Sub Category</th>
                    <th id="action">Action</th>
                  </tr>
                </ng-template>
                <ng-template pTemplate="body" let-subTagCategory>
                  <tr>
                    <td>{{ subTagCategory.name }}</td>
                    <td>
                      <a
                        class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary"
                        *hasAnyPermission="permissionModules.MANAGE_TAG_CATEGORY; disableEvent: true"
                        [routerLink]="[appRoutes.EDIT_TAG_SUB_CATEGORY, subTagCategory?.id]"
                      >
                        <span title="Edit" [inlineSVG]="'assets/media/svg/icons/edit.svg'" cacheSVG="true" class="svg-icon svg-icon-md"> </span>
                      </a>
                      <a
                        class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary ml-3"
                        *hasAnyPermission="permissionModules.MANAGE_TAG_CATEGORY; disableEvent: true"
                        (click)="confirmDeleteCategory(subTagCategory?.id)"
                      >
                        <span title="Archive" [inlineSVG]="'assets/media/svg/icons/archive.svg'" cacheSVG="true" class="svg-icon svg-icon-md"> </span>
                      </a>
                    </td>
                  </tr>
                </ng-template>
                <ng-template pTemplate="emptymessage">
                  <tr>
                    <td colspan="6">There are no sub category yet.</td>
                  </tr>
                </ng-template>
              </p-table>
            </div>
          </td>
        </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="6">There are no category yet.</td>
        </tr>
      </ng-template>
    </p-table>
  </div>
</div>

<p-dialog header="Delete Project" [(visible)]="showDeleteDialog" [modal]="true" class="confirm-dialog" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
  <h5 class="p-m-0">Are you sure you want to delete this tag sub category?</h5>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center">
      <button type="button" class="btn-cancel" (click)="closeModal()">No</button>
      <button type="button" class="btn-save" (click)="deleteCategory()" [isSubmitting]="isSubmitting">Yes</button>
    </div>
  </ng-template>
</p-dialog>

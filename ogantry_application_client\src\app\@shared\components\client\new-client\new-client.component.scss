.add-contact .card-body.create-card {
  box-shadow: none;
  margin: 0;
  padding: 20px !important;
}

.form-check {
  bottom: 4px;
  position: absolute;
}

.form-group {
  padding-bottom: 0;
  &:first-child {
    padding-top: 0;
  }
}

::ng-deep .mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-bar {
  background-color: #3acad1;
}

::ng-deep .mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-thumb {
  background-color: #109aa1;
}

::ng-deep .mat-slide-toggle .mat-slide-toggle-bar {
  background-color: rgba(220, 47, 68, 0.54);
}

::ng-deep .mat-slide-toggle .mat-slide-toggle-thumb {
  background-color: #dc2f44;
}

.status-toggle {
  margin-left: 3rem;
  font-size: 14px;
}
textarea {
  resize: none;
}

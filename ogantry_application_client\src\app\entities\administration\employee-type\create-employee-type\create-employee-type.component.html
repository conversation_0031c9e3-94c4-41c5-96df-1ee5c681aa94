<div class="card card-custom gutter-b" id="createEmployeeType">
  <app-card-header [cardTitle]="cardTitle" [cardSubTitle]="cardSubTitle" [buttons]="buttons"></app-card-header>
  <div class="card-body create-card" *isFetchingData="loading$">
    <form
      class="form"
      autocomplete="off"
      [formGroup]="createEmployeeTypeForm"
      autocomplete="off"
      novalidate="novalidate"
      id="create_role_form"
      (ngSubmit)="createEmployeeTypeForm.valid && onSave()"
    >
      <div id="client-detail">
        <kt-auth-notice></kt-auth-notice>
        <div class="mx-auto content-center">
          <div class="row">
            <div class="col-12 pr-md-5">
              <div class="form-group first">
                <label class="form-label">Employee Type</label>
                <input type="text" class="form-control custom" placeholder="e.g. W2" formControlName="name" />
                <app-form-error [validation]="'required'" [form]="createEmployeeTypeForm" [controlName]="'name'" [fieldLabel]="'Expense Type'"></app-form-error>
              </div>
              <div class="form-group">
                <label class="form-label">Is Employee?</label>
                <span class="status-toggle">False<mat-slide-toggle class="mx-3" color="warn" formControlName="is_employee"></mat-slide-toggle>True</span>
              </div>
              <div class="form-group">
                <label class="form-label">Include in Bench Cost?</label>
                <span class="status-toggle">False <mat-slide-toggle class="mx-3" color="warn" formControlName="is_utilized"></mat-slide-toggle>True</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>

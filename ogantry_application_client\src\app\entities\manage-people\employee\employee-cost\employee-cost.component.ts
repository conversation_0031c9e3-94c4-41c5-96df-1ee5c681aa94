import { DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { AuthNoticeService } from '@auth/auth-notice/auth-notice.service';
import { AdministrationService } from '@entities/administration/administration.service';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { ButtonParams, OGantryHttpResponse } from '@shared/models';
import { KtDialogService } from '@shared/services';
import { APP_ROUTES } from '@shared/constants';
import moment from 'moment';
import { Employee } from '@entities/administration/administration.model';

@Component({
  selector: 'app-employee-cost',
  templateUrl: './employee-cost.component.html',
  styleUrls: ['./employee-cost.component.scss'],
  providers: [DatePipe]
})
export class EmployeeCostComponent extends SflBaseComponent implements OnInit {
  cardTitle = 'Edit Employee Cost';
  cardSubTitle = null;
  buttons: ButtonParams[] = [];
  createEmployeeCostForm: FormGroup;
  employeeId: number;
  employee: Employee;
  showDialogBox = false;
  maxDate = null;
  minDate = null;
  showAccordian = false;
  constructor(
    private readonly administrationService: AdministrationService,
    private readonly authNoticeService: AuthNoticeService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly ktDialogService: KtDialogService,
    private readonly datePipe: DatePipe,
    private readonly router: Router
  ) {
    super();
  }
  ngOnInit(): void {
    this.loading$.next(false);
    this.initForm();
    this.getParams();
  }
  getParams() {
    this.activatedRoute.params.subscribe((params: Params) => {
      this.employeeId = params.employeeId;
      if (this.employeeId) {
        this.getEmployee();
        this.showAccordian = true;
      }
      this.setBtnParams();
    });
  }

  getEmployee() {
    this.loading$.next(true);
    this.subscriptionManager.add(
      this.administrationService.getEmployee(this.employeeId).subscribe(
        (res) => {
          this.employee = res.data;
          this.setEmployeeForm();
          this.loading$.next(false);
        },
        () => this.loading$.next(false)
      )
    );
  }
  async setEmployeeForm() {
    const employee = this.employee.employee;
    this.createEmployeeCostForm.controls['hourly_cost'].setValue(employee.hourly_cost);
    this.maxDate = moment(employee.end_date).toDate();
    this.minDate = moment(employee.start_date).toDate();
  }

  initForm() {
    this.createEmployeeCostForm = new FormGroup({
      hourly_cost: new FormControl('', Validators.required),
      active_date: new FormControl('', Validators.required)
    });
  }

  setBtnParams() {
    this.buttons = [
      {
        btnClass: 'btn btn-cancel',
        btnText: 'Cancel',
        redirectPath: this.appRoutes.MANAGE_EMPLOYEE
      },
      {
        btnClass: 'btn-save',
        btnText: 'Save',
        btnType: 'submit',
        action: this.showDialog.bind(this),
        loading: this.isSubmitting
      }
    ];
  }

  callupdateEmployeeCostApi() {
    this.isSubmitting = true;
    const employeeData = this.createEmployeeCostForm.value;
    employeeData.active_date = this.datePipe.transform(new Date(employeeData.active_date), 'yyyy-MM-dd');
    this.subscriptionManager.add(
      this.administrationService.updateEmployeeCost(employeeData, this.employeeId).subscribe(
        (res) => {
          this.closeModal();
          this.isSubmitting = false;
          this.router.navigateByUrl(APP_ROUTES.MANAGE_EMPLOYEE);
        },
        (err) => this.onError(err)
      )
    );
  }

  onError(err) {
    this.isSubmitting = false;
    this.closeModal();
    const error: OGantryHttpResponse<Employee> = err.error;
    this.authNoticeService.setNotice(error.errors, 'danger');
  }

  closeModal() {
    this.showDialogBox = false;
  }

  showDialog() {
    if (!this.checkFormForValidation(this.createEmployeeCostForm)) {
      this.showDialogBox = true;
    }
  }

  ngOnDestroy() {
    this.authNoticeService.setNotice(null);
    this.ktDialogService.hide();
  }

  getSkillSet(employee) {
    let a = [];
    employee?.employee?.position_types?.map((type) => {
      a.push(type.position_type.name);
    });
    return a.join(',').replace(',', ', ');
  }
}

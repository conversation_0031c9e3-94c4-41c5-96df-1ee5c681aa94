#booktimoff {
  input[type='radio'] {
    display: none;
  }

  input[type='radio']:checked + label:before {
    background: #4b3f72;
    color: #ffffff;
    content: '\2713';
    text-align: center;
  }

  input[type='radio'] + label:before {
    border: 1px solid #4b3f72;
    border-radius: 1rem;
    content: '\00a0';
    display: inline-block;
    font: 16px/1em sans-serif;
    height: 16px;
    margin: 0 0.25em 0 0;
    padding: 0;
    vertical-align: top;
    width: 16px;
  }

  .form .form-group {
    margin-bottom: 0;
    padding-top: 14px;
    padding-bottom: 14px;
  }

  ::ng-deep .dropdown .p-dropdown {
    width: 100%;
    height: 100%;
    border-radius: 9px !important;
    border: none !important;
    background-color: #f8f8ff !important;
    min-height: 60px !important;
    padding-top: 0.8rem;
  }

  ::ng-deep .dropdown .p-dropdown .p-dropdown-label {
    color: #000000;
    font-family: Poppins;
    font-size: 14px;
    letter-spacing: -0.32px;
    line-height: 25px;
    padding-top: 0.5rem;
    padding-left: 1rem;
  }

  ::ng-deep .dropdown .p-dropdown .p-dropdown-label.p-placeholder {
    color: #b5b5c3 !important;
  }

  .p-inputtext {
    padding: 0.5rem 0.6rem;
  }

  .p-calendar .p-inputtext {
    height: 60px !important;
  }

  .form-control {
    padding: 0px 0px 0px 6px;
  }

  .form-check-label {
    color: #000000;
    font-family: Poppins;
    font-size: 14px;
    letter-spacing: 0;
    line-height: 21px;
  }

  .content-center {
    width: 62%;
  }
  .range-width {
    width: 40% !important;
    color: #000000;
  }

  ::ng-deep .range-calender .p-calendar-w-btn {
    height: 100%;
    width: 100%;
  }

  ::ng-deep .range-calender .p-calendar-w-btn .p-inputtext {
    border: 0;
    background-color: #f8f8ff !important;
    color: #000000;
    font-size: 12px;
    align-self: center;
  }

  ::ng-deep .p-inputtext::-webkit-input-placeholder {
    color: #b5b5c3 !important;
    font-size: 17px !important;
  }

  ::ng-deep .p-button {
    background: #4b3f72;
    border-color: #4b3f72;
  }

  ::ng-deep .p-button:enabled:hover {
    background: #574985;
    border-color: #574985;
  }

  ::ng-deep .p-inputtext:enabled:focus {
    box-shadow: none;
  }

  ::ng-deep .p-button.p-button-icon-only {
    width: 4rem;
  }

  ::ng-deep .fa,
  .fa-caret-down {
    height: 28px;
    color: #b5b5c3 !important;
    float: right;
    line-height: 1;
    font-weight: 900;
    font-size: medium;
  }
  .p-dropdown-trigger-icon {
    font-size: medium;
    color: #b5b5c3 !important;
  }

  ::ng-deep .p-dropdown:not(.p-disabled).p-focus {
    box-shadow: none;
  }

  .dropdown-item:hover,
  .dropdown-item:focus {
    color: #101221;
    text-decoration: none;
    background-color: #f8f8ff;
  }

  .dropdown .p-multiselect .p-multiselect-label.p-placeholder {
    color: #b5b5c3 !important;
    border: 0;
    background-color: #f8f8ff;
    height: 59px;
    width: 100%;
    text-align: left;
  }

  .btn.dropdown-btn {
    color: #b5b5c3;
    border: 0;
    background-color: #f8f8ff;
    height: 59px;
    width: 100%;
    text-align: left;
  }

  .client-dropdown .dropdown-height {
    min-height: 60px !important;
  }
  .client-dropdown .dropdown-menu.show {
    height: 140px;
    overflow-y: scroll;
  }

  .dropdown-text {
    color: #000000 !important;
    font-size: 14px;
  }

  .dropdown-placeholder {
    font-size: 14px;
  }

  i {
    height: 18px !important;
    color: #6c757d !important;
    float: right;
    line-height: 2.8 !important;
  }

  .btn i {
    font-size: 1rem;
  }

  .btn.btn-outline-primary:hover,
  .btn.btn-outline-primary.dropdown-toggle {
    border: 0;
    background-color: #f8f8ff !important;
    // color: #b5b5c3 !important;
  }

  .show,
  .btn.btn-outline-primary:hover {
    color: #b5b5c3 !important;
  }

  .dropdown,
  .dropdown-menu {
    width: 100%;
  }

  .space {
    padding-left: 1rem;
    color: #000000;
  }

  ::ng-deep button,
  svg {
    min-height: 60px !important;
    max-height: auto !important;
  }
}
@media (max-width: 1199.98px) {
  .content-center {
    width: 100% !important;
  }
}
.btn-min-h {
  min-height: 60px !important;
}
::ng-deep .p-calendar .p-datepicker {
  z-index: 2103 !important;
}

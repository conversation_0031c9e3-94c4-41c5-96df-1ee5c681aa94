// add custom css here
@import "/src/assets/sass/components/variables.bootstrap";
// update snackbar css
.mat-snack-bar-container.custom-update-prompt {
  background-color: #e46868 !important;
  color: #140808 !important;
}

.custom-update-prompt .mat-simple-snackbar {
  font-weight: bold;
}

.custom-update-prompt .mat-simple-snackbar-action button {
  border: 1px solid #34405c;
  color: #fff;
  background: #1f3649;
}

// update snackbar css end
.detail-sidebar-content {
  height: calc(100vh - 50px);
  overflow-y: hidden !important;
}

mat-sidenav.detail-sidebar {
  width: 50%;
  .mat-drawer-inner-container {
    overflow-x: hidden !important;
  }
  .card-body {
    box-shadow: 0px 0px 6px 1px #b1b1b1;
    margin: 15px;
  }
  .card-header {
    padding: 2rem 1.25rem;
  }
}

@media (max-width: 1400px) {
  mat-sidenav.detail-sidebar {
    width: 80%;
  }
}

mat-sidenav-container {
  height: 100% !important;
  // height: calc(100vh - 50px) !important;
  background: transparent !important;
}

.detail-sidebar-content::-webkit-scrollbar {
  background-color: #fff !important;
  width: 8px;
}

.detail-sidebar-content::-webkit-scrollbar-track {
  background-color: #fff !important;
}
.detail-sidebar-content::-webkit-scrollbar-track:hover {
  background-color: #f4f4f4 !important;
}

.detail-sidebar-content::-webkit-scrollbar-thumb {
  background-color: #babac0 !important;
  border-radius: 10px !important;
}
.detail-sidebar-content::-webkit-scrollbar-thumb:hover {
  background-color: #a0a0a5 !important;
}

.detail-sidebar-content::-webkit-scrollbar-button {
  display: none !important;
}

.mat-drawer-backdrop.mat-drawer-shown {
  opacity: 0.28 !important;
}

.color-link {
  color: $link-color;
}

.color-disabled {
  color: $disable-color;
}

.form-label {
  color: #2a2a2a !important;
  font-weight: 600 !important;
  font-size: 15px !important;
}

.form-control.custom {
  border-radius: 9px !important;
  border: none !important;
  background-color: #f8f8ff !important;
  min-height: 60px !important;
}

.custom {
  &.btn {
    height: 60px !important;
    font-size: 18px;
  }
}

.user-profile-dropdown-img {
  background-color: $primary;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 30px #f8f8ff inset !important;
  box-shadow: 0 0 0 30px #f8f8ff inset !important;
}

.create-card {
  padding: 1.5rem 7rem !important;
  input,
  input::-webkit-input-placeholder,
  textarea,
  textarea::-webkit-input-placeholder {
    font-size: 16px;
  }
}

@media (max-width: 500px) {
  .create-card {
    padding: 1.5rem !important;
  }
}

@media (min-width: 768px) {
  .pr-md-5 {
    padding-right: 2rem !important;
  }
  .pl-md-5 {
    padding-left: 2rem !important;
  }
}

.btn-close-icon,
.btn-close-icon .fa-icon {
  font-size: 18px;
  color: #4a4a4a;
  border: none;
  background: none;
}

.btn-filter-icon,
.btn-filter-icon .fa-icon {
  font-size: 24px;
  color: #4b3f72;
  border: 1px solid #4B3F72;
  border-radius: 6px;
  background: none;
}

input[type="checkbox"] {
  visibility: hidden;
}
input[type="checkbox"]:checked + label:before {
  background: $primary;
  color: $white;
  content: "\2713";
  text-align: center;
}

input[type="checkbox"]:focus + label::before {
  outline: $primary auto 5px;
}
input[type="checkbox"] + label:before {
  border: 1px solid $primary;
  content: "\00a0";
  display: inline-block;
  font: 16px/1em sans-serif;
  height: 16px;
  margin: 0.15em 0.25em 0 0;
  padding: 0;
  vertical-align: top;
  width: 16px;
}

body::-webkit-scrollbar {
  width: 0.5em !important;
}

body::-webkit-scrollbar-track {
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3) !important;
}

body::-webkit-scrollbar-thumb {
  background-color: darkgray !important;
  outline: 1px solid slategray !important;
}

.p-checkbox .p-checkbox-box.p-highlight{
 border-color: #827da0 !important;
 background: #827da0 !important;
}

::ng-deep .p-checkbox:not(.p-checkbox-disabled) .p-checkbox-box:hover {
  border-color: #827da0;
 }

 ::ng-deep .p-checkbox:not(.p-checkbox-disabled) .p-checkbox-box .p-highlight:hover {
  border-color: #827da0 !important;
  background: #827da0 !important;
 }

 ::ng-deep .p-multiselect-panel .p-multiselect-items .p-multiselect-item.p-highlight {
  border-color:#dcdcdd;
  background: #dcdcdd;
}

::ng-deep .p-multiselect-panel .p-multiselect-items .p-multiselect-item:focus{
  box-shadow:none;
}

.p-multiselect.p-multiselect-chip .p-multiselect-token{
  background: #eeebf4 !important;
}

.p-treeselect.p-treeselect-chip .p-treeselect-token{
  background-color: #eeebf4 !important;
}

.sticky-row-1 > th {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 99;
}
.sticky-row-2 > th {
  position: -webkit-sticky;
  position: sticky;
  top: 2rem;
  z-index: 99;
}
.p-datatable-responsive-scroll>.p-datatable-wrapper {
  max-height: 84vh !important;
  overflow-y: auto !important;
}

::ng-deep .p-datatable-responsive-scroll>.p-datatable-wrapper {
  min-height: 55vh !important;
}

.pointer{
  cursor: pointer;
}

button ,svg{
  min-height: 40px !important;
  max-height: 40px !important;
}




.p-splitbutton .p-splitbutton-defaultbutton, .p-splitbutton.p-button-rounded>.p-splitbutton-defaultbutton.p-button, .p-splitbutton.p-button-outlined>.p-splitbutton-defaultbutton.p-button{
  border-radius: 6px 0px 0px 6px !important;
  max-width: 35px !important;
  min-width: 35px !important;
}


.p-splitbutton-menubutton, .p-splitbutton.p-button-rounded>.p-splitbutton-menubutton.p-button, .p-splitbutton.p-button-outlined>.p-splitbutton-menubutton.p-button{
  border-radius: 0px 6px 6px 0px !important;
  max-width: 35px !important;
  min-width: 35px !important;
}

.btn-switcher{
  max-width: 35px !important;
  min-width: 35px !important;
  padding: 2px 0px 0px 0px;
}
.svg-inline--fa{
  max-height: 15px !important;
  min-height: 15px !important;
}

$smoke-color: #F6F6F6;
$smoke-border-color: #E1E1EF;

.tag-selector{
 background-color: $smoke-color;
 border: 2px solid $smoke-border-color;
}


p-paginator{
  button{
   width: 40px !important;
  }
}

.red-text{
  color: red;
}

p-calendar{
  button{
    max-height: max-content !important;
  }
}


.column-selection-icon{
  border-radius: 6px;
  text-align: center;
  width: 44px;
  height: 40px;
  display: block;
  background: #4b3f72;
}

.p-dialog-header-close{
  height: 40px !important;
  width: 40px !important;
}

.f-13{
  font-size: 13px;
}

::ng-deep  .plus-icon{
  svg{
  fill:$primary;
}
}

.informative-Icon{
   color: #b5b5c3;
  cursor: pointer;
}

.export-dialog .p-dialog{
  min-height: fit-content;
  top: 55px !important;
  right: 103px !important;
}

.purple-color{
 color: $purple-Color;
}


.p-multiselect-panel .month-option-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.p-multiselect-panel .month-option-amount {
  margin-left: 20px;
  font-weight: 500;
}

/**
 * @sunflowerlab
 * <AUTHOR>
 */
import { Injectable } from '@angular/core';

/**
 * This service checks for new updates in the application.
 */
@Injectable({ providedIn: 'root' })
export class GetSetCacheFiltersService {
  constructor() {}

  public setCacheFilters(filters, page): void {
    let pageFilter = {};
    pageFilter[page] = filters;
    if (localStorage.getItem('cacheFilters')) {
      let cacheFilters = JSON.parse(localStorage.getItem('cacheFilters'));
      cacheFilters[page] = filters;
      localStorage.setItem('cacheFilters', JSON.stringify(cacheFilters));
    } else {
      localStorage.setItem('cacheFilters', JSON.stringify(pageFilter));
    }
  }

  public getCacheFilters(page) {
    if (localStorage.getItem('cacheFilters')) {
      let cacheFilters = JSON.parse(localStorage.getItem('cacheFilters'));
      if (cacheFilters.hasOwnProperty(page)) {
        return cacheFilters[page];
      } else {
        return null;
      }
    } else {
      return null;
    }
  }
  public resetCacheFilters(page) {
    if (localStorage.getItem('cacheFilters')) {
      let cacheFilters = JSON.parse(localStorage.getItem('cacheFilters'));
      if (cacheFilters.hasOwnProperty(page)) {
        delete cacheFilters[page];
        localStorage.setItem('cacheFilters', JSON.stringify(cacheFilters));
      }
    }
  }
}

import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Params } from '@angular/router';
import { AuthNoticeService } from '@auth/auth-notice/auth-notice.service';
import { PositionType } from '@entities/administration/administration.model';
import { AdministrationService } from '@entities/administration/administration.service';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { ButtonParams, OGantryHttpResponse } from '@shared/models';
import { KtDialogService } from '@shared/services';

@Component({
  selector: 'app-create-position-type',
  templateUrl: './create-position-type.component.html',
  styleUrls: ['./create-position-type.component.scss']
})
export class CreatePositionTypeComponent extends SflBaseComponent implements OnInit {
  cardTitle = 'Add Skill Set';
  cardSubTitle = null;
  buttons: ButtonParams[] = [];
  createPositionTypeForm: FormGroup;
  typeId: number;
  positionType: PositionType;
  constructor(
    private readonly administrationService: AdministrationService,
    private readonly authNoticeService: AuthNoticeService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly ktDialogService: KtDialogService
  ) {
    super();
  }
  ngOnInit(): void {
    this.loading$.next(false);
    this.initForm();
    this.getParams();
  }

  getParams() {
    this.activatedRoute.params.subscribe((params: Params) => {
      this.typeId = params.typeId;
      if (this.typeId) {
        this.cardTitle = 'Edit Skill Set';
        this.getPositionType();
      }
      this.setBtnParams();
    });
  }

  getPositionType() {
    this.loading$.next(true);
    this.subscriptionManager.add(
      this.administrationService.getPositionType(this.typeId).subscribe(
        (res) => {
          this.positionType = res.data;
          this.setPositionTypeForm();
          this.loading$.next(false);
        },
        () => this.loading$.next(false)
      )
    );
  }

  initForm() {
    this.createPositionTypeForm = new FormGroup({
      name: new FormControl('', Validators.required)
    });
  }

  setBtnParams() {
    this.buttons = [
      {
        btnClass: 'btn btn-cancel',
        btnText: 'Cancel',
        redirectPath: this.appRoutes.MANAGE_POSITION_TYP
      },
      {
        btnClass: 'btn-save',
        btnText: 'Save',
        btnType: 'submit',
        action: this.typeId ? this.onEdit.bind(this) : this.onSave.bind(this),
        loading: this.isSubmitting
      }
    ];
  }

  onEdit() {
    if (!this.checkFormForValidation(this.createPositionTypeForm)) {
      this.isSubmitting = true;
      this.setBtnParams();
      this.subscriptionManager.add(
        this.administrationService.updatePositionType(this.createPositionTypeForm.value, this.typeId).subscribe(
          (res) => {
            this.onSuccess(res.data, 'Skill Set updated successfully');
          },
          (err) => this.onError(err)
        )
      );
    }
  }

  onSave() {
    if (!this.checkFormForValidation(this.createPositionTypeForm)) {
      this.isSubmitting = true;
      this.setBtnParams();
      this.subscriptionManager.add(
        this.administrationService.createPositionType(this.createPositionTypeForm.value).subscribe(
          (res) => {
            this.onSuccess(res.data, 'Skill Set created successfully');
          },
          (err) => this.onError(err)
        )
      );
    }
  }

  ngOnDestroy() {
    this.authNoticeService.setNotice(null);
    this.ktDialogService.hide();
  }

  onSuccess(positionType: PositionType, successMsg: string) {
    this.isSubmitting = false;
    this.positionType = positionType;
    this.typeId = this.positionType.position_type.id;
    this.setBtnParams();
    this.authNoticeService.setNotice(successMsg, 'success');
  }

  onError(err) {
    this.isSubmitting = false;
    this.setBtnParams();
    const error: OGantryHttpResponse<PositionType> = err.error;
    this.authNoticeService.setNotice(error.errors, 'danger');
  }

  setPositionTypeForm() {
    const positionType = this.positionType.position_type;
    this.createPositionTypeForm.controls['name'].setValue(positionType.name);
  }
}

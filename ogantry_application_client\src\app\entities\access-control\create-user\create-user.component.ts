import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Params } from '@angular/router';
import { AuthService } from '@auth/auth.service';
import { AuthNoticeService } from '@auth/auth-notice/auth-notice.service';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { ButtonParams, OGantryHttpResponse } from '@shared/models';
import { User, Users } from '../manage-user/manage-user.component';
import { AccessControlService } from '../access-control.service';

@Component({
  selector: 'app-create-user',
  templateUrl: './create-user.component.html',
  styleUrls: ['./create-user.component.scss']
})
export class CreateUserComponent extends SflBaseComponent implements OnInit {
  cardTitle = 'Create User';
  cardSubTitle = null;
  buttons: ButtonParams[] = [];
  createUserForm: FormGroup;
  user: User;
  userRoles = [];
  showInActiveUserPrompt = false;
  saveBtnText = 'Save';
  userId: number;

  constructor(
    private readonly authNoticeService: AuthNoticeService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly authSerivce: AuthService,
    private readonly accessControlService: AccessControlService
  ) {
    super();
  }

  ngOnInit(): void {
    this.loading$.next(false);
    this.initializeUserForm();
    this.routerListener();
    this.setBtnParams();
    this.getTheAvailableRoles();
  }

  async getTheAvailableRoles() {
    // if globalDetailsList$ subject has value use that value else call the api
    if (this.authSerivce.globalDetailsList$.value) {
      this.userRoles = this.authSerivce.globalDetailsList$.value?.global_details[0]?.global_detail?.extended_fields?.roles?.map(({ name }) => ({ name }));
    } else {
      const globalDetails = await this.authSerivce.getPermissionsForRoles().toPromise();
      this.userRoles = globalDetails?.data?.global_details[0]?.global_detail?.extended_fields?.roles?.map(({ name }) => ({ name }));
    }
  }

  routerListener() {
    this.activatedRoute.params.subscribe((params: Params) => {
      this.userId = params.userId;
      if (this.userId) {
        this.cardTitle = 'Update User';
        this.getUser();
      }
      this.setBtnParams();
    });
  }

  getUser() {
    this.subscriptionManager.add(
      this.accessControlService.getUser(this.userId).subscribe((user) => {
        this.user = user?.data;
        this.initUserFormWithUserData();
      })
    );
  }

  setBtnParams() {
    this.buttons = [
      {
        btnClass: 'btn btn-cancel',
        btnText: 'Cancel',
        redirectPath: this.appRoutes.MANAGE_USER
      },
      {
        btnClass: 'btn-save',
        btnText: 'Save',
        btnType: 'submit',
        action: this.userId ? this.updateUser.bind(this) : this.onSave.bind(this),
        loading: this.isSubmitting
      }
    ];
  }

  initializeUserForm() {
    this.createUserForm = new FormGroup({
      firstName: new FormControl(null, [Validators.required, Validators.minLength(2)]),
      lastName: new FormControl(null, [Validators.required, Validators.minLength(2)]),
      email: new FormControl(null, [Validators.required, Validators.email]),
      role: new FormControl(null, Validators.required),
      active: new FormControl(true, Validators.required)
    });
  }

  initUserFormWithUserData() {
    const roleName = JSON.parse((this.user?.user_authorization?.self_service_storage).replace(/'/g, '"'));
    this.createUserForm.patchValue({
      firstName: this.user?.user_authorization?.first_name,
      lastName: this.user?.user_authorization?.last_name,
      email: this.user?.user_authorization?.email,
      id: this.user?.user_authorization?.id,
      active: this.user?.user_authorization?.active,
      role: roleName?.role
    });
  }

  onSave() {
    if (this.createUserForm?.invalid) {
      this.createUserForm?.markAllAsTouched();
      return;
    }
    this.subscriptionManager.add(
      this.accessControlService.addOrUpdateUser(this.userDTO).subscribe((res) => {
        this.onSuccess(res.data, 'User created successfully.');
      })
    );
  }

  updateUser() {
    if (this.createUserForm?.invalid) {
      return;
    }
    this.subscriptionManager.add(
      this.accessControlService.addOrUpdateUser(this.userDTO, this.userId).subscribe((res) => {
        this.onSuccess(res.data, 'User authorization updated successfully..');
      })
    );
  }

  get userDTO(): User {
    const roleDetail = "{'role': '" + this.createUserForm?.get('role')?.value + "'}";
    return {
      user_authorization: {
        email: this.createUserForm?.get('email')?.value,
        first_name: this.createUserForm?.get('firstName')?.value,
        last_name: this.createUserForm?.get('lastName')?.value,
        active: this.createUserForm?.get('active')?.value,
        id: this.createUserForm?.get('id')?.value,
        self_service_storage: roleDetail
      }
    };
  }

  onSuccess(user: User, successMsg: string) {
    this.isSubmitting = false;
    this.user = user;
    this.userId = this.user.user_authorization?.id;
    this.setBtnParams();
    this.authNoticeService.setNotice(successMsg, 'success');
  }

  onError(err) {
    this.isSubmitting = false;
    const error: OGantryHttpResponse<User> = err.error;
    this.authNoticeService.setNotice(error.errors, 'danger');
  }

  ngOnDestroy() {
    this.authNoticeService.setNotice(null);
  }
}

import { DatePipe } from '@angular/common';
import { Component, OnInit, ViewChild } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Params } from '@angular/router';
import { AuthNoticeService } from '@auth/auth-notice/auth-notice.service';
import { Employee, Job, WorkException } from '@entities/administration/administration.model';
import { AdministrationService } from '@entities/administration/administration.service';
import { EmployeeType, Region } from '@entities/utilization-management/utilization.model';
import { UtilizationService } from '@entities/utilization-management/utilization.service';

import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { ButtonParams, OGantryHttpResponse } from '@shared/models';
import { KtDialogService } from '@shared/services';
import moment from 'moment';
import { NgbAccordion } from '@ng-bootstrap/ng-bootstrap';
import { ComponentsType } from '@shared/models/component-type-enum';
import { ExtendedFormComponent } from '@shared/components/extended-form/extended-form.component';
@Component({
  selector: 'app-create-employee',
  templateUrl: './create-employee.component.html',
  styleUrls: ['./create-employee.component.scss'],
  providers: [DatePipe]
})
export class CreateEmployeeComponent extends SflBaseComponent implements OnInit {
  hideCost = true;
  cardTitle = 'Create Employee';
  cardSubTitle = null;
  buttons: ButtonParams[] = [];
  employeeRoles = [];
  regions = [];
  employeeTypes = [];
  employeeId: number;
  createEmployeeForm: FormGroup;
  createAccrualForm: FormGroup;
  employee: Employee;
  date: Date;
  regionFlag = false;
  employeeTypeFlag = false;
  addNewFlag = false;
  showEndDate = false;
  showDialog = false;
  isEdit = false;
  inactive = false;
  showAccordian = false;
  auditHistory: Employee[] = [];
  auditLoader = false;
  pendingJobs: Job[] = [];
  pendingJobLoader = false;
  @ViewChild('acc') el: NgbAccordion;
  @ViewChild('regionsDropDown') regionsDropDown: HTMLElement;
  exceptionTypes = [];
  isAddNewAccruals = false;

  extendFieldsObj: any = {};
  componentType = ComponentsType.Employee;
  @ViewChild('extendFrom') extendFiledComponent: ExtendedFormComponent;

  tags: string[];
  constructor(
    private readonly ktDialogService: KtDialogService,
    private readonly datePipe: DatePipe,
    private readonly activatedRoute: ActivatedRoute,
    private readonly authNoticeService: AuthNoticeService,
    private readonly administrationService: AdministrationService,
    private readonly utilizationService: UtilizationService
  ) {
    super();
  }

  ngOnInit(): void {
    this.loading$.next(false);
    this.getParams();
    this.getRegions();
    this.getEmployeeRoles();
    this.getEmployeeTypes();
    this.initForm();
  }

  getParams() {
    this.activatedRoute.params.subscribe((params: Params) => {
      this.employeeId = params.employeeId;
      if (this.employeeId) {
        this.isEdit = true;
        this.showAccordian = true;
        this.cardTitle = 'Edit Employee';
        this.getEmployee();
      } else {
        this.setBtnParams();
      }
    });
  }

  getEmployee() {
    this.loading$.next(true);
    this.subscriptionManager.add(
      this.administrationService.getEmployee(this.employeeId).subscribe(
        (res) => {
          this.employee = res.data;
          this.extendFieldsObj = this.employee?.employee?.extended_fields;
          if (this.employee.employee.extended_fields) {
            const extendedFields = this.employee.employee.extended_fields;
            if (extendedFields.ext_role_position) {
              this.employee.employee.ext_role_position = extendedFields.ext_role_position;
            }
            if (extendedFields.ext_travel) {
              this.employee.employee.ext_travel = extendedFields.ext_travel;
            }
          }
          this.tags = this.employee.employee.tags;
          this.setEmployeeForm();
          this.loading$.next(false);
        },
        () => this.loading$.next(false)
      )
    );
  }

  getRegions() {
    this.loading$.next(true);
    this.subscriptionManager.add(
      this.utilizationService.getRegions().subscribe(
        (res) => {
          if (res?.data?.regions) {
            const regions = res?.data?.regions;
            regions.forEach((region: Region) => {
              this.regions.push({
                label: region?.region?.name,
                value: region?.region?.id
              });
            });
          }
          this.loading$.next(false);
        },
        () => this.loading$.next(false)
      )
    );
  }

  getEmployeeRoles() {
    this.loading$.next(true);
    this.subscriptionManager.add(
      this.utilizationService.getPositionTypes().subscribe(
        (res) => {
          if (res?.data?.position_types) {
            res.data.position_types.forEach((type) => {
              this.employeeRoles.push({
                label: type.position_type.name,
                value: type.position_type.id
              });
            });
          }
          this.loading$.next(false);
        },
        () => this.loading$.next(false)
      )
    );
  }

  getEmployeeTypes() {
    this.loading$.next(true);
    this.subscriptionManager.add(
      this.utilizationService.getEmployeeTypes().subscribe((res) => {
        if (res?.data?.employee_types) {
          const regions = res?.data?.employee_types;
          regions.forEach((employeeType: EmployeeType) => {
            this.employeeTypes.push({
              label: employeeType?.employee_type?.name,
              value: employeeType?.employee_type?.id
            });
          });
        }
      })
    );
  }

  initForm() {
    this.createEmployeeForm = new FormGroup({
      first_name: new FormControl('', [Validators.required, Validators.minLength(2)]),
      last_name: new FormControl('', [Validators.required, Validators.minLength(2)]),
      email: new FormControl('', Validators.required),
      hourly_cost: new FormControl('', Validators.required),
      employee_type_id: new FormControl(null),
      region_id: new FormControl(1),
      start_date: new FormControl('', Validators.required),
      ext_role_position: new FormControl(''),
      utilization_target: new FormControl(100, [Validators.required, Validators.min(0), Validators.max(100)]),
      ext_travel: new FormControl('', [Validators.min(0), Validators.max(100)]),
      position_types: new FormControl([]),
      end_date: new FormControl('')
    });
    this.createEmployeeForm.valueChanges.subscribe((res) => {
      this.authNoticeService.setNotice(null);
    });
  }

  async setEmployeeForm() {
    const employee = this.employee.employee;
    this.createEmployeeForm.controls['first_name'].setValue(employee.first_name);
    this.createEmployeeForm.controls['last_name'].setValue(employee.last_name);
    this.createEmployeeForm.controls['email'].setValue(employee.email);
    this.createEmployeeForm.controls['hourly_cost'].setValue(employee.hourly_cost);
    this.createEmployeeForm.controls['start_date'].setValue(moment.utc(new Date(employee.start_date)).format('l LT'));
    this.createEmployeeForm.controls['end_date'].setValue(moment.utc(new Date(employee.end_date)).format('l LT'));
    this.createEmployeeForm.controls['region_id'].setValue(employee.region?.id);
    this.createEmployeeForm.controls['employee_type_id'].setValue(employee.employee_type?.id);
    this.createEmployeeForm.controls['utilization_target'].setValue(employee.utilization_target);
    this.createEmployeeForm.controls['ext_role_position'].setValue(employee.ext_role_position);
    this.createEmployeeForm.controls['ext_travel'].setValue(employee.ext_travel);
    this.createEmployeeForm.controls['position_types'].patchValue(await this.getSelectedPositionTypes);
    this.createEmployeeForm.controls['utilization_target'].disable();
    this.createEmployeeForm.controls['hourly_cost'].disable();
    this.createEmployeeForm.controls['employee_type_id'].disable();
    let currentDate = new Date();
    let endDate = new Date(employee.end_date);
    if (currentDate.getTime() >= endDate.getTime()) {
      this.createEmployeeForm.disable();
      this.inactive = true;
      this.isEdit = false;
      this.authNoticeService.setNotice(
        `The employee has already been terminated effective <strong>
      ${moment.utc(new Date(employee.end_date)).format('MMM DD, YYYY')} </strong>`,
        'dark'
      );
    }
    this.setBtnParams();
  }

  get getSelectedPositionTypes(): Promise<number[]> {
    return new Promise((resolve) => {
      const positionTypeIds: number[] = [];
      for (const type of this.employee?.employee?.position_types) {
        positionTypeIds.push(type?.position_type?.id);
      }
      resolve(positionTypeIds);
    });
  }

  ngOnDestroy() {
    this.authNoticeService.setNotice(null);
    this.ktDialogService.hide();
  }

  setBtnParams() {
    this.buttons = [
      {
        btnClass: 'btn btn-cancel',
        btnText: 'Cancel',
        redirectPath: this.appRoutes.MANAGE_EMPLOYEE
      },
      {
        btnClass: this.inactive ? 'btn-hide' : 'btn-save',
        btnText: 'Save',
        btnType: 'submit',
        action: this.employeeId ? this.onEdit.bind(this) : this.onSave.bind(this),
        loading: this.isSubmitting
      },
      {
        btnClass: this.addNewFlag ? 'btn-addNew' : 'btn-hide',
        btnText: 'Add New',
        action: this.addNewEmployee.bind(this)
      }
    ];
  }

  addNewEmployee() {
    this.employee = null;
    this.employeeId = null;
    this.authNoticeService.setNotice(null);
    this.createEmployeeForm.reset();
    this.initForm();
    this.addNewFlag = false;
    this.setBtnParams();
  }

  getEmployeeWithExtendedData(employeeData) {
    const employeeDataWithExtendedData = { ...employeeData };
    employeeDataWithExtendedData.extended_fields = {};
    if (employeeDataWithExtendedData?.ext_role_position) {
      employeeDataWithExtendedData.extended_fields.ext_role_position = employeeDataWithExtendedData?.ext_role_position;
    }
    if (employeeDataWithExtendedData?.ext_travel) {
      employeeDataWithExtendedData.extended_fields.ext_travel = employeeDataWithExtendedData?.ext_travel;
    }
    employeeDataWithExtendedData.extended_fields = this.extendFiledComponent?.extendFieldsObj ?? {};

    return employeeDataWithExtendedData;
  }

  onSave() {
    const date = 'yyyy-MM-dd';
    if (!this.createEmployeeForm.value.region_id || !this.createEmployeeForm.value.employee_type_id) {
      this.checkFormForValidation(this.createEmployeeForm);
      this.regionFlag = true;
      this.employeeTypeFlag = true;
    } else {
      if (!this.checkFormForValidation(this.createEmployeeForm)) {
        this.isSubmitting = true;
        this.setBtnParams();
        const employeeData = this.getEmployeeWithExtendedData(this.createEmployeeForm.value);
        if (this.createEmployeeForm.value.end_date) {
          employeeData.end_date = this.datePipe.transform(new Date(this.createEmployeeForm.value.end_date), date);
        } else {
          delete employeeData.end_date;
        }
        employeeData.start_date = this.datePipe.transform(new Date(this.createEmployeeForm.value.start_date), date);
        delete employeeData.ext_travel;
        delete employeeData.ext_role_position;
        this.subscriptionManager.add(
          this.administrationService.createEmployee(employeeData).subscribe(
            (res) => {
              this.onSuccess(res.data, 'Employee created successfully.');
              this.showDialog = false;
              this.addNewFlag = true;
              this.isEdit = true;
              this.setBtnParams();
            },
            (err) => this.onError(err)
          )
        );
      }
    }
  }
  callSaveEmployeeApi() {
    const date = 'yyyy-MM-dd';
    this.isSubmitting = true;
    this.setBtnParams();
    const employeeData = this.getEmployeeWithExtendedData(this.createEmployeeForm.value);
    delete employeeData.end_date;
    employeeData.start_date = this.datePipe.transform(new Date(this.createEmployeeForm.value.start_date), date);
    delete employeeData.ext_travel;
    delete employeeData.ext_role_position;
    this.subscriptionManager.add(
      this.administrationService.updateEmployee(employeeData, this.employeeId).subscribe(
        (res) => {
          this.onSuccess(res.data, 'Employee updated successfully.');
          this.showDialog = false;
        },
        (err) => this.onError(err)
      )
    );
  }

  employeeTypeSelected() {
    this.employeeTypeFlag = false;
  }

  regionSelected() {
    this.regionFlag = false;
  }

  onSuccess(employee: Employee, successMsg: string) {
    this.isSubmitting = false;
    this.setBtnParams();
    this.employee = employee;
    this.employeeId = this.employee.employee.id;
    this.authNoticeService.setNotice(successMsg, 'success');
  }

  onError(err) {
    this.isSubmitting = false;
    this.setBtnParams();
    const error: OGantryHttpResponse<Employee> = err.error;
    this.authNoticeService.setNotice(error.errors, 'danger');
  }

  onEdit() {
    if (!this.checkFormForValidation(this.createEmployeeForm)) {
      this.callSaveEmployeeApi();
    }
  }
  onTerminateClick(event) {
    if (event.target.checked) {
      this.showEndDate = true;
      this.createEmployeeForm.controls['end_date'].setValidators(Validators.required);
      this.createEmployeeForm.get('end_date').updateValueAndValidity();
    } else {
      this.showEndDate = false;
      this.createEmployeeForm.controls['end_date'].setValidators([]);
      this.createEmployeeForm.get('end_date').updateValueAndValidity();
      this.createEmployeeForm.controls['end_date'].setValue(null);
    }
  }
  closeModal() {
    this.showDialog = false;
  }

  handleKeyDown(event: any, shiftKey: boolean) {
    if (event.key === 'Tab' && (shiftKey ? event.shiftKey : !event.shiftKey)) {
      const dropdown = document.querySelector('.p-multiselect-trigger') as HTMLElement;
      if (dropdown) {
        dropdown.click();
        event.preventDefault();
      }
    }
  }

  setFocus($event) {
    setTimeout(() => {
      this.regionsDropDown.focus();
    }, 100);
  }

  limitDecimalPlaces(event: any): void {
    const value = event?.target?.value;
    const regex = new RegExp(this.appConstants.regexForTwoDecimal).test(value);
    if (!regex) {
      event.target.value = value.slice(0, -1);
      this.createEmployeeForm?.controls['utilization_target']?.setValue(event?.target?.value);
    }
  }
}

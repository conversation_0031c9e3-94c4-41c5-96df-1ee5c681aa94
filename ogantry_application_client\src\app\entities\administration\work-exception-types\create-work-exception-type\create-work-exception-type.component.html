<div class="card card-custom gutter-b" id="createWorkExceptionType">
  <app-card-header [cardTitle]="cardTitle" [cardSubTitle]="cardSubTitle" [buttons]="buttons"></app-card-header>
  <div class="card-body create-card" *isFetchingData="loading$">
    <form
      class="form"
      autocomplete="off"
      [formGroup]="createWorkExceptionTypeForm"
      autocomplete="off"
      novalidate="novalidate"
      id="create_role_form"
      (ngSubmit)="createWorkExceptionTypeForm.valid && onSave()"
    >
      <div id="client-detail">
        <kt-auth-notice></kt-auth-notice>
        <div class="mx-auto content-center">
          <div class="row">
            <div class="col-12 pr-md-5">
              <div class="form-group first">
                <label class="form-label">Add Work Exception Type</label>
                <input type="text" class="form-control custom" placeholder="e.g. Bereavement" formControlName="name" />
                <app-form-error [validation]="'required'" [form]="createWorkExceptionTypeForm" [controlName]="'name'" [fieldLabel]="'Work Exception Type'"></app-form-error>
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>

.apply-filter {
  color: #4b3f72;
  font-family: <PERSON><PERSON><PERSON>;
  font-size: 14px;
  font-weight: 600;
  margin-right: 1rem;
}
.p-tag {
  color: #4b3f72;
  font-family: Poppins;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 18px;
  text-align: center;
  margin-right: 1rem;
  margin-bottom: 0.25rem;
  border-radius: 2px;
  box-sizing: border-box;
  border: 1px solid #edeff3;
  background-color: #f4f6f8;
}

::ng-deep .mat-chip-list-wrapper {
  display: inline !important;
}

::ng-deep .material-icons {
  vertical-align: top !important;
  line-height: 1.3;
}

::ng-deep .mat-chip.mat-standard-chip .mat-chip-remove {
  opacity: 1 !important;
  line-height: 0.9;
}

.chip :hover,
.chip {
  background-color: inherit !important;
}
.icon-background {
  background-color: #4b3f72 !important;
  padding: 0.2rem;
}

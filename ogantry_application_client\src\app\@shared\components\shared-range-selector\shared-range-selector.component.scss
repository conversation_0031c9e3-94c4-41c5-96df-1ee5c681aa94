.PL-border {
  box-sizing: border-box;
  border: 1px solid #eeebf4;
  border-radius: 9px;
  margin-bottom: 1rem;
}

#shared-range-selector {
  .background {
    height: 38px;
    border-radius: 9px 9px 0 0;
    background-color: #eeebf4;
    display: flex;
    align-items: center;
    padding-left: 0.5rem;
  }

  .save-filter-radio {
    display: flex;
    justify-content: flex-end;
    width: 100%;
  }

  .width-34 {
    width: 34%;
  }

  input[type='radio'] {
    display: none;
  }

  input[type='radio']:checked + label:before {
    background: #4b3f72;
    color: #ffffff;
    content: '\2713';
    text-align: center;
  }

  input[type='radio'] + label:before {
    border: 1px solid #4b3f72;
    border-radius: 1rem;
    content: '\00a0';
    display: inline-block;
    font: 16px/1em sans-serif;
    height: 16px;
    padding: 0;
    vertical-align: top;
    width: 16px;
  }

  input[type='radio']:checked:before {
    background: #4b3f72;
    color: #ffffff;
    content: '\2713';
    text-align: center;
    appearance: auto;
  }

  .form-group {
    padding-bottom: 0;

    &:first-child {
      padding-top: 0;
    }
  }

  ::ng-deep .range-calender .p-calendar-w-btn {
    width: 100%;
  }

  ::ng-deep .range-calender .p-calendar-w-btn .p-inputtext {
    border: 0;
    background-color: #f8f8ff !important;
    height: 60px;
  }

  ::ng-deep .range-calender .p-calendar-w-btn {
    width: 100%;
  }

  ::ng-deep .range-calender .p-calendar-w-btn .p-inputtext {
    border: 0;
    background-color: #f8f8ff !important;
    height: 60px;
  }

  .p-calendar .p-inputtext {
    height: 60px !important;
  }

  ::ng-deep .p-button.p-button-icon-only {
    width: 3.357rem;
  }

  ::ng-deep .p-button {
    background: #4b3f72;
    border-color: #4b3f72;
  }

  ::ng-deep .p-button:enabled:hover {
    background: #574985;
    border-color: #574985;
  }

  ::ng-deep .pi-icon .pi {
    font-size: 0.5rem;
  }
}

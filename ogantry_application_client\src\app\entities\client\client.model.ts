import { CustomerContact } from '@shared/models';

export interface Contact {
  contact?: CustomerContact;
}

interface ExtendedFields {
  [key: string]: string;
}

export interface Client {
  customer?: {
    id?: number;
    name?: string;
    is_active?: boolean;
    comment?: string;
    contacts?: Contact[];
    useNewClient?: boolean;
    ref?: string;
    tags?: string[];
    extended_fields: ExtendedFields;
  };
  isEdit?: boolean;
}

export interface ClientList {
  customers?: Client[];
}

export interface SaveFilter {
  is_shared?: boolean;
  resource?: string;
  query_string?: string;
  name?: string;
}

export interface QueryFilter {
  query_filter: {
    id: number;
    is_shared: boolean;
    query_string: string;
    ref: string;
    resource: string;
    user_id: number;
    name: string;
  };
}
export interface ISavedFilterList {
  data: {
    query_filters?: QueryFilter[];
  };
}
export interface ISavedFilter {
  data: QueryFilter;
}

export class IFilter {
  name_search?: string;
  is_active = 'true';
  limit?: number;
  offset?: number;
  order_by?: string;
  tags?: string;
}
export interface QueryFilterParams {
  limit?: number;
  offset?: number;
  order_by?: string;
  name?: string;
  is_active?: boolean;
}

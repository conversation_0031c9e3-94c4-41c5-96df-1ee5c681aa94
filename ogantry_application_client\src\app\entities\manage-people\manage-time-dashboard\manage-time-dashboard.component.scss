#manage-time-dashboard {
  background-color: white;

  .status {
    display: flex;
    align-items: center;

    .status-value {
      background-color: lightgray;
      border-radius: 20px;
      padding: 5px;
    }

    &.approved {
      .status-value {
        background-color: #00b69b;
        color: white;
      }
    }

    &.submitted {
      .status-value {
        background-color: #007bff;
        color: white;
      }
    }
  }

  .timesheet-label {
    margin-bottom: 5px;
    background-color: lightgray;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .pi-angle-right {
    font-size: 20px;
  }

  .time-off-days {
    display: grid;
  }

  .upcoming-timeoff-data {
    height: calc(100vh - 450px);
    overflow: auto;
    padding-right: 5px;
  }

  .position {
    margin-top: 20px;
  }

  .w-25 {
    width: 25% !important;
  }

  .w-35 {
    width: 35% !important;
  }

  .f-10 {
    font-size: 10px !important;
  }

  .f-12 {
    font-size: 12px !important;
  }

  .f-14 {
    font-size: 14px !important;
  }

  .d-grid {
    display: grid;
  }

  .cursor-pointer {
    cursor: pointer;
  }

  .pointer,
  .pointer:hover {
    text-decoration: underline !important;
  }

  .data-border {
    padding: 10px;
    border: 1px solid #cfcfd9;
    border-radius: 6px;
    margin-top: 10px !important;
    font-weight: 400;
    color: black;
  }
  .overflow-handel {
    overflow-y: auto;
    max-height: calc(100vh - 150px);
  }

  .disable-link {
    pointer-events: none;
    color: gray;
  }
}

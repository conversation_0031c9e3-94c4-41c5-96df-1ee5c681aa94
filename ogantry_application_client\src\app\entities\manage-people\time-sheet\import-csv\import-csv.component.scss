@import '../../../../../assets/sass/pages/wizard/wizard4.scss';

.wizard.wizard-4 {
  .wizard-nav {
    border-bottom: none !important;
  }
  .wizard-buttons {
    width: 170px;
  }
  .wizard-line-wrapper {
    width: 130px;
    background: #000;
    height: 1px;
  }

  .wizard-nav .wizard-steps .wizard-step {
    flex: 0 0 calc(21% - 0.25rem);
    .wizard-desc {
      font-size: 11px;
    }
  }
  ::ng-deep .p-datatable-scrollable-both .p-datatable-thead > tr > th,
  .p-datatable-scrollable-both .p-datatable-tbody > tr > td,
  .p-datatable-scrollable-both .p-datatable-tfoot > tr > td,
  .p-datatable-scrollable-horizontal .p-datatable-thead > tr > th .p-datatable-scrollable-horizontal .p-datatable-tbody > tr > td,
  .p-datatable-scrollable-horizontal .p-datatable-tfoot > tr > td,
  .p-datatable-scrollable-both .p-datatable-thead > tr > th {
    flex: 1 1 0 !important;
  }

  ::ng-deep .p-datatable-scrollable-both .p-datatable-tbody > tr > td,
  .p-datatable-scrollable-both .p-datatable-tfoot > tr > td,
  .p-datatable-scrollable-horizontal .p-datatable-thead > tr > th .p-datatable-scrollable-horizontal .p-datatable-tbody > tr > td,
  .p-datatable-scrollable-horizontal .p-datatable-tfoot > tr > td {
    flex: 1 1 0 !important;
  }
}

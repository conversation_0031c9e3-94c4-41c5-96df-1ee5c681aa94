<div class="card card-custom gutter-b" id="createExpenseType">
  <app-card-header [cardTitle]="cardTitle" [cardSubTitle]="cardSubTitle" [buttons]="buttons"></app-card-header>
  <div class="card-body create-card" *isFetchingData="loading$">
    <form
      class="form"
      autocomplete="off"
      [formGroup]="createExpenseTypeForm"
      autocomplete="off"
      novalidate="novalidate"
      id="create_role_form"
      (ngSubmit)="createExpenseTypeForm.valid && onSave()"
    >
      <div id="client-detail">
        <kt-auth-notice></kt-auth-notice>
        <div class="mx-auto content-center">
          <div class="row">
            <div class="col-12 pr-md-5">
              <div class="form-group first">
                <label class="form-label">Add Expense Type</label>
                <input type="text" class="form-control custom" placeholder="e.g. Equipment" formControlName="name" />
                <app-form-error [validation]="'required'" [form]="createExpenseTypeForm" [controlName]="'name'" [fieldLabel]="'Expense Type'"></app-form-error>
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>

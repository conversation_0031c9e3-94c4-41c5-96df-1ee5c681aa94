import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { ManagePLComponent } from './manage-pl/manage-pl.component';
import { PermissionAuthGuard } from '@shared/services/permission-auth-guard.service';
import { PermissionModules } from '@shared/models/permission.enum';
import { CheckAuthResolver } from '@shared/services';
import { PlComparisonComponent } from './pl-comparison/pl-comparison.component';

const routes: Routes = [
  {
    path: 'manage-P&L',
    component: ManagePLComponent,
    canActivate: [PermissionAuthGuard],
    data: {
      permissionModules: [PermissionModules.VIEW_PL]
    },
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'compare-P&L',
    component: PlComparisonComponent,
    canActivate: [PermissionAuthGuard],
    data: {
      permissionModules: [PermissionModules.VIEW_COMPARE_PL]
    },
    resolve: { authState: CheckAuthResolver }
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class FinancialForecastingRoutingModule {}

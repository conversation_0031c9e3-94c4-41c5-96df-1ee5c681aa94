<div class="d-flex flex-column flex-root" id="kt_login_wrapper">
  <div class="login login-3 login-signin-on d-flex flex-column flex-lg-row flex-row-fluid bg-white" id="kt_login">
    <!--begin::Aside-->
    <div class="login-aside d-flex flex-row-auto bgi-size-cover bgi-no-repeat p-10 p-lg-10">
      <div class="d-flex flex-row-fluid flex-column tagline-wrapper desktop-layout">
        <a class="flex-column-auto mt-5 text-center">
          <img src="assets/media/svg/landing/landing-page-logo.svg" class="max-h-100px" alt="logo" />
        </a>

        <div class="flex-column-fluid d-flex flex-column tagline">
          <span>Manage the future </span>
          <span> Don't just react to the past </span>
        </div>

        <div class="landing-aside-bg">
          <img src="assets/media/svg/landing/bg-image-landing-page.svg" alt="logo" />
        </div>
      </div>
      <!-- Mobile layout -->
      <div class="mobile-layout">
        <div class="row align-items-center">
          <div class="col-md-6 col-12">
            <div class="row">
              <div class="col-12 col-6">
                <img src="assets/media/svg/landing/landing-page-logo.svg" class="max-h-100px" alt="logo" />
              </div>
              <div class="col-12 tagline">
                <span>Manage the Future Don't be</span> <br />
                <span> Reactive to the Past </span>
              </div>
            </div>
          </div>
          <div class="bg-image col-md-6 col-12">
            <img src="assets/media/svg/landing/bg-image-landing-page.svg" alt="logo" class="img-fluid" />
          </div>
        </div>
      </div>
      <!-- Mobile layout ends -->
    </div>
    <!--begin::Aside-->

    <!--begin::Content-->
    <div class="flex-row-fluid d-flex flex-column position-relative p-7 overflow-hidden">
      <router-outlet></router-outlet>
      <div class="btn-light-primary font-weight-bold px-8 m-0 flex-grow-1 mr-3 font-size-h6" style="color: #4b3f72">
        Version:- {{ currentApplicationVersion }} : FE &nbsp; {{ backendVersion }} : BE
      </div>
    </div>
    <!--end::Content-->
  </div>
</div>

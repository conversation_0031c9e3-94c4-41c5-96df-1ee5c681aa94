<!-- <div class="m-2 d-flex justify-content-end filter-options-wrapper">
    <div class="mr-1 filter-option">
        <p-dropdown optionLabel="option" placeholder="Select a Option" [options]="filterOptions"></p-dropdown>
    </div>
    <div class="ml-2 filter-option">
        <button class="btn-filter-icon">
            <ng-container>
                <a title="Filter" class="btn btn-icon btn-sm icon-background">
                    <span [inlineSVG]="'assets/media/svg/icons/filter.svg'" cacheSVG="true"
                        class="svg-icon svg-icon-md">
                    </span>
                </a>
            </ng-container>
        </button>
    </div>
</div> -->
<app-time-sheet-table [tableHeader]="tableHeader" [tableData]="updatedCSVData"></app-time-sheet-table>

<p-confirmDialog [visible]="showConformationDialog"></p-confirmDialog>

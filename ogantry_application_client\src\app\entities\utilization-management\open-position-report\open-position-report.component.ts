import { FormControl } from '@angular/forms';
import { DatePipe } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, HostListener, OnInit, ViewChild, Input } from '@angular/core';
import { MatSidenav } from '@angular/material/sidenav';
import { ProjectService } from '@entities/project/project.service';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { ButtonParams } from '@shared/models';
import { SidebarParams } from '@shared/models/sidebar-params.model';
import { LayoutConfigService } from '@shared/services/layout-config.service';
import { LazyLoadEvent } from 'primeng/api';
import { MultiSelect } from 'primeng/multiselect';
import { FilterReport, IFilter, MONTH_NAMES, OpenPositionReport, QueryFilterParams, TableHeader, Group } from '../utilization.model';
import { UtilizationService } from '../utilization.service';
import { AppConstants } from '@shared/constants';
const height = 'calc((var(--fixed-content-height, 1vh) * 100) - 165px)';
import { ISavedFilterList, QueryFilter, SaveFilter } from '@entities/administration/administration.model';
import { AlertType } from '@shared/models/alert-type.enum';
import { LayoutUtilsService } from '@shared/services/layout-utils.service';
import { GetSetCacheFiltersService } from '@shared/services/get-set-cache-filters.service';
import moment from 'moment';
import * as _ from 'lodash';
import { AuthService } from '@auth/auth.service';
import { PermissionModules } from '@shared/models/permission.enum';
import { isArray, isString } from 'lodash';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { ColumnToggleService } from '@shared/services/column-toggle.service';

@Component({
  selector: 'app-open-position-report',
  templateUrl: './open-position-report.component.html',
  styleUrls: ['./open-position-report.component.scss'],
  providers: [DatePipe],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class OpenPositionReportComponent extends SflBaseComponent implements OnInit, AfterViewInit {
  cardTitle = 'Lost Revenue Report';
  cardSubTitle = null;
  splitButtonDropDownOption = {
    action: this.openSideBar.bind(this),
    options: [
      {
        label: 'Get Stored Filters',
        icon: 'get-stored-filter-split-button-icon',
        command: () => {
          this.openSaveFilterList();
        }
      },
      {
        label: 'Save Filter',
        icon: 'save-filter-split-button-icon',
        command: () => {
          this.onSaveFilter();
        }
      },
      {
        label: 'Reset Filter',
        icon: 'reset-filter-split-button-icon',
        command: () => {
          this.resetFilter();
        }
      }
    ]
  };
  exportButtons: ButtonParams[] = [
    {
      action: this.exportReport.bind(this)
    }
  ];
  filterCardTitle = 'Apply Filter';
  filterButtons: ButtonParams[] = [
    {
      btnSvg: 'filter-list',
      btnClass: 'btn-filter-icon',
      action: this.openSaveFilterList.bind(this)
    },
    {
      btnSvg: 'save',
      btnClass: 'btn btn-sm btn-icon btn-icon-light svg-icon svg-icon-md icon-background mr-2 filter-btn-wrapper',
      action: this.onSaveFilter.bind(this)
    },
    {
      btnClass: 'btn-close-icon',
      btnIcon: 'times',
      action: this.onClose.bind(this)
    }
  ];
  styleObj = {
    heading: {
      color: 'rgb(109 108 108)',
      fontFamily: 'Poppins',
      fontSize: '10px',
      fontWeight: '500',
      letterSpacing: '0',
      lineHeight: '16px'
    },
    subHeading: {
      color: '#242424',
      fontFamily: 'Poppins',
      fontSize: '12px',
      letterSpacing: '0',
      lineHeight: '18px'
    }
  };
  height = height;
  sidebarParams: SidebarParams<FilterReport>;
  showFilterListDialog = false;
  availableFilters = null;
  selectedFilter = null;
  sharedFilters: QueryFilter[] = [];
  myFilters: QueryFilter[] = [];
  showSavedFilter = false;
  selectedFilterFormControl = new FormControl('');
  editFilterObj: QueryFilter;
  showNameError = false;
  showDeleteDialog = false;
  deleteFilterObj: QueryFilter;
  showShareDialog = false;
  shareFilterObj = null;
  showEditDialog = false;
  filteredFilters: ISavedFilterList;
  @ViewChild('sidebarFilter', { static: true }) el: MatSidenav;
  csvCols = [];
  exportPdfColumns = [];
  exportReportData = [];
  excelExportReportData = [];
  excelHeaders = [];
  tableHeaders: TableHeader[] = [];
  loading = false;
  positionReportData: OpenPositionReport[] = [];
  dataFilter: IFilter = new IFilter();
  positionList = [];
  projectList = [];
  clientGroup: Group[];
  projectGroup: Group[];
  tags = [];
  frozenCols = [];
  statuses = [];
  defaultStatuses: string[];
  dateError = false;
  dateRequired = false;
  projectStatus: any;
  resizeFlag = false;
  sortColumnFlag = false;
  sortFieldName: string;
  sortOrderNumber: number;
  client = [];
  showClientFilter = false;
  clientName = [];
  selectedProjectName = [];
  showProjectFilter = false;
  projectName = [];
  @ViewChild('multiSelectComp') multiSelectComp: MultiSelect;
  @ViewChild('multiSelectComp2') multiSelectComp2: MultiSelect;
  @ViewChild('multiSelectComp3') multiSelectComp3: MultiSelect;
  selectedClientName = [];
  showFinancials = false;
  queryFilterId: number;
  showApplyMsg = true;
  isShowHideColumns: boolean = false;
  _selectedColumns;
  _pCols: string[] = [];
  hideAbleColumns = [];
  customePtableSpinner: boolean;
  selectedPositionOption;

  @Input() get selectedColumns(): any[] {
    return this._selectedColumns;
  }

  set selectedColumns(val) {
    setTimeout(() => {
      this.columnToggle.setSelectedColumns(this._selectedColumns, 'lostRevenue');
      const col = this._selectedColumns;
      this._pCols = col?.map((f) => f.field);
    }, 500);
  }
  constructor(
    private readonly datePipe: DatePipe,
    private readonly utilizationService: UtilizationService,
    private readonly cdf: ChangeDetectorRef,
    private readonly projectService: ProjectService,
    private readonly layoutConfigService: LayoutConfigService,
    private readonly layoutUtilsService: LayoutUtilsService,
    private readonly cacheFilter: GetSetCacheFiltersService,
    private readonly authService: AuthService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly router: Router,
    private readonly columnToggle: ColumnToggleService
  ) {
    super();
  }

  async ngOnInit(): Promise<void> {
    if (window.innerWidth <= 1024) {
      this.resizeFlag = true;
    } else {
      this.resizeFlag = false;
    }

    if (this.cacheFilter.getCacheFilters('lost-revenue')) {
      this.dataFilter = this.cacheFilter.getCacheFilters('lost-revenue');
    }

    this.frozenCols = [
      { field: 'position.project.customer.name', monthLabel: 'Client Name', sort: true },
      { field: 'position.project.name', monthLabel: 'Project Name', sort: true },
      { field: 'position.name', monthLabel: 'Position Name', sort: true }
    ];
    this.hideAbleColumns = [
      { field: 'position.project.status', monthLabel: 'Project Status', sort: true },
      { field: 'position.start_date', monthLabel: 'Start Date', sort: true },
      { field: 'position.end_date', monthLabel: 'End Date', sort: true }
    ];
    this._selectedColumns = JSON.parse(localStorage.getItem('selectedColumnsArray'))?.lostRevenue
      ? JSON.parse(localStorage.getItem('selectedColumnsArray'))['lostRevenue']
      : this.hideAbleColumns;
    this._pCols = this._selectedColumns.map((f) => f.field);
    this.showFinancials = await this.authService.isPermittedAction([PermissionModules.VIEW_BILL_RATE_OPEN_POS]);
    await this.getProjectList();
    this.setExportHeaders();
    this.getPositionList();
    this.getStoredFilters();
    this.getClientGroup();
    this.getProjectGroup();
    this.getProjectStatus();
    this.getClient();
  }

  defaultFilters() {
    this.tags = [];
    this.showClientFilter = false;
    if (!this.dataFilter?.project_statuses) {
      this.dataFilter.project_statuses = this.defaultStatuses.toString();
      this.projectStatus = this.defaultStatuses;
      this.tags.push({
        label: 'Project Status',
        value: this.dataFilter.project_statuses,
        key: ['project_statuses']
      });
    }
    if (!this.dataFilter.start_date_lt && !this.dataFilter.end_date_gte) {
      const date = new Date();
      this.dataFilter.rollingOption = 'Current plus 2 months';
      if (this.dataFilter.rollingOption) {
        this.tags.push({
          label: 'Rolling',
          value: this.dataFilter.rollingOption.toString(),
          key: ['start_date_lt', 'end_date_gte', 'rollingOption']
        });
      }
    }
    this.applyTags();
  }
  getClient() {
    this.subscriptionManager.add(
      this.utilizationService.getClientData().subscribe((res) => {
        const client = [];
        //  const client = [{label:'--None--', value:{name:'',value:''}}];
        res?.body?.data?.customers?.map((c) => {
          client.push({
            label: c.customer.name,
            value: String(c.customer.id)
          });
        });
        this.client = client;
        this.sortList(this.client);
        this.cdf.detectChanges();
      })
    );
  }

  sortList(sortList) {
    if (this.sortList.length > 0) {
      sortList.sort((a, b) => {
        const fa = a?.label?.toLowerCase();
        const fb = b?.label?.toLowerCase();
        if (fa < fb) {
          return -1;
        }
        if (fa > fb) {
          return 1;
        }
        return 0;
      });
    }
  }

  sortColumn() {
    this.sortColumnFlag = true;
  }
  @HostListener('window:resize', ['$event'])
  onResize(event) {
    if (event.target.innerWidth <= 1024) {
      this.resizeFlag = true;
    } else {
      this.resizeFlag = false;
    }
  }

  ngAfterViewInit() {
    if (this.multiSelectComp) {
      this.multiSelectComp.options = this.statuses;
    }
    if (this.multiSelectComp2) {
      this.multiSelectComp2.options = this.client;
    }
    if (this.multiSelectComp3) {
      this.multiSelectComp3.options = this.projectList;
    }

    if (this.el) {
      this.loading = false;
      this.loading$.next(false);
      this.sidebarParams = { template: this.el };
      this.sidebarParams.template.open();
    }

    const data = document.getElementById('project-status');
    setTimeout(() => {
      data.click();
    }, 200);
  }

  getProjectStatus() {
    this.subscriptionManager.add(
      this.projectService.getProjectStatus().subscribe((res) => {
        const projectStatuses = res.data?.project_statuses || [];
        this.defaultStatuses = projectStatuses.filter((status) => status.project_status.is_default).map((status) => status.project_status.name);

        this.statuses = projectStatuses.map((status) => ({
          label: status.project_status.name,
          value: status.project_status.name
        }));
        this.defaultFilters();
      })
    );
  }

  statusSelected(event) {
    this.dataFilter.project_statuses = event?.value?.toString();
  }

  getClientGroup() {
    const requestObject = {
      // is_shared: true,
      resource: 'customers'
    };

    this.subscriptionManager.add(
      this.utilizationService.getGroup(requestObject).subscribe((res) => {
        const response = JSON.parse(JSON.stringify(res));
        const clientGrp = [];
        response?.data?.query_filters?.map((query) => {
          clientGrp.push({ label: query.query_filter.name, value: { name: query.query_filter.name, value: query.query_filter.query_string } });
        });
        this.clientGroup = clientGrp;
        this.sortList(this.clientGroup);
        this.cdf.detectChanges();
      })
    );
  }

  getProjectGroup() {
    const requestObject = {
      // is_shared: true,
      resource: 'projects'
    };

    this.subscriptionManager.add(
      this.utilizationService.getGroup(requestObject).subscribe((res) => {
        const response = JSON.parse(JSON.stringify(res));
        const projectGrp = [];
        response?.data?.query_filters?.map((query) => {
          projectGrp.push({ label: query.query_filter.name, value: { name: query.query_filter.name, value: query.query_filter.query_string } });
        });
        this.projectGroup = projectGrp;
        this.sortList(this.projectGroup);
        this.cdf.detectChanges();
      })
    );
  }

  getProjectList(): Promise<void> {
    return new Promise((resolve) => {
      this.subscriptionManager.add(
        this.utilizationService.getProjectList().subscribe((res) => {
          res.data.projects.forEach((project) => {
            this.projectList.push({
              label: `${project.project?.customer?.name} : ${project.project?.name}`,
              name: project?.project?.name,
              value: project.project.id.toString(),
              status: project?.project?.status
            });
          });
          this.sortList(this.projectList);
          this.positionReportData.forEach((pos) => {
            pos.position.project.status = this.projectList.find((x) => x.value == pos.position.project.id).status;
          });
          this.applyAllFilter();
          this.cdf.detectChanges();
          resolve();
        })
      );
    });
  }

  getPositionList() {
    this.subscriptionManager.add(
      this.utilizationService.getPositionList().subscribe((res) => {
        res?.data?.positions?.map((position) => {
          this.positionList.push({ label: position.position.name, value: position.position.id });
        });

        this.sortList(this.positionList);
        this.dataFilter.position_ids = this.dataFilter.position_ids;
        if (this.dataFilter.position_ids) {
          const position = this.positionList.find((position) => position.value === Number(this.dataFilter.position_ids));
          this.tags.push({
            label: 'Position',
            value: position?.label,
            key: ['position_ids']
          });
          this.selectedPositionOption = position;
        }
        this.applyAllFilter();
        if (this.dataFilter.position_ids) {
          this.dataFilter.position_ids = Number(this.dataFilter.position_ids);
        }
        this.cdf.detectChanges();
      })
    );
  }

  onPositionChange(e: any) {
    if (e.value) {
      this.selectedPositionOption = e.value;
      this.dataFilter.position_ids = e.value.value;
    } else {
      this.selectedPositionOption = null;
      this.dataFilter.position_ids = null;
    }
  }

  showHideColumns(type) {
    if (type == 'showColumns') {
      this.isShowHideColumns = true;
    } else {
      this.isShowHideColumns = false;
    }
  }

  onSelectColumsChange(event) {
    if (event) {
      this.columnToggle.setSelectedColumns(event.value, 'lostRevenue');
      this._selectedColumns = event.value;
      this._pCols = event.value.map((f) => f.field);
    }
    this.isShowHideColumns = !this.isShowHideColumns;
  }

  setExportHeaders() {
    const projectName = 'Project Name';
    const startDate = 'Start Date';
    this.exportPdfColumns = [
      { title: 'Client Name', dataKey: 'client_name' },
      { title: projectName, dataKey: 'project_name' },
      { title: 'Position', dataKey: 'position_name' },
      { title: startDate, dataKey: 'start_date' },
      { title: 'End Date', dataKey: 'end_date' }
    ];

    this.csvCols = this.exportPdfColumns.map((col) => col.dataKey);
    this.excelHeaders = [
      {
        client_name: 'Client Name',
        project_name: projectName,
        position_name: 'Position',
        start_date: startDate,
        end_date: 'End Date'
      }
    ];
  }

  loadReport(event?: LazyLoadEvent) {
    this.customePtableSpinner = true;
    this.sidebarParams.template.close();
    this.loading = true;
    this.showApplyMsg = false;
    this.positionReportData = [];
    let queryFilter: QueryFilterParams = {
      order_by: !this.sortColumnFlag ? (this.dataFilter?.order_by ? this.dataFilter.order_by : this.activeSort(event)) : this.activeSort(event),
      projection_detail_level: 'position_monthly',
      visibility: 'Public'
    };

    if (this.dataFilter.open_positions) {
      delete this.dataFilter['open_positions'];
    }

    if (this.dataFilter.rollingOption) {
      const { startDate, endDate } = this.getStartDateEndDateFromRolling(this.dataFilter.rollingOption);
      this.dataFilter.end_date_gte = this.datePipe.transform(startDate.toString(), AppConstants.format);
      this.dataFilter.start_date_lt = this.datePipe.transform(endDate.toString(), AppConstants.format);
      this.dataFilter.year = null;
      this.dataFilter.quarter = null;
      this.dataFilter.start_month = null;
      this.dataFilter.end_month = null;
    }

    if (this.dataFilter.year) {
      const startEndDate = this.getStartEndDateFromYear(this.dataFilter.year);
      this.dataFilter.end_date_gte = this.datePipe.transform(startEndDate.start_date.toString(), AppConstants.format);
      this.dataFilter.start_date_lt = this.datePipe.transform(startEndDate.end_date.toString(), AppConstants.format);
      this.dataFilter.quarter = null;
      this.dataFilter.rollingOption = null;
      this.dataFilter.start_month = null;
      this.dataFilter.end_month = null;
    }
    if (this.dataFilter.quarter) {
      const startEndDate = this.getStartEndDateFromQuarter(this.dataFilter.quarter);
      this.dataFilter.end_date_gte = this.datePipe.transform(startEndDate.start_date, AppConstants.format);
      this.dataFilter.start_date_lt = this.datePipe.transform(startEndDate.end_date, AppConstants.format);
      this.dataFilter.year = null;
      this.dataFilter.rollingOption = null;
      this.dataFilter.start_month = null;
      this.dataFilter.end_month = null;
    }

    if (!this.sortColumnFlag) {
      this.sortFieldName = this.dataFilter?.order_by ? this.dataFilter.order_by.split(':')[1] : event?.sortField;
      this.sortOrderNumber = this.dataFilter?.order_by ? (this.dataFilter.order_by.split(':')[0] === 'asc' ? 1 : -1) : event?.sortOrder;
    }

    if (this.dataFilter) {
      this.cacheFilter.setCacheFilters({ ...this.dataFilter, ...queryFilter }, 'lost-revenue');
      for (const [key] of Object.entries(this.dataFilter)) {
        if (key === 'employee_type_name') {
          queryFilter[`${key}`] = this.dataFilter[key].employee_type.name;
        } else {
          if (key !== 'order_by') queryFilter[`${key}`] = this.dataFilter[key];
        }
      }
    }
    if (!this.dataFilter.start_date_lt && !this.dataFilter.end_date_gte) {
      const date = new Date();
      queryFilter.end_date_gte = this.datePipe.transform(new Date(date.getFullYear(), date.getMonth(), 1), AppConstants.format);
      queryFilter.start_date_lt = this.datePipe.transform(new Date(date.getFullYear(), date.getMonth() + 3, 0), AppConstants.format);
    }
    delete queryFilter['date'];
    delete queryFilter['customer_name'];
    delete queryFilter['start_month'];
    delete queryFilter['end_month'];
    delete queryFilter['project_name'];
    delete queryFilter['client'];
    delete queryFilter['value'];
    delete queryFilter['year'];
    delete queryFilter['clientName'];
    delete queryFilter['ClientName'];
    delete queryFilter['projectName'];

    queryFilter = this.queryStringUtil(queryFilter);
    this.loading = true;
    this.subscriptionManager.add(
      this.utilizationService.getOpenPositionReport(queryFilter).subscribe(
        (res) => {
          this.positionReportData = res.body.data?.positions ? res.body.data?.positions : [];
          this.positionReportData.forEach((pos) => {
            pos.position.project.status = this.projectList?.find((x) => x.value == pos.position.project.id)?.status;
          });
          this.makeRowsSameHeight();
          this.prepareHeaders();
          setTimeout(() => {
            this.loading = false;
            this.customePtableSpinner = false;
            this.cdf.detectChanges();
          }, 200);
          this.cdf.detectChanges();
        },
        () => (this.loading = false)
      )
    );
  }

  makeRowsSameHeight() {
    setTimeout(() => {
      if (document.getElementsByClassName('p-datatable-scrollable-wrapper').length) {
        const wrapper = document.getElementsByClassName('p-datatable-scrollable-wrapper');
        for (var i = 0; i < wrapper.length; i++) {
          const w = wrapper.item(i) as HTMLElement;
          const frozen_rows: any = w.querySelectorAll('.p-datatable-frozen-view tr');
          const unfrozen_rows: any = w.querySelectorAll('.p-datatable-unfrozen-view tr');
          for (let i = 0; i < frozen_rows.length; i++) {
            if (frozen_rows[i].clientHeight > unfrozen_rows[i].clientHeight) {
              unfrozen_rows[i].style.height = frozen_rows[i].clientHeight + 'px';
            } else if (frozen_rows[i].clientHeight < unfrozen_rows[i].clientHeight) {
              frozen_rows[i].style.height = unfrozen_rows[i].clientHeight + 'px';
            }
          }
        }
      }
      this.layoutConfigService.updateHeight$.next(true);
      this.height = height;
    });
  }

  prepareHeaders() {
    this.tableHeaders = [];
    this.exportReportData = [];
    this.excelExportReportData = [];
    this.setExportHeaders();
    if (this.positionReportData.length) {
      for (const position of this.positionReportData) {
        let exportData = {};
        let excelExportData = {};
        const format = 'MM/dd/yyyy';
        exportData = {
          client_name: position?.position?.project?.customer?.name,
          project_name: position?.position?.project?.name,
          position_name: position?.position?.name,
          start_date: this.datePipe.transform(position?.position?.start_date, format),
          end_date: this.datePipe.transform(position?.position?.end_date, format)
        };
        excelExportData = { ...exportData };
        if (position?.position?.validated_monthly_positions && this.showFinancials) {
          for (const monthly_position of position?.position?.validated_monthly_positions) {
            const valid_position = monthly_position.validated_monthly_position;
            const tableHeader: TableHeader = {
              month: valid_position.month,
              monthLabel: `${MONTH_NAMES[valid_position.month - 1]} ${valid_position.year}`,
              year: valid_position.year.toString(),
              id: Number(`${valid_position.year}${valid_position.month - 1}`)
            };
            exportData[tableHeader.monthLabel] =
              '$' +
              Math.round(Number(valid_position.open_positions_cost))
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
            excelExportData[tableHeader.monthLabel + ' (in $)'] = Math.round(Number(valid_position.open_positions_cost));
            valid_position[tableHeader.monthLabel] = valid_position.open_positions_cost;
            this.tableHeaders = [...this.tableHeaders, tableHeader];
          }
        }
        this.exportReportData.push(exportData);
        this.excelExportReportData.push(excelExportData);
      }
      const seen = new Set();
      this.tableHeaders = this.tableHeaders.filter((el) => {
        const duplicate = seen.has(el.id);
        seen.add(el.id);
        return !duplicate;
      });

      this.tableHeaders.forEach((header) => {
        this.exportPdfColumns.push({ title: header.monthLabel, dataKey: header.monthLabel });
        this.excelHeaders[0][header.monthLabel + ' (in $)'] = header.monthLabel + ' (in $)';
        this.csvCols.push(header.monthLabel);
      });
    }
    this.addZeroToNonAvailableData();
    this.layoutConfigService.updateHeight$.next(true);
    this.height = height;
    this.cdf.detectChanges();
  }

  addZeroToNonAvailableData() {
    const reportData = [];
    const excelExportData = [];
    for (const data of this.exportReportData) {
      const keys = Object.keys(data);
      this.tableHeaders.forEach((header) => {
        if (!keys.includes(header.monthLabel)) {
          data[header.monthLabel] = '$0';
        }
      });
      reportData.push(data);
    }
    for (const data of this.excelExportReportData) {
      const keys = Object.keys(data);
      this.tableHeaders.forEach((header) => {
        if (!keys.includes(header.monthLabel + ' (in $)')) {
          data[header.monthLabel + ' (in $)'] = 0;
        }
      });
      excelExportData.push(data);
    }
    this.exportReportData = reportData;
    this.excelExportReportData = excelExportData;
  }

  getValues(header, data) {
    for (const position of data?.position?.validated_monthly_positions) {
      const keys = Object.keys(position.validated_monthly_position);
      if (keys.includes(header.monthLabel)) {
        return Math.round(position.validated_monthly_position.revenue);
      }
    }
    return 0;
  }

  activeSort(event: LazyLoadEvent) {
    if (event?.sortField) {
      if (event.sortOrder === 1) {
        return 'asc:' + event.sortField;
      } else {
        return 'desc:' + event.sortField;
      }
    }
    return null;
  }

  // used to take query string param and removes values which are not set in the query string
  queryStringUtil(queryStringParam: QueryFilterParams) {
    const queryFilter: QueryFilterParams = {};
    if (queryStringParam) {
      for (const [key] of Object.entries(queryStringParam)) {
        if (queryStringParam[key] === '' || queryStringParam[key] === null || queryStringParam[key] === undefined) {
          delete queryStringParam[key];
        }
      }
    }
    return queryStringParam;
  }

  openSideBar() {
    this.sidebarParams = { template: this.el };
    this.sidebarParams.template.open();
  }

  onClose() {
    this.sidebarParams.template.close();
    this.dateRequired = false;
    this.dateError = false;
  }

  clearFilters() {
    this.dataFilter = new IFilter();
    delete this.dataFilter['open_positions'];
    this.projectStatus = this.defaultStatuses;
    this.dateRequired = false;
    this.selectedPositionOption = null;
    this.dateError = false;
    this.clientName = null;
    this.selectedClientName = [];
    this.projectName = null;
    this.selectedProjectName = [];
    this.defaultFilters();
    const date = new Date();
  }

  filterReport() {
    if (this.dataFilter.start_month || this.dataFilter.end_month) {
      if (!this.dataFilter.start_month) {
        this.dateRequired = true;
      }
      if (!this.dataFilter.end_month) {
        this.dateRequired = true;
      }
    }
    if (this.dataFilter?.value?.value) {
      this.dataFilter.employee_ids = this.dataFilter.value.value;
    }
    if (!this.dateError && !this.dateRequired) {
      this.applyTags();
      this.loadReport();
      this.onClose();
    }
  }

  exportReport(type) {
    if (type === 'csv') {
      this.utilizationService.exportToCsv(this.exportReportData, 'openPositionReport', this.csvCols);
    }
    if (type === 'pdf') {
      this.utilizationService.exportPdf(this.exportPdfColumns, this.exportReportData, 'openPositionReport', 4);
    }
    if (type === 'excel') {
      this.utilizationService.exportExcel(this.excelHeaders, this.excelExportReportData, 'openPositionReport');
    }
  }

  dateSelected() {
    if (this.dataFilter.date[1] !== null) {
      this.dataFilter.start_date_lt = this.datePipe.transform(this.dataFilter.date[1], AppConstants.format);
    }
    if (this.dataFilter.date[0] !== null) {
      this.dataFilter.end_date_gte = this.datePipe.transform(this.dataFilter.date[0], AppConstants.format);
    }
  }

  startMonthSelected(event) {
    this.dateError = false;
    this.dateRequired = false;
    this.dataFilter.end_date_gte = this.datePipe.transform(event, AppConstants.format);
    this.dataFilter.start_month = moment(this.dataFilter.end_date_gte).toDate();
    if (this.dataFilter.start_date_lt && this.dataFilter.end_date_gte) {
      if (new Date(this.dataFilter.start_date_lt) < new Date(this.dataFilter.end_date_gte)) {
        this.dateError = true;
      }
    }
  }
  endMonthSelected(event) {
    this.dateError = false;
    this.dateRequired = false;
    let date = new Date(event);
    date = new Date(date.getFullYear(), date.getMonth() + 1, 0);
    this.dataFilter.start_date_lt = this.datePipe.transform(date, AppConstants.format);
    this.dataFilter.end_month = moment(this.dataFilter.start_date_lt).toDate();
    if (this.dataFilter.start_date_lt && this.dataFilter.end_date_gte) {
      if (new Date(this.dataFilter.start_date_lt) < new Date(this.dataFilter.end_date_gte)) {
        this.dateError = true;
      }
    }
  }

  getClientIds() {
    const paramsToRemove = ['offset', 'limit'];
    const filteredParams = this.removeParams(this.dataFilter.customer_name.value.split('&'), paramsToRemove);
    this.subscriptionManager.add(
      this.utilizationService.getClientIds(filteredParams).subscribe((res) => {
        if (res?.customer_ids) {
          this.dataFilter.customer_ids = res.customer_ids.join(',');
        } else {
          this.dataFilter.customer_ids = '';
        }
        this.cdf.detectChanges();
      })
    );
  }

  getProjectIds() {
    const paramsToRemove = ['offset', 'limit'];
    const filteredParams = this.removeParams(this.dataFilter?.project_name?.value.split('&'), paramsToRemove);
    this.subscriptionManager.add(
      this.utilizationService.getProjectIds(filteredParams).subscribe((res) => {
        if (res?.project_ids) {
          this.dataFilter.project_ids = res.project_ids.join(',');
        } else {
          this.dataFilter.project_ids = '';
        }
        this.cdf.detectChanges();
      })
    );
  }

  removeParams(params, paramsToRemove) {
    return params
      .filter((param) => {
        const [key, value] = param.split('=');
        return !paramsToRemove.includes(key) && value !== '' && value !== null;
      })
      .join('&');
  }

  applyTags() {
    this.tags = [];
    if (!this.dataFilter) {
      return;
    }

    if (this.dataFilter.start_date_lt && this.dataFilter.end_date_gte && !this.dataFilter.year && !this.dataFilter.quarter && !this.dataFilter.rollingOption) {
      const dateFormat = 'MM/dd/yyyy';
      const value = this.datePipe.transform(this.dataFilter.end_date_gte, dateFormat) + ' - ' + this.datePipe.transform(this.dataFilter.start_date_lt, dateFormat);
      this.tags.push({
        label: 'Date Range',
        value: value,
        key: ['start_date_lt', 'end_date_gte', 'start_month', 'end_month']
      });
    }

    if (this.dataFilter.project_name) {
      this.tags.push({
        label: 'Project Group',
        value: this.dataFilter.project_name.name,
        key: ['project_name', 'project_ids']
      });
    }

    if (this.dataFilter.rollingOption) {
      this.tags.push({
        label: 'Rolling',
        value: this.dataFilter.rollingOption.toString(),
        key: ['start_date_lt', 'end_date_gte', 'rollingOption']
      });
    }

    if (this.dataFilter.year) {
      this.tags.push({
        label: 'By Year',
        value: this.dataFilter.year.toString(),
        key: ['start_date_lt', 'end_date_gte', 'year']
      });
    }

    if (this.dataFilter.quarter) {
      this.tags.push({
        label: 'By Quater',
        value: this.dataFilter.quarter.toString(),
        key: ['start_date_lt', 'end_date_gte', 'quarter']
      });
    }

    if (this.dataFilter?.ClientName?.length) {
      this.tags.push({
        label: 'Client',
        value: this.selectedClientName?.join(','),
        key: ['ClientName', 'customer_ids']
      });
    }

    if (this.dataFilter?.projectName?.length) {
      this.tags.push({
        label: 'Project',
        value: this.selectedProjectName?.join(','),
        key: ['projectName', 'project_ids']
      });
    }

    if (this.dataFilter.customer_name) {
      this.tags.push({
        label: 'Client Group',
        value: this.dataFilter.customer_name.name,
        key: ['customer_name', 'customer_ids']
      });
    }

    if (this.dataFilter.position_ids) {
      const position = this.positionList.find((position) => position.value === Number(this.dataFilter.position_ids));
      if (position) {
        this.tags.push({
          label: 'Position',
          value: position?.label,
          key: ['position_ids']
        });
      }
    }
    if (this.dataFilter.project_statuses) {
      this.tags.push({
        label: 'Project Status',
        value: this.dataFilter.project_statuses,
        key: ['project_statuses']
      });
    }
  }

  onCloseFromFilterPanel(tagValue) {
    tagValue?.key.forEach((key) => {
      if (key === 'ClientName') {
        this.dataFilter[key] = null;
        this.clientName = null;
        this.selectedClientName = [];
      } else if (key === 'projectName') {
        this.dataFilter[key] = null;
        this.projectName = null;
        this.selectedProjectName = [];
      } else if (key === 'position_ids') {
        this.dataFilter[key] = null;
        this.selectedPositionOption = null;
      } else {
        this.dataFilter[key] = null;
      }
    });
    this.doFilterData();
  }

  clientSelected(event) {
    this.dataFilter.customer_ids = event?.value?.toString();
    let clientName = this.client.filter((elist) => elist.value == event.itemValue);
    if (clientName.length && clientName[0]?.label) {
      if (this.selectedClientName?.includes(clientName[0]?.label)) {
        this.selectedClientName = this.selectedClientName.filter((emp) => emp !== clientName[0].label);
      } else {
        this.selectedClientName.push(clientName[0]?.label);
      }
    }
    this.dataFilter.ClientName = this.selectedClientName;
  }

  projectSelected(event) {
    this.dataFilter.project_ids = event?.value?.toString();
    let projectName = this.projectList.filter((elist) => elist.value == event.itemValue);
    if (projectName.length && projectName[0]?.name) {
      if (this.selectedProjectName.includes(projectName[0]?.name)) {
        this.selectedProjectName = this.selectedProjectName.filter((emp) => emp !== projectName[0].name);
      } else {
        this.selectedProjectName.push(projectName[0]?.name);
      }
    }
    this.dataFilter.projectName = this.selectedProjectName;
  }

  onRemoveStatusFilter(statusValue) {
    if (statusValue.length) {
      this.dataFilter.project_statuses = statusValue.join(',');
      this.projectStatus = statusValue;
    } else {
      this.dataFilter.project_statuses = null;
      this.projectStatus = null;
    }
    this.doFilterData();
  }

  doFilterData() {
    this.loadReport();
    this.applyTags();
  }

  showHelpData(data) {
    data.showHelpIconData = !data.showHelpIconData;
    this.loading$.next(true);
    this.utilizationService.getProject(data.position.project?.id).subscribe((res) => {
      this.loading$.next(false);
      data.position.customer_name = res?.data?.project?.customer?.name;
      data.position.contact_person = res?.data?.project?.contacts;
      this.cdf.detectChanges();
    });
  }
  getFixCssClass(col) {
    return 'fix-col-3';
  }
  getDynCssClass(col) {
    if (col.length <= 3) {
      return 'dynamic-col-3';
    } else {
      return 'dynamic-col-more';
    }
  }

  resetFilter() {
    this.dataFilter = new IFilter();
    delete this.dataFilter['open_positions'];
    this.cacheFilter.resetCacheFilters('lost-revenue');
    this.projectStatus = this.defaultStatuses;
    this.tags = [];
    this.selectedClientName = [];
    this.clientName = null;
    this.selectedProjectName = [];
    this.projectName = null;
    this.selectedPositionOption = null;
    this.showClientFilter = false;
    this.showProjectFilter = false;
    this.router.navigate([], { relativeTo: this.activatedRoute, queryParams: { filterId: null }, queryParamsHandling: 'merge' });
    // reset the filter selection as well
    this.selectedFilterFormControl = new FormControl('');
    this.defaultFilters();
    this.loadReport();
  }

  openSaveFilterList() {
    this.showFilterListDialog = true;
    this.showSavedFilter = true;
    this.cdf.detectChanges();
  }
  applyAllFilter() {
    if (this.dataFilter.end_date_gte && this.dataFilter.start_month) {
      this.dataFilter.start_month = moment(this.dataFilter.end_date_gte).toDate();
    }
    if (this.dataFilter.start_date_lt && this.dataFilter.end_month) {
      this.dataFilter.end_month = moment(this.dataFilter.start_date_lt).toDate();
    }
    if (this.dataFilter?.project_statuses) {
      this.dataFilter.project_statuses = this.dataFilter.project_statuses.replace(/%2C/g, ',');
      this.projectStatus = this.dataFilter.project_statuses.split(',');
    }
    if (this.dataFilter?.project_grp_name && this.dataFilter?.project_grp_value) {
      this.dataFilter.project_name = { name: this.dataFilter.project_grp_name, value: this.dataFilter.project_grp_value.replace(/%3D/g, '=').replace(/%26/g, '&') };
      delete this.dataFilter.project_grp_name;
      delete this.dataFilter.project_grp_value;
      // this.showProjectFilter = true;
    }
    if (this.dataFilter?.client_grp_name && this.dataFilter?.client_grp_value) {
      this.dataFilter.customer_name = { name: this.dataFilter.client_grp_name, value: this.dataFilter.client_grp_value.replace(/%3D/g, '=').replace(/%26/g, '&') };
      delete this.dataFilter.client_grp_name;
      delete this.dataFilter.client_grp_value;
      // this.showClientFilter = true;
    }
    if (this.dataFilter?.project_ids) {
      this.dataFilter.project_ids = this.dataFilter.project_ids.replace(/%2C/g, ',');
      this.showProjectFilter = true;
    }
    if (this.dataFilter.position_ids) {
      const position = this.positionList.find((position) => position.value === Number(this.dataFilter.position_ids));
      this.selectedPositionOption = position;
      this.dataFilter.position_ids = Number(this.dataFilter.position_ids);
    }
    if (this.dataFilter?.customer_ids) {
      this.dataFilter.customer_ids = this.dataFilter.customer_ids.replace(/%2C/g, ',');
      this.showClientFilter = true;
    }
    if (this.dataFilter?.ClientName?.length && this.dataFilter.customer_ids) {
      this.dataFilter.customer_ids = this.dataFilter.customer_ids.replace(/%2C/g, ',');
      this.clientName = this.dataFilter.customer_ids.split(',');
      this.selectedClientName = this.dataFilter.ClientName.toString().replace(/%2C/g, ',').split(',');
      this.showClientFilter = false;
    }

    if (this.dataFilter.projectName?.length && this.dataFilter.project_ids) {
      this.dataFilter.project_ids = this.dataFilter.project_ids.replace(/%2C/g, ',');
      this.projectName = this.dataFilter.project_ids.split(',');
      this.selectedProjectName = this.dataFilter.projectName.toString().replace(/%2C/g, ',').split(',');
      this.showProjectFilter = false;
      this.cdf.detectChanges();
    }
    this.cdf.detectChanges();
    this.applyTags();
  }
  applyFilter() {
    this.showSavedFilter = false;
    this.dataFilter = JSON.parse('{"' + decodeURI(this.selectedFilter.query_filter.query_string).replace(/"/g, '\\"').replace(/&/g, '","').replace(/=/g, '":"') + '"}');
    this.applyAllFilter();
    this.selectedFilter = null;
    this.filteredFilters.data.query_filters = this.availableFilters;
    this.loadReport();
    this.closeModal();
  }

  closeModal() {
    this.showFilterListDialog = false;
    this.selectedFilter = null;
    this.showEditDialog = false;
    this.editFilterObj = null;
    this.showNameError = false;
    this.showDeleteDialog = false;
    this.deleteFilterObj = null;
    this.showShareDialog = false;
    this.shareFilterObj = null;
  }

  showSaveClientFilterSelected() {
    this.showClientFilter = !this.showClientFilter;
    this.dataFilter.customer_ids = null;
    this.dataFilter.customer_name = null;
    this.selectedClientName = [];
    this.dataFilter.ClientName = [];
    this.clientName = null;
    this.cdf.detectChanges();
  }

  showSaveProjectFilterSelected() {
    this.showProjectFilter = !this.showProjectFilter;
    this.dataFilter.project_ids = null;
    this.dataFilter.projectName = [];
    this.selectedProjectName = [];
    this.projectName = null;
    this.dataFilter.project_name = null;
    this.cdf.detectChanges();
  }

  onSaveFilter() {
    let filter = JSON.parse(JSON.stringify(this.dataFilter));
    filter = this.queryStringUtil(filter);
    if (this.dataFilter?.project_name?.name) {
      filter.project_grp_name = this.dataFilter?.project_name?.name;
      filter.project_grp_value = this.dataFilter?.project_name?.value;
    }
    if (this.dataFilter?.customer_name?.name) {
      filter.client_grp_name = this.dataFilter?.customer_name?.name;
      filter.client_grp_value = this.dataFilter?.customer_name?.value;
    }

    const requestObject: SaveFilter = {
      query_string: this.serialize(filter),
      resource: 'openPositionReport'
    };
    const dialogTitle = 'Save Filter Group';
    const dialogRef = this.layoutUtilsService.saveClientGroupName(dialogTitle, requestObject);
    dialogRef.afterClosed().subscribe((filtersResponse) => {
      if (filtersResponse) {
        this.getStoredFilters();
        this.layoutUtilsService.showActionNotification('Filter has been saved successfully', AlertType.Success);
      }
    });
  }
  // converts the object in to query param string
  serialize = (obj) => {
    const str = [];
    for (const p in obj) {
      if (obj.hasOwnProperty(p)) {
        str.push(encodeURIComponent(p) + '=' + encodeURIComponent(obj[p]));
      }
    }
    return str.join('&');
  };

  getStoredFilters() {
    const requestObject = {
      resource: 'openPositionReport'
    };
    this.subscriptionManager.add(
      this.utilizationService.getStoredFilters(requestObject).subscribe(
        (res: ISavedFilterList) => {
          this.loading = false;
          this.sharedFilters = [];
          this.myFilters = [];
          this.filteredFilters = JSON.parse(JSON.stringify(res)); // to deep copy the object
          this.sharedFilters = this.filteredFilters?.data?.query_filters?.filter((q) => q.query_filter.is_shared === true);
          this.myFilters = this.filteredFilters?.data?.query_filters?.filter((q) => q.query_filter.is_shared === false);
          this.availableFilters = res.data.query_filters;
          this.cdf.detectChanges();
          this.routerListener();
        },
        () => (this.loading = false)
      )
    );
  }

  getStartDateEndDateFromRolling(rollingOption: string) {
    const currentDate = new Date();
    const rollingRelativeMonth = rollingOption.split(' ')[1];
    const rollingMonths = parseInt(rollingOption.split(' ')[2]);

    const startDate = new Date(currentDate);
    startDate.setDate(1);

    const endDate = new Date(startDate);
    endDate.setMonth(startDate.getMonth() + rollingMonths + 1, 0);

    if (rollingRelativeMonth === 'minus') {
      startDate.setMonth(currentDate.getMonth() - 1);
      endDate.setMonth(currentDate.getMonth() + rollingMonths, 0);
      endDate.setFullYear(currentDate.getFullYear());
    }

    return { startDate, endDate };
  }

  getStartEndDateFromQuarter(quarter) {
    const now = new Date();
    const quarterDates = {
      Q1: {
        start_date: new Date(now.getFullYear(), 0, 1, 0, 0, 0, 0),
        end_date: new Date(now.getFullYear(), 2, 31, 11, 59, 59, 999)
      },
      Q2: {
        start_date: new Date(now.getFullYear(), 3, 1, 0, 0, 0, 0),
        end_date: new Date(now.getFullYear(), 5, 30, 11, 59, 59, 999)
      },
      Q3: {
        start_date: new Date(now.getFullYear(), 6, 1, 0, 0, 0, 0),
        end_date: new Date(now.getFullYear(), 8, 30, 11, 59, 59, 999)
      },
      Q4: {
        start_date: new Date(now.getFullYear(), 9, 1, 0, 0, 0, 0),
        end_date: new Date(now.getFullYear(), 11, 31, 11, 59, 59, 999)
      }
    };
    return quarterDates[quarter];
  }

  getStartEndDateFromYear(year) {
    return {
      start_date: new Date(year, 0, 1, 0, 0, 0, 0),
      end_date: new Date(year, 11, 31, 11, 59, 59, 999)
    };
  }

  searchFilters(filter: string) {
    this.filteredFilters.data.query_filters = filter.length
      ? this.availableFilters.filter((availableFilter) => availableFilter.query_filter.name.toLowerCase().includes(filter.toLowerCase()))
      : this.availableFilters;
    this.sharedFilters = this.filteredFilters?.data?.query_filters.filter((q) => q.query_filter.is_shared === true);
    this.myFilters = this.filteredFilters?.data?.query_filters.filter((q) => q.query_filter.is_shared === false);
  }

  editFilter(filter) {
    this.showEditDialog = true;
    this.editFilterObj = _.cloneDeep(filter);
  }
  shareFilter(filterOption) {
    this.showShareDialog = true;
    this.shareFilterObj = { ...filterOption, header: 'Share', text: `Do you want to share Filter "${filterOption.query_filter.name}" to all users publically?` };
    this.shareFilterObj.query_filter.is_shared = !this.shareFilterObj.query_filter.is_shared;
  }

  unShareFilter(filterOption) {
    this.showShareDialog = true;
    this.shareFilterObj = { ...filterOption, header: 'Unshare', text: `Do you want to unshare Filter "${filterOption.query_filter.name}" from all users?` };
    this.shareFilterObj.query_filter.is_shared = !this.shareFilterObj.query_filter.is_shared;
  }

  saveShareFilter() {
    this.isSubmitting = true;
    this.subscriptionManager.add(
      this.utilizationService.updateFilter(this.shareFilterObj.query_filter.id, this.shareFilterObj).subscribe(
        (res) => {
          this.layoutUtilsService.showActionNotification(this.shareFilterObj.query_filter.is_shared ? AppConstants.shareFilter : AppConstants.unShareFilter, AlertType.Success);
          this.isSubmitting = false;
          this.utilizationService.showNewSharedFilter.next('Lost Revenue');
          this.closeModal();
          this.getStoredFilters();
          this.showSavedFilter = true;
          this.showFilterListDialog = true;
          this.cdf.detectChanges();
        },
        () => (this.isSubmitting = false)
      )
    );
  }
  inputFilterName() {
    this.showNameError = false;
  }

  deleteFilter(filterOption) {
    this.showDeleteDialog = true;
    this.deleteFilterObj = filterOption;
  }

  saveDeleteFilter() {
    this.isSubmitting = true;
    this.subscriptionManager.add(
      this.utilizationService.deleteStoredFilters(this.deleteFilterObj.query_filter.id).subscribe(
        (res) => {
          this.layoutUtilsService.showActionNotification(AppConstants.deleteFilter, AlertType.Success);
          this.isSubmitting = false;
          this.closeModal();
          this.getStoredFilters();
          this.showSavedFilter = true;
          this.showFilterListDialog = true;
          this.cdf.detectChanges();
          this.utilizationService.showNewSharedFilter.next('Lost Revenue');
        },
        () => (this.isSubmitting = false)
      )
    );
  }

  saveEditFilter() {
    if (!this.editFilterObj.query_filter.name.length) {
      this.showNameError = true;
    } else {
      this.isSubmitting = true;
      this.subscriptionManager.add(
        this.utilizationService.updateFilter(this.editFilterObj.query_filter.id, this.editFilterObj).subscribe(
          (res) => {
            this.layoutUtilsService.showActionNotification(AppConstants.updateFilter, AlertType.Success);
            this.isSubmitting = false;
            this.closeModal();
            this.getStoredFilters();
            this.showSavedFilter = true;
            this.showFilterListDialog = true;
            this.cdf.detectChanges();
          },
          () => (this.isSubmitting = false)
        )
      );
    }
  }

  private routerListener() {
    this.activatedRoute.queryParamMap.subscribe((params: Params) => {
      // grab the filter id from the url if present we will make an api call to get the specific filter from that id.
      if (params.params.filterId) {
        this.queryFilterId = params.params.filterId;
        this.getTheFilterById();
      }
    });
  }

  // used to get the filter by its id
  getTheFilterById() {
    this.subscriptionManager.add(
      this.projectService.getTheFilterById(this.queryFilterId).subscribe(
        (res) => {
          // if we get some response only then we may try to apply filter
          if (res) {
            this.selectedFilter = res?.data;
            this.applyFilter();
          }
        },
        (error) => {
          this.layoutUtilsService.showActionNotification(AppConstants.problemFetchingFilterById, AlertType.Error);
        }
      )
    );
  }

  copyLinkToTheFilter(filterId: number) {
    const filterHolder = document.createElement('textarea');
    filterHolder.style.position = 'fixed';
    filterHolder.style.left = '0';
    filterHolder.style.top = '0';
    filterHolder.style.opacity = '0';
    // construct the full url to be copied
    // e.g. http://localhost:4200/project/manage?filterId=3
    const hostName = `${window.location.protocol}${window.location.host}`;
    const filterString = `${hostName}${this.router.url.split('?')[0]}/?filterId=${filterId}`;

    filterHolder.value = filterString;
    document.body.appendChild(filterHolder);
    filterHolder.focus();
    filterHolder.select();
    document.execCommand('copy');
    document.body.removeChild(filterHolder);
    this.layoutUtilsService.showActionNotification(AppConstants.filterLinkCopied, AlertType.Success);
  }

  applySelectedFilterAndUpdateUrl() {
    this.showSavedFilter = false;
    this.showFilterListDialog = false;
    this.selectedFilter = this.selectedFilterFormControl.value;
    this.router.navigate([], { relativeTo: this.activatedRoute, queryParams: { filterId: this.selectedFilter?.query_filter?.id }, queryParamsHandling: 'merge' });
  }
}

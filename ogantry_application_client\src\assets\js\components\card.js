"use strict";var KTCard=function(t,e){var n=this,i=KTUtil.getById(t),o=KTUtil.getBody();if(i){var r={toggleSpeed:400,sticky:{releseOnReverse:!1,offset:300,zIndex:101}},s={construct:function(t){return KTUtil.data(i).has("card")?n=KTUtil.data(i).get("card"):(s.init(t),s.build(),KTUtil.data(i).set("card",n)),n},init:function(t){n.element=i,n.events=[],n.options=KTUtil.deepExtend({},r,t),n.header=KTUtil.child(i,".card-header"),n.footer=KTUtil.child(i,".card-footer"),KTUtil.child(i,".card-body")?n.body=KTUtil.child(i,".card-body"):KTUtil.child(i,".form")&&(n.body=KTUtil.child(i,".form"))},build:function(){var t=KTUtil.find(n.header,"[data-card-tool=remove]");t&&KTUtil.addEvent(t,"click",function(t){t.preventDefault(),s.remove()});var e=KTUtil.find(n.header,"[data-card-tool=reload]");e&&KTUtil.addEvent(e,"click",function(t){t.preventDefault(),s.reload()});var i=KTUtil.find(n.header,"[data-card-tool=toggle]");i&&KTUtil.addEvent(i,"click",function(t){t.preventDefault(),s.toggle()})},initSticky:function(){n.options.sticky.offset;n.header&&window.addEventListener("scroll",s.onScrollSticky)},onScrollSticky:function(t){var e=n.options.sticky.offset;if(!isNaN(e)){var i=KTUtil.getScrollTop();i>=e&&!1===KTUtil.hasClass(o,"card-sticky-on")?(s.eventTrigger("stickyOn"),KTUtil.addClass(o,"card-sticky-on"),s.updateSticky()):1.5*i<=e&&KTUtil.hasClass(o,"card-sticky-on")&&(s.eventTrigger("stickyOff"),KTUtil.removeClass(o,"card-sticky-on"),s.resetSticky())}},updateSticky:function(){var t,e,i;n.header&&(KTUtil.hasClass(o,"card-sticky-on")&&(t=n.options.sticky.position.top instanceof Function?parseInt(n.options.sticky.position.top.call(this,n)):parseInt(n.options.sticky.position.top),e=n.options.sticky.position.left instanceof Function?parseInt(n.options.sticky.position.left.call(this,n)):parseInt(n.options.sticky.position.left),i=n.options.sticky.position.right instanceof Function?parseInt(n.options.sticky.position.right.call(this,n)):parseInt(n.options.sticky.position.right),KTUtil.css(n.header,"z-index",n.options.sticky.zIndex),KTUtil.css(n.header,"top",t+"px"),KTUtil.css(n.header,"left",e+"px"),KTUtil.css(n.header,"right",i+"px")))},resetSticky:function(){n.header&&!1===KTUtil.hasClass(o,"card-sticky-on")&&(KTUtil.css(n.header,"z-index",""),KTUtil.css(n.header,"top",""),KTUtil.css(n.header,"left",""),KTUtil.css(n.header,"right",""))},remove:function(){!1!==s.eventTrigger("beforeRemove")&&(KTUtil.remove(i),s.eventTrigger("afterRemove"))},setContent:function(t){t&&(n.body.innerHTML=t)},getBody:function(){return n.body},getSelf:function(){return i},reload:function(){s.eventTrigger("reload")},toggle:function(){KTUtil.hasClass(i,"card-collapse")||KTUtil.hasClass(i,"card-collapsed")?s.expand():s.collapse()},collapse:function(){!1!==s.eventTrigger("beforeCollapse")&&(KTUtil.slideUp(n.body,n.options.toggleSpeed,function(){s.eventTrigger("afterCollapse")}),KTUtil.addClass(i,"card-collapse"))},expand:function(){!1!==s.eventTrigger("beforeExpand")&&(KTUtil.slideDown(n.body,n.options.toggleSpeed,function(){s.eventTrigger("afterExpand")}),KTUtil.removeClass(i,"card-collapse"),KTUtil.removeClass(i,"card-collapsed"))},eventTrigger:function(t){for(var e=0;e<n.events.length;e++){var i=n.events[e];if(i.name==t){if(1!=i.one)return i.handler.call(this,n);if(0==i.fired)return n.events[e].fired=!0,i.handler.call(this,n)}}},addEvent:function(t,e,i){return n.events.push({name:t,handler:e,one:i,fired:!1}),n}};return n.setDefaults=function(t){r=t},n.remove=function(){return s.remove(html)},n.initSticky=function(){return s.initSticky()},n.updateSticky=function(){return s.updateSticky()},n.resetSticky=function(){return s.resetSticky()},n.destroySticky=function(){s.resetSticky(),window.removeEventListener("scroll",s.onScrollSticky)},n.reload=function(){return s.reload()},n.setContent=function(t){return s.setContent(t)},n.toggle=function(){return s.toggle()},n.collapse=function(){return s.collapse()},n.expand=function(){return s.expand()},n.getBody=function(){return s.getBody()},n.getSelf=function(){return s.getSelf()},n.on=function(t,e){return s.addEvent(t,e)},n.one=function(t,e){return s.addEvent(t,e,!0)},s.construct.apply(n,[e]),n}};"undefined"!=typeof module&&void 0!==module.exports&&(module.exports=KTCard);

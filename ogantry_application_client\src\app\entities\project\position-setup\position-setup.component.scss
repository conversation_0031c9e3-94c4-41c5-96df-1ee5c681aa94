@import 'src/app/@shared/components/card-header/card-header.component.scss';
@import 'src/assets/sass/components/variables.demo';
#positionSetup {
  .card-body {
    padding: 1rem;
  }

  .fix-action-width {
    max-width: 10% !important;
    width: 10% !important;
  }

  ::ng-deep .p-dropdown .p-dropdown-trigger {
    width: 1.2rem;
  }
  ::ng-deep .p-dropdown {
    width: 100%;
    border: 0;
  }
  ::ng-deep .p-dropdown:not(.p-disabled):hover,
  ::ng-deep .p-dropdown:not(.p-disabled).p-focus {
    border: 1px solid #119da4;
    border-radius: 6px;
    background-color: #ffffff;
    box-shadow: 0 0 2px 1px rgba(17, 157, 164, 0.2);
  }

  .editRow {
    border: 1px solid #ced4da !important;
    border-radius: 6px;
  }

  ::ng-deep .editRow .p-dropdown {
    border: 1px solid #ced4da !important;
    border-radius: 6px;
  }

  ::ng-deep .p-inputtext {
    width: 100%;
    border: 0;
  }

  ::ng-deep .p-inputtext:enabled:focus {
    box-shadow: none !important;
  }

  ::ng-deep .p-dropdown-label {
    padding-right: 0rem !important;
    line-height: 1;
  }

  .no-wrap {
    text-wrap: nowrap;
  }

  .icon-background {
    background-color: #4b3f72 !important;
    padding: 0.2rem;
  }

  ::ng-deep small {
    position: absolute;
  }

  ::ng-deep .p-datatable-thead {
    position: sticky;
    top: 0;
    z-index: 1;
  }

  ::ng-deep .p-datatable .p-datatable-thead > tr > th {
    color: #4b3f72;
    font-family: Poppins;
    font-size: 12px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 21px;
    border-bottom: none;
    background-color: #f8f8ff;
    padding: 0.5rem 0.5rem;
  }

  .background-white {
    background-color: #ffffff !important;
  }

  ::ng-deep .p-datatable .p-datatable-tbody > tr > td {
    height: 20px;
    color: #000000;
    max-width: 123px;
    font-family: Poppins;
    font-size: 12px;
    letter-spacing: 0;
    line-height: 15px;
    padding: 0rem 0.5rem;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  ::ng-deep .pi-icon .pi {
    font-size: 0.5rem;
  }

  ::ng-deep .svg-icon-white .svg-icon svg g [fill] {
    fill: white !important;
  }

  ::ng-deep .p-datatable-tbody tr:last-child > td {
    border-bottom: none;
  }

  .p-dropdown-label.p-inputtext {
    display: flex;
    align-items: center;
    padding-left: 15px;
    font-size: 14px;
  }

  .p-dropdown-trigger-icon {
    padding-right: 20px;
  }

  ::ng-deep .p-inputtext:enabled:focus,
  ::ng-deep .p-inputtext:enabled:hover {
    border: 1px solid #119da4;
    border-radius: 6px;
    background-color: #ffffff;
    box-shadow: 0 0 2px 1px rgba(17, 157, 164, 0.2);
  }

  .center-align {
    text-align: center !important;
  }

  ::ng-deep .p-datatable .p-datatable-loading-overlay {
    top: 28px;
    background-color: transparent;
  }

  ::ng-deep {
    .skill-set .p-dropdown-label,
    .employee-dropdown .p-dropdown-label,
    .purchase-order .p-dropdown-label {
      margin-right: 10px;
    }
  }

  ::ng-deep {
    .skill-set .p-dropdown-clear-icon,
    .employee-dropdown .p-dropdown-clear-icon,
    .purchase-order .p-dropdown-clear-icon {
      height: 15px;
      display: flex;
      align-items: center;
    }
  }

  ::ng-deep .p-calendar {
    width: 100% !important;
    display: flex;
    margin-right: 0.2rem;
  }

  .date-filter-group {
    height: 30px !important;
    min-width: 120px;
  }

  ::ng-deep .date-filter-cal .p-inputtext {
    height: 28px !important;
    border: none !important;
  }

  ::ng-deep .date-filter-cal .p-inputtext::placeholder {
    padding-left: 5px;
  }

  ::ng-deep .p-calendar .p-inputtext {
    height: 28px !important;
    width: 120px !important;
    border: 1px solid #b5b5c3;
    border-radius: 6px;
    background-color: #ffffff;
  }

  ::ng-deep .p-calendar .p-inputtext:enabled:focus,
  ::ng-deep .p-calendar .p-inputtext:enabled:hover {
    border: 1px solid #119da4;
    border-radius: 6px;
    background-color: #ffffff;
    box-shadow: 0 0 2px 1px rgba(17, 157, 164, 0.2);
  }

  ::ng-deep .edit-cal .p-calendar .p-inputtext {
    border: 1px solid #ced4da !important;
    border-radius: 6px !important;
  }

  ::ng-deep .p-calendar .p-datepicker {
    position: absolute;
    width: 400px;
  }

  ::ng-deep .calender-month .p-calendar .p-datepicker {
    position: absolute;
    width: 100%;
  }

  .filter-listing {
    position: absolute;
    right: 0;
    z-index: 10;
    box-sizing: border-box;
    height: 300px;
    width: 50%;
    border: 1px solid #efeff7;
    border-radius: 4px;
    background-color: #ffffff;
    box-shadow: 0 12px 30px 0 rgba(215, 215, 230, 0.4);
    overflow-y: auto;
    padding: 15px 0;
  }

  ::ng-deep .filter-listing::-webkit-scrollbar {
    width: 0.5em !important;
  }

  ::ng-deep .filter-listing::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3) !important;
  }

  ::ng-deep .filter-listing::-webkit-scrollbar-thumb {
    background-color: darkgray !important;
    outline: 1px solid slategray !important;
  }

  .btn-plus {
    color: #ffffff !important;
    background-color: #4b3f72 !important;
    border-color: #4b3f72 !important;
  }

  ::ng-deep .svg-icon-white .svg-icon svg g [fill] {
    fill: white !important;
  }

  ::ng-deep .kt-wizard-v4 .kt-wizard-v4__wrapper .kt-form {
    width: 80% !important;
  }

  ::ng-deep .form-control-custom {
    height: 30px !important;
    width: 100% !important;
    border: 1px solid #b5b5c3;
    border-radius: 6px;
    background-color: #ffffff;
  }

  .add-new {
    text-decoration: underline;
  }

  .btn-prev {
    border-color: #4b3f72 !important;
    background-color: #ffffff !important;
    height: 40px;
  }

  .btn-prev:hover {
    background-color: #574985 !important;
    border-color: #574985 !important;
    color: #ffffff !important;
  }

  ::ng-deep .confirm-dialog .p-dialog {
    width: 30vw;
  }

  ::ng-deep .expense-dialog .p-dialog {
    width: 60vw;
    margin-top: 4rem;
    height: 80%;
  }

  ::ng-deep .expense-dialog .p-dialog-content {
    height: 85%;
  }

  .show-pointer {
    cursor: pointer;
    color: #4b3f72 !important;
    text-decoration: underline;
  }

  .pointer {
    cursor: pointer;
  }

  .disabled-edit {
    cursor: not-allowed;
    opacity: 0.6;
    color: #6c757d !important;
  }

  ::ng-deep .p-inputgroup-addon {
    background-color: #f8f8ff !important;
    border-radius: 4px;
    border-color: #b5b5c3;
    border-right: none !important;
    border-bottom-right-radius: 0px !important;
    border-top-right-radius: 0px !important;
  }

  ::ng-deep .position-abs small {
    position: absolute;
  }

  ::ng-deep .position-rel small {
    position: absolute;
  }

  ::ng-deep .p-calendar .p-inputtext {
    width: 50% !important;
  }

  ::ng-deep .position-abs .p-calendar .p-inputtext {
    border: 1px solid #ced4da !important;
    border-radius: 6px !important;
  }
  ::ng-deep .p-dropdown .p-dropdown-clear-icon {
    color: #6c757d;
    right: 1.2rem;
  }

  .help-icon {
    color: #b5b5c3;
    cursor: pointer;
  }
  .heading {
    height: 15px;
    width: 51px;
    color: #9b9b9b;
    font-family: Poppins;
    font-size: 10px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 16px;
  }

  .sub-heading {
    height: 17px;
    color: #242424;
    font-family: Poppins;
    font-size: 12px;
    letter-spacing: 0;
    line-height: 18px;
  }

  .bottom-border {
    border-bottom: 1px solid #e9ecef;
  }

  ::ng-deep .p-datatable-scrollable-body {
    margin-top: 1rem;
    overflow-y: auto !important;
  }

  .btn-footer {
    bottom: 0;
    position: absolute;
    width: 98%;
  }

  .scrollable-content {
    max-height: calc((var(--fixed-content-height, 1vh) * 100) - 100px) !important;
    overflow: hidden !important;
  }
  // .custom-width{
  //   width:60% !important;
  // }

  .btn-fix {
    position: fixed !important;
    top: calc(100vh - 105px) !important;
    width: calc(100vw - 294px) !important;
    margin-top: 15px;
  }
  .position-relative {
    position: relative;
  }

  .billRate {
    .body-width-plus {
      width: 4%;
    }
    .body-cost {
      width: 7.9%;
    }
    .body-expense {
      width: 6.7%;
    }
    .body-allocation {
      width: 6.6%;
    }
    .body-start {
      width: 8.8%;
    }
    .body-end {
      width: 8.5%;
    }
    .body-rate {
      width: 8.8%;
    }
    .body-name {
      width: 9.5%;
    }
    .body-margin {
      width: 7.5%;
    }

    .body-person {
      width: 13.6%;
    }
    .body-skill {
      width: 7.8%;
    }
    .header-skill {
      width: 5%;
    }
    .header-start {
      width: 8%;
    }
    .header-allocation {
      width: 6%;
    }
    .header-cost {
      width: 9%;
    }
  }
  .btn-height {
    height: 40px;
    line-height: 1.2;
  }

  .dots-sym {
    color: #4b3f72;
    font-weight: 600;
    cursor: pointer;
  }

  .header-width-plus {
    .position-label {
      display: flex;
      align-items: center;

      .position-text {
        font-weight: 600;
        font-size: 16px;
        color: #181c32;
      }
    }

    .add-new-position {
      .add-new-btn {
        height: 40px !important;
      }
    }

    .edit-date-btn {
      margin-right: 8px;
      border-right: 3px solid #ecedf5;
    }
  }

  ::ng-deep .p-input-icon-right > em:last-of-type {
    color: #8a9191;
    font-size: 0.5rem;
    right: 0.5rem;
    position: absolute;
    top: 50%;
    margin-top: -0.5rem;
  }

  ::ng-deep .p-input-icon-right {
    border: 1px solid #b5b5c3;
    border-radius: 4px;
    background-color: white;
  }

  ::ng-deep .date-filter-group:hover .p-input-icon-right,
  .date-filter-group:hover .p-inputgroup-cancel,
  .date-filter-group:hover .p-inputgroup-addon {
    border: 1px solid #119da4;
    border-radius: 4px;
    background-color: #ffffff;
  }

  ::ng-deep .cal-drop.p-element.p-dropdown {
    width: 100%;
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
  }

  ::ng-deep .p-inputgroup-cancel {
    background-color: #f8f8ff !important;
    border: 1px solid #b5b5c3;
    border-radius: 4px;
    border-left: none !important;
    border-radius: 0 4px 4px 0 !important;
    display: flex;
    align-items: center;

    .pi-times {
      font-size: 8px;
      padding-right: 3px;
      cursor: pointer;
    }
  }

  @media (max-width: 991px) {
    ::ng-deep .p-datatable table {
      display: block;
      overflow: auto;
      white-space: nowrap;
    }
    .btn-fix {
      width: 100vw !important;
    }
  }

  @media (max-width: 500px) {
    .btn-fix {
      position: initial !important;
      margin-top: 0;
    }
    ::ng-deep .p-datatable-wrapper {
      height: calc((var(--fixed-content-height, 1vh) * 100) - 230px) !important;
    }

    .py-5 {
      padding: 5px 20px !important;
    }
  }

  #addNewPosition {
    display: flex;
  }

  /**
 * ==============================================
 * Dot Pulse
 * ==============================================
 */
  .pulse-container {
    display: flex;
    justify-content: start;
    align-items: center;
  }
  .dot-pulse {
    position: relative;
    left: -9985px;
    width: 10px;
    height: 10px;
    border-radius: 5px;
    background-color: #4b3f72;
    color: #4b3f72;
    box-shadow: 9999px 0 0 -5px #4b3f72;
    animation: dotPulse 1.5s infinite linear;
    animation-delay: 0.25s;
  }

  .dot-pulse::before,
  .dot-pulse::after {
    content: '';
    display: inline-block;
    position: absolute;
    top: 0;
    width: 10px;
    height: 10px;
    border-radius: 5px;
    background-color: #4b3f72;
    color: #4b3f72;
  }

  .dot-pulse::before {
    box-shadow: 9984px 0 0 -5px #4b3f72;
    animation: dotPulseBefore 1.5s infinite linear;
    animation-delay: 0s;
  }

  .dot-pulse::after {
    box-shadow: 10014px 0 0 -5px #4b3f72;
    animation: dotPulseAfter 1.5s infinite linear;
    animation-delay: 0.5s;
  }

  @keyframes dotPulseBefore {
    0% {
      box-shadow: 9984px 0 0 -5px #4b3f72;
    }
    30% {
      box-shadow: 9984px 0 0 2px #4b3f72;
    }
    60%,
    100% {
      box-shadow: 9984px 0 0 -5px #4b3f72;
    }
  }

  @keyframes dotPulse {
    0% {
      box-shadow: 9999px 0 0 -5px #4b3f72;
    }
    30% {
      box-shadow: 9999px 0 0 2px #4b3f72;
    }
    60%,
    100% {
      box-shadow: 9999px 0 0 -5px #4b3f72;
    }
  }

  @keyframes dotPulseAfter {
    0% {
      box-shadow: 10014px 0 0 -5px #4b3f72;
    }
    30% {
      box-shadow: 10014px 0 0 2px #4b3f72;
    }
    60%,
    100% {
      box-shadow: 10014px 0 0 -5px #4b3f72;
    }
  }

  ::ng-deep .editing-row td {
    padding: 2.5rem 1rem !important;
    animation: fadeInAnimation ease 1.5s;
    animation-iteration-count: 1;
    animation-fill-mode: forwards;
  }

  ::ng-deep .editing-row {
    box-shadow: inset 0px 0px 5px 0px #4b3f72 !important;
  }

  @keyframes fadeInAnimation {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }

  ::ng-deep .editing-row td {
    padding: 2.5rem 1rem !important;
    animation: fadeInAnimation ease 1.5s;
    animation-iteration-count: 1;
    animation-fill-mode: forwards;
  }

  ::ng-deep .editing-row {
    box-shadow: inset 0px 0px 5px 0px #4b3f72 !important;
  }

  @keyframes fadeInAnimation {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }

  .checkbox-wrapper {
    white-space: nowrap;
    width: 3%;
    max-width: 42px !important;
  }
  ::ng-deep .popup-column {
    z-index: 100 !important;
    position: absolute !important;
    right: 5px !important;
    top: 50px !important;
  }

  .update-Date {
    height: 40px !important;
    width: 40px !important;
  }
}
.date-calender {
  .position-date {
    ::ng-deep {
      &.p-calendar {
        min-width: 75%;
      }
    }

    p {
      font-size: 14px;
      font-weight: 500;
    }
  }
}

::ng-deep .cost-dialog-warning {
  font-size: 1.5rem !important;
  margin-right: 0.5rem !important;
}

.date-filter {
  border-left: none !important;
  border-right: none !important;
  border-radius: 0 !important;
}

::ng-deep .po-dialog {
  width: 100%;
  height: 100%;
  border-radius: 9px !important;
  border: none !important;
  background-color: #f8f8ff !important;
  margin-top: 1rem;

  .p-dropdown-clear-icon {
    height: 15px;
    display: flex;
    align-items: center;
  }

  .p-dropdown-label {
    margin-right: 10px;
  }
}

.month-section {
  max-width: 400px !important;
  min-width: 400px !important;
}

.month-calendar-wrapper {
  width: 100%;
  max-width: 400px !important;

  .calendar-day-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;

    .calendar-day {
      width: 24px;
      height: 24px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 50%;

      &.selected-month {
        background-color: $primary;
        color: $primary-inverse;
      }
    }
  }

  .selected-months-footer {
    padding: 8px;
    border-top: 1px solid $calendar-border-color;

    .selected-months-label {
      font-weight: 500;
      margin-bottom: 8px;
      color: $primary;
    }

    .selected-months-chips {
      display: flex;
      flex-wrap: wrap;
      gap: 5px;
    }
  }

  .selected-months-display {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-top: 8px;
    border: 2px solid $primary;
    padding: 5px;
    min-height: 40px;
    height: auto !important;
  }
}

::ng-deep .month-chip.p-chip {
  background-color: $primary-light;
  color: $primary;
  font-weight: 500;

  .p-chip-remove-icon {
    color: $primary;
  }

  &:hover {
    background-color: $chip-hover-bg;
  }
}

#custom-month-edit > p-calendar {
  ::ng-deep input {
    visibility: hidden !important;
  }
}

::ng-deep .selected-month-yellow {
  background-color: $primary !important;
  color: $primary-inverse !important;
  font-weight: bold;
  border-radius: 4px;
  outline: 1px solid transparent;
  padding: 1px;
}

::ng-deep .disabled-month {
  pointer-events: none;
  background-color: #d3d3d3 !important;
  opacity: 0.4;
  padding: 0.7rem;
  border: 1px solid;
}

#custom-month-edit {
  .custom-month-selection {
    position: relative;
  }
}

.fix-calender-pos {
  right: 0px;
  position: absolute;
  top: 0px;
}

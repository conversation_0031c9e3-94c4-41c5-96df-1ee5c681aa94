#common-table--wrapper {
  ::ng-deep .p-datatable .p-datatable-thead > tr > th {
    height: 49px;
    color: #4b3f72;
    font-family: Poppins;
    font-size: 14px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 21px;
    border-bottom: none;
    padding: 0.5rem 0.5rem;
    background-color: #ecedf6 !important;
  }

  ::ng-deep .p-multiselect {
    border: 1px solid #4b3f72 !important;
  }

  ::ng-deep .p-datatable .p-datatable-tbody > tr > td {
    height: 48px;
    color: #000000;
    font-family: Poppins;
    font-size: 12px;
    letter-spacing: 0;
    line-height: 15px;
    padding: 0.5rem 0.5rem;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .not-macthed-with-OG {
    color: #ea3b52 !important;
    cursor: pointer;
  }

  .utilization-progress--wrapper {
    width: 75px;
    background-color: #d9d9d9;
    .actual-utilization-progress {
      width: 40px;
      background: green;
    }
  }

  ::ng-deep .p-datatable-wrapper {
    .p-datatable-thead {
      .p-inputtext {
        width: 100%;
        border: 1px solid #4b3f72;
      }
    }
  }

  ::ng-deep .p-paginator {
    display: flex !important;
    justify-content: flex-start !important;
    border: none;
    margin-top: 15px;

    & .p-paginator-pages {
      & .p-paginator-page:not(.p-highlight):hover {
        border-radius: 50%;
      }

      & .p-paginator-page.p-highlight {
        background: #4b3f72;
        border-color: #e3f2fd;
        color: #ffffff;
        border-radius: 50%;
        pointer-events: none;
      }
    }

    & .p-paginator-first,
    & .p-paginator-prev,
    & .p-paginator-next,
    & .p-paginator-last {
      &:not(.p-disabled):not(.p-highlight):hover {
        background: #e9ecef;
        border-color: transparent;
        color: #495057;
        border-radius: 50%;
      }

      &:not(.p-disabled):not(.p-highlight) {
        border-radius: 50%;
        background-color: #f4f5f8;
      }
    }

    & .p-paginator-rpp-options {
      margin-left: 20px;
      height: 37px;
      border-radius: 20px;
      background-color: #f4f5f8;
      width: 100%;
      border: 1px solid #4b3f72 !important;
    }

    & .p-dropdown-label.p-inputtext {
      display: flex;
      align-items: center;
      padding-left: 15px;
      font-size: 14px;
    }

    & .p-dropdown-trigger-icon {
      padding-right: 20px;
    }

    & .p-link:focus {
      box-shadow: none !important;
    }
    ::ng-deep .p-dropdown:not(.p-disabled).p-focus {
      box-shadow: none;
    }
  }
}

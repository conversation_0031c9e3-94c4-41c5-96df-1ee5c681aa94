[{"id": 1, "name": "Zoolab", "start_date": "9/27/2020", "end_date": "4/20/2020", "employee_name": "<PERSON><PERSON><PERSON>", "type": "eepurl.com", "status": "Lotlux"}, {"id": 2, "name": "Keylex", "start_date": "4/23/2020", "end_date": "12/30/2019", "employee_name": "<PERSON>", "type": "acquirethisname.com", "status": "Cardify"}, {"id": 3, "name": "Regrant", "start_date": "9/24/2020", "end_date": "2/3/2020", "employee_name": "<PERSON><PERSON><PERSON>", "type": "opera.com", "status": "Trippledex"}, {"id": 4, "name": "Domainer", "start_date": "7/29/2020", "end_date": "1/4/2020", "employee_name": "<PERSON><PERSON>", "type": "rediff.com", "status": "Aerified"}, {"id": 5, "name": "Vagram", "start_date": "10/20/2020", "end_date": "9/8/2020", "employee_name": "<PERSON>", "type": "com.com", "status": "<PERSON>"}, {"id": 6, "name": "Lotlux", "start_date": "12/14/2020", "end_date": "12/5/2020", "employee_name": "<PERSON><PERSON>", "type": "freewebs.com", "status": "Hatity"}, {"id": 7, "name": "Voyatouch", "start_date": "3/31/2020", "end_date": "10/22/2020", "employee_name": "<PERSON><PERSON>", "type": "cisco.com", "status": "Andalax"}, {"id": 8, "name": "<PERSON><PERSON><PERSON>", "start_date": "2/11/2020", "end_date": "2/13/2020", "employee_name": "<PERSON><PERSON>", "type": "wordpress.org", "status": "Stronghold"}, {"id": 9, "name": "Ka<PERSON><PERSON>", "start_date": "6/27/2020", "end_date": "6/24/2020", "employee_name": "<PERSON>", "type": "digg.com", "status": "Konklab"}, {"id": 10, "name": "Lotstring", "start_date": "9/8/2020", "end_date": "8/23/2020", "employee_name": "Cross Barbara", "type": "yale.edu", "status": "Keylex"}, {"id": 11, "name": "<PERSON><PERSON><PERSON>", "start_date": "5/31/2020", "end_date": "9/4/2020", "employee_name": "<PERSON><PERSON><PERSON>", "type": "canalblog.com", "status": "Sonsing"}, {"id": 12, "name": "Flowdesk", "start_date": "12/11/2020", "end_date": "7/13/2020", "employee_name": "<PERSON><PERSON>", "type": "usnews.com", "status": "Cardguard"}, {"id": 13, "name": "Bamity", "start_date": "1/17/2020", "end_date": "6/11/2020", "employee_name": "Salem Silversmidt", "type": "huffingtonpost.com", "status": "<PERSON><PERSON>"}, {"id": 14, "name": "Y-find", "start_date": "8/9/2020", "end_date": "12/28/2019", "employee_name": "<PERSON>", "type": "sina.com.cn", "status": "Tempsoft"}, {"id": 15, "name": "Temp", "start_date": "11/13/2020", "end_date": "1/12/2020", "employee_name": "<PERSON><PERSON>", "type": "meetup.com", "status": "<PERSON><PERSON>"}, {"id": 16, "name": "Trippledex", "start_date": "6/12/2020", "end_date": "11/8/2020", "employee_name": "<PERSON><PERSON>", "type": "cdbaby.com", "status": "Matsoft"}, {"id": 17, "name": "<PERSON><PERSON>", "start_date": "10/4/2020", "end_date": "4/14/2020", "employee_name": "<PERSON><PERSON><PERSON><PERSON>", "type": "homestead.com", "status": "Quo Lux"}, {"id": 18, "name": "Regrant", "start_date": "11/17/2020", "end_date": "1/17/2020", "employee_name": "<PERSON><PERSON>", "type": "deviantart.com", "status": "Sub-Ex"}, {"id": 19, "name": "Sonsing", "start_date": "9/8/2020", "end_date": "6/16/2020", "employee_name": "<PERSON><PERSON><PERSON>", "type": "cam.ac.uk", "status": "Viva"}, {"id": 20, "name": "Tin", "start_date": "7/9/2020", "end_date": "9/27/2020", "employee_name": "Virginia Bisacre", "type": "japanpost.jp", "status": "Matsoft"}]
import { DatePipe } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { EmployeeIds, IFilter, QueryFilterParams, WorkException } from '@entities/administration/administration.model';
import { AdministrationService } from '@entities/administration/administration.service';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { Debounce } from '@shared/decorators/debounce.decorator';
import { ButtonParams } from '@shared/models';
import { AlertType } from '@shared/models/alert-type.enum';
import { GetSetCacheFiltersService } from '@shared/services/get-set-cache-filters.service';
import { LayoutUtilsService } from '@shared/services/layout-utils.service';
import { MessageService, TableState } from 'primeng/api';
import { Table } from 'primeng/table';
@Component({
  selector: 'app-manage-work-exceptions',
  templateUrl: './manage-work-exceptions.component.html',
  styleUrls: ['./manage-work-exceptions.component.scss'],
  providers: [MessageService, DatePipe],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ManageWorkExceptionsComponent extends SflBaseComponent implements OnInit {
  workExceptionsData = [];
  activeEmployeeIds: number[] = [];
  totalRecords: number;
  loading = true;
  cardTitle = 'Manage Work Exceptions';
  cardSubTitle = null;
  workexception: WorkException;
  buttons: ButtonParams[] = [
    {
      btnClass: 'btn-save',
      btnText: 'Add New',
      redirectPath: this.appRoutes.CREATE_WORK_EXCEPTION,
      permissions: [this.permissionModules.MANAGE_WORK_EXCEPTION]
    }
  ];

  dataFilter: IFilter = new IFilter();
  exceptionTypes = [];
  @ViewChild('dt') table: Table;
  deleteExceptionId: number;
  showDeleteDialog = false;
  employeeExist: boolean;
  filteredFlag = false;
  pageChangeFlag = false;
  sortColumnFlag = false;
  sortFieldName: string;
  sortOrderNumber: number;
  descendingSortNumber = -1;
  dateDefaultOrder = 'desc:date';
  date = 'date';
  constructor(
    private readonly adminService: AdministrationService,
    private readonly cdf: ChangeDetectorRef,
    private readonly layoutUtilsService: LayoutUtilsService,
    private readonly datePipe: DatePipe,
    private readonly cacheFilter: GetSetCacheFiltersService
  ) {
    super();
  }

  ngOnInit(): void {
    this.getExceptionTypes();
  }

  // used to apply filter on the table
  filter() {
    if (!this.dataFilter.type_name) {
      this.dataFilter.type_name = null;
    }
    this.filteredFlag = true;
    this.loading = true;
    this.workExceptionsData = [];
    this.getManageWorkExceptions(this.table);
  }

  getExceptionTypes() {
    this.loading$.next(true);
    this.subscriptionManager.add(
      this.adminService.getExceptionTypes().subscribe(
        (res) => {
          if (res?.data?.work_exception_types) {
            this.exceptionTypes.push({ label: '-- All --', value: '' });
            const types = res?.data?.work_exception_types;
            types.forEach((type) => {
              this.exceptionTypes.push({
                label: type?.work_exception_type?.name,
                value: type?.work_exception_type?.name
              });
            });
          }
          if (this.cacheFilter.getCacheFilters('work-exception')) {
            this.dataFilter = this.cacheFilter.getCacheFilters('work-exception');
            if (this.dataFilter.date) {
              this.dataFilter.date = new Date(this.dataFilter.date);
            }
          }
          this.loading$.next(false);
        },
        () => this.loading$.next(false)
      )
    );
  }

  clearFilter(key) {
    delete this.dataFilter[key];
    this.filter();
  }

  @Debounce()
  async getManageWorkExceptions(event?: TableState) {
    // we will be passing list of employee ids along with other query param, as default the get api of work exception returns duplicate items.
    // if there is any change in employee details it is triggering the work exception for that updated record as well and due to that we are receiving duplicate rows.
    // to remove those we will send list of employee ids (active) and that way we will get rid of those duplicate entries.
    if (!this.activeEmployeeIds.length) {
      await this.getActiveEmployeesIds();
    }
    let queryFilter: QueryFilterParams = {
      limit: !this.pageChangeFlag ? (this.dataFilter?.limit ? this.dataFilter.limit : event?.rows) : event?.rows,
      offset: !this.pageChangeFlag ? (this.dataFilter?.offset ? this.dataFilter.offset : event?.first) : event?.first,
      order_by: !this.sortColumnFlag ? (this.dataFilter?.order_by ? this.dataFilter.order_by : this.activeSort(event)) : this.activeSort(event),
      employee_ids: this.activeEmployeeIds.join(',')
    };
    if (!this.pageChangeFlag) {
      this.table._first = this.dataFilter?.offset ? this.dataFilter.offset : event?.first;
      this.table._rows = this.dataFilter?.limit ? this.dataFilter.limit : event?.rows;
    }
    if (!this.sortColumnFlag) {
      this.sortFieldName = this.dataFilter?.order_by ? this.dataFilter.order_by.split(':')[1] : event?.sortField;
      this.sortOrderNumber = this.dataFilter?.order_by ? (this.dataFilter.order_by.split(':')[0] === 'asc' ? 1 : -1) : event?.sortOrder;
    }

    if (!queryFilter?.order_by && !this.sortFieldName && !this.dataFilter?.order_by) {
      queryFilter.order_by = this.dateDefaultOrder;
      this.sortFieldName = this.date;
      this.dataFilter.order_by = this.dateDefaultOrder;
      this.sortOrderNumber = this.descendingSortNumber;
    }

    if (this.dataFilter) {
      this.cacheFilter.setCacheFilters({ ...this.dataFilter, ...queryFilter }, 'work-exception');
      if (this.filteredFlag) {
        queryFilter.offset = 0;
        event.first = 0;
        this.filteredFlag = false;
      }
      for (const [key] of Object.entries(this.dataFilter)) {
        if (key === 'employee_first_name' || key === 'description') {
          queryFilter[`${key}${'_search'}`] = this.dataFilter[key];
        } else if (key === 'date') {
          if (this.dataFilter[key]) {
            queryFilter[`${key}`] = this.datePipe.transform(new Date(this.dataFilter[key]), 'yyyy-MM-dd');
          } else {
            delete queryFilter[`${key}`];
          }
        } else {
          if (key !== 'offset' && key !== 'limit' && key !== 'order_by') queryFilter[`${key}`] = this.dataFilter[key];
        }
      }
    }
    delete queryFilter['employee_inactive'];
    delete queryFilter['employee_active'];
    queryFilter = this.queryStringUtil(queryFilter);
    // this.subscriptionManager.add(this.adminService.getActiveEmployees().subscribe(res => {
    // queryFilter.employee_ids = res?.employee_ids?.join(',')
    this.subscriptionManager.add(
      this.adminService.getManageWorkExceptions(queryFilter).subscribe(
        (res) => {
          this.workExceptionsData = res?.body?.data?.work_exceptions;
          this.totalRecords = Number(res?.headers.get('x-total-count'));
          this.loading = false;
          this.cdf.detectChanges();
        },
        () => (this.loading = false)
      )
    );
    // }, () => this.loading = false))
  }

  getActiveEmployeesIds(): Promise<void> {
    return new Promise((resolve) => {
      this.subscriptionManager.add(
        this.adminService.getEmployeeIds().subscribe((res) => {
          this.activeEmployeeIds = res.employee_ids;
          resolve();
        })
      );
    });
  }

  clearDate() {
    delete this.dataFilter.date;
    this.filter();
  }

  activeSort(event: TableState) {
    if (event.sortField) {
      if (event.sortOrder === 1) {
        return 'asc:' + event.sortField;
      } else {
        return 'desc:' + event.sortField;
      }
    }
    return null;
  }

  // used to take query string param and removes values which are not set in the query string
  queryStringUtil(queryStringParam: QueryFilterParams) {
    const queryFilter: QueryFilterParams = {};
    if (queryStringParam) {
      for (const [key] of Object.entries(queryStringParam)) {
        if (queryStringParam[key] === '' || queryStringParam[key] === null || queryStringParam[key] === undefined) {
          delete queryStringParam[key];
        }
      }
    }
    return queryStringParam;
  }

  confirmDeleteException(id: number, flag: boolean) {
    this.deleteExceptionId = id;
    this.showDeleteDialog = true;
    this.employeeExist = flag;
  }

  closeModal() {
    this.deleteExceptionId = null;
    this.showDeleteDialog = false;
    this.employeeExist = null;
  }

  deleteWorkException() {
    this.isSubmitting = true;
    const exceptionData = this.workExceptionsData;
    const id = this.deleteExceptionId;
    const serviceName = this.employeeExist ? 'deleteWorkException' : 'deleteHolidayWorkException';
    this.subscriptionManager.add(
      this.adminService[serviceName](id).subscribe(
        () => {
          this.isSubmitting = false;
          this.closeModal();
          this.workExceptionsData = exceptionData.filter((p) => p.work_exception.id !== id);
          this.totalRecords = this.totalRecords - 1;
          this.cdf.detectChanges();
          this.layoutUtilsService.showActionNotification('Work Exception has been archived successsfully', AlertType.Success);
        },
        () => (this.isSubmitting = true)
      )
    );
  }

  resetFilters() {
    this.dataFilter = new IFilter();
    this.cacheFilter.resetCacheFilters('work-exception');
    this.filter();
  }

  pageChange() {
    this.pageChangeFlag = true;
    this.loading = true;
    this.workExceptionsData = [];
  }
  sortColumn() {
    this.sortColumnFlag = true;
  }
}

<div class="card card-custom gutter-b" id="createUser">
  <app-card-header [cardTitle]="cardTitle" [cardSubTitle]="cardSubTitle" [buttons]="buttons"></app-card-header>
  <div class="card-body create-card scrollable-content" *isFetchingData="loading$">
    <form
      class="form"
      autocomplete="off"
      [formGroup]="createUserForm"
      autocomplete="off"
      novalidate="novalidate"
      id="create_client_form"
      (ngSubmit)="createUserForm.valid && onSave()"
    >
      <div id="client-detail">
        <kt-auth-notice></kt-auth-notice>
        <div class="row">
          <div class="col-sm-4 col-12 pr-md-5">
            <div class="form-group">
              <label class="form-label">First Name</label>
              <input type="text" class="form-control custom" placeholder="John" required formControlName="firstName" />
              <app-form-error [validation]="'required'" [form]="createUserForm" [controlName]="'firstName'" [fieldLabel]="'First Name'"></app-form-error>
              <app-form-error [validation]="'minlength'" [length]="2" [form]="createUserForm" [controlName]="'firstName'" [fieldLabel]="'First Name'"></app-form-error>
            </div>
          </div>
          <div class="col-sm-4 col-12 pr-md-5">
            <div class="form-group">
              <label class="form-label">Last Name</label>
              <input type="text" class="form-control custom" placeholder="Doe" required formControlName="lastName" />
              <app-form-error [validation]="'required'" [form]="createUserForm" [controlName]="'lastName'" [fieldLabel]="'Last Name'"></app-form-error>
              <app-form-error [validation]="'minlength'" [length]="2" [form]="createUserForm" [controlName]="'lastName'" [fieldLabel]="'Last Name'"></app-form-error>
            </div>
          </div>
          <div class="col-sm-4 col-12 pr-md-5">
            <div class="form-group">
              <label class="form-label">Email</label>
              <input type="text" class="form-control custom" placeholder="<EMAIL>" required formControlName="email" email />
              <app-form-error [validation]="'required'" [form]="createUserForm" [controlName]="'email'" [fieldLabel]="'Email'"></app-form-error>
              <app-form-error [validation]="'email'" [form]="createUserForm" [controlName]="'email'" [fieldLabel]="'Email'"></app-form-error>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-sm-4 col-12 pr-md-5">
            <div class="form-group">
              <label class="form-label">Status: {{ createUserForm?.get('active')?.value ? 'Active' : 'In Active' }}</label>
              <span class="status-toggle d-flex">Inactive <mat-slide-toggle class="mx-3" color="warn" formControlName="active"></mat-slide-toggle>Active</span>
              <app-form-error [validation]="'required'" [form]="createUserForm" [controlName]="'active'" [fieldLabel]="'Status'"></app-form-error>
            </div>
          </div>
          <div class="col-sm-4 col-12 pr-md-5">
            <div class="form-group">
              <label class="form-label">Role</label>
              <p-dropdown class="form-control custom" [options]="userRoles" optionLabel="name" optionValue="name" placeholder="Select" formControlName="role" required></p-dropdown>
              <app-form-error [validation]="'required'" [form]="createUserForm" [controlName]="'role'" [fieldLabel]="'Role'"></app-form-error>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>

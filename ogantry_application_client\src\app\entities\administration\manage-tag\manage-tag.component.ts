import { Component, OnInit } from '@angular/core';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { ButtonParams } from '@shared/models';
import { GlobalDetailSubCategory, GlobalDetailTaggingCategory, GlobalDetailTags, SubCategory, Tag, TagCategory } from '../administration.model';
import { AdministrationService } from '../administration.service';

@Component({
  selector: 'app-manage-tag',
  templateUrl: './manage-tag.component.html',
  styleUrls: ['./manage-tag.component.scss']
})
export class ManageTagComponent extends SflBaseComponent implements OnInit {
  tags: Tag[] = [];
  loading = false;
  cardTitle = 'Manage Tag';
  cardSubTitle = null;
  buttons: ButtonParams[] = [
    {
      btnClass: 'btn-save',
      btnText: 'Add New',
      redirectPath: this.appRoutes.CREATE_TAG,
      permissions: [this.permissionModules.MANAGE_TAG_CATEGORY]
    }
  ];
  deleteTagId: string;
  showDeleteDialog = false;
  globalDetailsTag: GlobalDetailTags;
  globalDetailId: number;
  globalDetailSubCategoryId: number;
  globalDetailsTaggingCategory: GlobalDetailTaggingCategory;
  globalDetailsTaggingSubCategory: GlobalDetailSubCategory;
  tagCategory: TagCategory[] = [];
  tagSubCategory: SubCategory[] = [];

  constructor(private readonly adminService: AdministrationService) {
    super();
  }

  ngOnInit(): void {
    this.getGlobalDetailsCategory();
    this.getGlobalDetailsSubCategory();
  }

  getGlobalDetailsCategory() {
    this.subscriptionManager.add(
      this.adminService.getTagCategories('TagCategoryManagement').subscribe(
        (res) => {
          this.loading = false;
          if (res?.data?.global_details) {
            const globalDetail = res?.data?.global_details;
            if (globalDetail && globalDetail[0]?.global_detail?.name === 'TagCategoryManagement') {
              this.globalDetailsTaggingCategory = globalDetail[0];
              this.globalDetailId = globalDetail[0].global_detail.id;
              this.tagCategory = globalDetail[0].global_detail.extended_fields.tagCategory;
            }
          }
        },
        () => (this.loading = false)
      )
    );
  }

  getGlobalDetailsSubCategory() {
    this.subscriptionManager.add(
      this.adminService.getTagSubCategories('SubCategoryManagement').subscribe(
        (res) => {
          this.loading = false;
          if (res?.data?.global_details) {
            const globalDetail = res?.data?.global_details;
            if (globalDetail && globalDetail[0]?.global_detail?.name === 'SubCategoryManagement') {
              this.globalDetailsTaggingSubCategory = globalDetail[0];
              this.globalDetailSubCategoryId = globalDetail[0].global_detail.id;
              this.tagSubCategory = globalDetail[0].global_detail.extended_fields.subCategory;
            }
          }
        },
        () => (this.loading = false)
      )
    );
  }

  getGlobalDetailTags() {
    this.tags = [];
    this.loading = true;
    this.subscriptionManager.add(
      this.adminService.getTags('TagManagement').subscribe(
        (res) => {
          this.loading = false;
          if (res?.data?.global_details) {
            const globalDetail = res?.data?.global_details;
            if (globalDetail && globalDetail[0]?.global_detail?.name === 'TagManagement') {
              this.globalDetailsTag = globalDetail[0];
              this.globalDetailId = globalDetail[0].global_detail.id;
              this.tags = globalDetail[0].global_detail.extended_fields.tags;
              this.adminService.setTags(globalDetail[0].global_detail);
            }
          }
        },
        () => (this.loading = false)
      )
    );
  }

  confirmDeleteTag(id: string) {
    this.deleteTagId = id;
    this.showDeleteDialog = true;
  }

  closeModal() {
    this.deleteTagId = null;
    this.showDeleteDialog = false;
  }

  deleteTag() {
    this.isSubmitting = true;
    this.tags = this.tags.filter((category) => category.id !== this.deleteTagId);
    this.globalDetailsTag.global_detail.extended_fields.tags = this.tags;
    this.subscriptionManager.add(
      this.adminService.updateCategory(this.globalDetailsTag?.global_detail, this.globalDetailId).subscribe(
        (res) => {
          this.isSubmitting = false;
          this.closeModal();
          this.adminService.tags(res?.data?.global_detail);
        },
        () => (this.isSubmitting = false)
      )
    );
  }

  // used to take category id and returns it's name
  getCategoryNameFromId(categoryId: string): string {
    return this.tagCategory?.filter((category) => category.id === categoryId)[0]?.name;
  }

  // used to take sub category id and returns it's name
  getSubCategoryNameFromId(subCategoryId: string) {
    return this.tagSubCategory?.filter((subCate) => subCate.id === subCategoryId)[0]?.name;
  }
}

"use strict";var KTWizard=function(t,e){var n=this,r=KTUtil.getById(t);KTUtil.getBody();if(r){var i={startStep:1,clickableSteps:!1},a={construct:function(t){return KTUtil.data(r).has("wizard")?n=KTUtil.data(r).get("wizard"):(a.init(t),a.build(),KTUtil.data(r).set("wizard",n)),n},init:function(t){n.element=r,n.events=[],n.options=KTUtil.deepExtend({},i,t),n.steps=KTUtil.findAll(r,'[data-wizard-type="step"]'),n.btnSubmit=KTUtil.find(r,'[data-wizard-type="action-submit"]'),n.btnNext=KTUtil.find(r,'[data-wizard-type="action-next"]'),n.btnPrev=KTUtil.find(r,'[data-wizard-type="action-prev"]'),n.btnLast=KTUtil.find(r,'[data-wizard-type="action-last"]'),n.btnFirst=KTUtil.find(r,'[data-wizard-type="action-first"]'),n.events=[],n.currentStep=1,n.stopped=!1,n.totalSteps=n.steps.length,n.options.startStep>1&&a.goTo(n.options.startStep),a.updateUI()},build:function(){KTUtil.addEvent(n.btnNext,"click",function(t){t.preventDefault(),a.goTo(a.getNextStep(),!0)}),KTUtil.addEvent(n.btnPrev,"click",function(t){t.preventDefault(),a.goTo(a.getPrevStep(),!0)}),KTUtil.addEvent(n.btnFirst,"click",function(t){t.preventDefault(),a.goTo(a.getFirstStep(),!0)}),KTUtil.addEvent(n.btnLast,"click",function(t){t.preventDefault(),a.goTo(a.getLastStep(),!0)}),!0===n.options.clickableSteps&&KTUtil.on(r,'[data-wizard-type="step"]',"click",function(){var t=KTUtil.index(this)+1;t!==n.currentStep&&a.goTo(t,!0)})},goTo:function(t,e){if(console.log("go to:"+t),!(t===n.currentStep||t>n.totalSteps||t<0)){var r;if(t=t?parseInt(t):a.getNextStep(),!0===e&&(r=t>n.currentStep?a.eventTrigger("beforeNext"):a.eventTrigger("beforePrev")),!0!==n.stopped)return!1!==r&&(!0===e&&a.eventTrigger("beforeChange"),n.currentStep=t,a.updateUI(),!0===e&&a.eventTrigger("change")),!0===e&&(t>n.startStep?a.eventTrigger("afterNext"):a.eventTrigger("afterPrev")),n;n.stopped=!1}},stop:function(){n.stopped=!0},start:function(){n.stopped=!1},isLastStep:function(){return n.currentStep===n.totalSteps},isFirstStep:function(){return 1===n.currentStep},isBetweenStep:function(){return!1===a.isLastStep()&&!1===a.isFirstStep()},updateUI:function(){var t="",e=n.currentStep-1;t=a.isLastStep()?"last":a.isFirstStep()?"first":"between",KTUtil.attr(n.element,"data-wizard-state",t);var r=KTUtil.findAll(n.element,'[data-wizard-type="step"]');if(r&&r.length>0)for(var i=0,o=r.length;i<o;i++)i==e?KTUtil.attr(r[i],"data-wizard-state","current"):i<e?KTUtil.attr(r[i],"data-wizard-state","done"):KTUtil.attr(r[i],"data-wizard-state","pending");var s=KTUtil.findAll(n.element,'[data-wizard-type="step-info"]');if(s&&s.length>0)for(i=0,o=s.length;i<o;i++)i==e?KTUtil.attr(s[i],"data-wizard-state","current"):KTUtil.removeAttr(s[i],"data-wizard-state");var p=KTUtil.findAll(n.element,'[data-wizard-type="step-content"]');if(p&&p.length>0)for(i=0,o=p.length;i<o;i++)i==e?KTUtil.attr(p[i],"data-wizard-state","current"):KTUtil.removeAttr(p[i],"data-wizard-state")},getNextStep:function(){return n.totalSteps>=n.currentStep+1?n.currentStep+1:n.totalSteps},getPrevStep:function(){return n.currentStep-1>=1?n.currentStep-1:1},eventTrigger:function(t,e){for(var r=0;r<n.events.length;r++){var i=n.events[r];if(i.name==t){if(1!=i.one)return i.handler.call(this,n);if(0==i.fired)return n.events[r].fired=!0,i.handler.call(this,n)}}},addEvent:function(t,e,r){return n.events.push({name:t,handler:e,one:r,fired:!1}),n}};return n.setDefaults=function(t){i=t},n.goNext=function(t){return a.goTo(a.getNextStep(),t)},n.goPrev=function(t){return a.goTo(a.getPrevStep(),t)},n.goLast=function(t){return a.goTo(a.getLastStep(),t)},n.goFirst=function(t){return a.goTo(a.getFirstStep(),t)},n.goTo=function(t,e){return a.goTo(t,e)},n.stop=function(){return a.stop()},n.start=function(){return a.start()},n.getStep=function(){return n.currentStep},n.isLastStep=function(){return a.isLastStep()},n.isFirstStep=function(){return a.isFirstStep()},n.on=function(t,e){return a.addEvent(t,e)},n.one=function(t,e){return a.addEvent(t,e,!0)},a.construct.apply(n,[e]),n}};"undefined"!=typeof module&&void 0!==module.exports&&(module.exports=KTWizard);

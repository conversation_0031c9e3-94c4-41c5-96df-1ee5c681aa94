<div id="accrual-list">
  <div class="d-flex align-items-center justify-content-between border-bottom">
    <h5 class="pl-4">Accruals</h5>
    <div class="add-new-accruals">
      <span class="form-label"
        ><a href="javascript:;void" class="purple-color pointer" (click)="isAddNewAccruals = true" [ngClass]="{ 'pointer-none': accrualEdit || isAddNewAccruals }"
          >+ Add New Accruals</a
        ></span
      >
    </div>
  </div>

  <form class="form pl-4 pt-4 pb-4" autocomplete="off" [formGroup]="createAccrualForm" id="create_client_form">
    <p-table responsiveLayout="scroll" [value]="accrualList" [selectAll]="true" dataKey="accrual.id" styleClass="p-datatable-customers" [filterDelay]="0" editMode="row">
      <ng-template pTemplate="header">
        <tr class="accruals-header">
          <th class="no-wrap text-center work-exception">Work Exception</th>
          <th class="no-wrap text-center start-date">Start Date</th>
          <th class="no-wrap text-center cycle">Cycle (Days)</th>
          <th class="no-wrap text-center rate">Rate (Hours)</th>
          <th class="no-wrap text-center balance">Starting Balance</th>
          <th class="action"></th>
        </tr>
        <tr *ngIf="isAddNewAccruals">
          <th class="background-white">
            <p-dropdown
              appSflAutofocus
              [options]="exceptionTypes"
              formControlName="work_exception_type_id"
              styleClass="p-column-filter pi-icon form-control-custom work_exception-input"
              placeholder="Work Exception"
              appendTo="body"
            >
            </p-dropdown>
            <div [ngClass]="{ error: isFormInvalid() }">
              <app-form-error
                class="position-rel"
                [validation]="'required'"
                [form]="createAccrualForm"
                [controlName]="'work_exception_type_id'"
                [fieldLabel]="'Exception'"
              ></app-form-error>
            </div>
          </th>

          <th class="background-white">
            <p-calendar
              appendTo="body"
              styleClass="start-date-input"
              [readonlyInput]="false"
              inputId="startDate"
              formControlName="start_date"
              placeholder="Start Date"
              showButtonBar="true"
            >
            </p-calendar>
            <div [ngClass]="{ error: isFormInvalid() }">
              <app-form-error class="position-rel" [validation]="'required'" [form]="createAccrualForm" [controlName]="'start_date'" [fieldLabel]="'Start date'"></app-form-error>
            </div>
          </th>

          <th class="background-white">
            <input pInputText type="text" formControlName="cycle" class="w-100 p-column-filter form-control-custom" placeholder="Cycle" (input)="restrictDecimal($event)" />
            <div [ngClass]="{ error: isFormInvalid() }">
              <app-form-error class="position-rel" [validation]="'required'" [form]="createAccrualForm" [controlName]="'cycle'" [fieldLabel]="'Cycle'"></app-form-error>
              <app-form-error class="position-rel" [validation]="'min'" [form]="createAccrualForm" [controlName]="'cycle'" [fieldLabel]="'It'"></app-form-error>
            </div>
          </th>
          <th class="background-white">
            <input
              sflIsDecimalNumber
              [decimals]="2"
              pInputText
              type="text"
              formControlName="rate"
              placeholder="Rate"
              (input)="validateInput($event, 0)"
              [min]="1"
              class="w-100 p-column-filter form-control-custom"
            />
            <div [ngClass]="{ error: isFormInvalid() }">
              <app-form-error class="position-rel" [validation]="'required'" [form]="createAccrualForm" [controlName]="'rate'" [fieldLabel]="'Rate'"></app-form-error>
              <app-form-error class="position-rel" [validation]="'min'" [form]="createAccrualForm" [controlName]="'rate'" [fieldLabel]="'It'"></app-form-error>
            </div>
          </th>
          <th class="background-white">
            <input
              sflIsDecimalNumber
              [decimals]="2"
              pInputText
              type="text"
              formControlName="starting_balance"
              placeholder="Balance"
              class="w-100 p-column-filter form-control-custom"
              (input)="validateInput($event, -1)"
            />
            <div [ngClass]="{ error: isFormInvalid() }">
              <app-form-error
                class="position-rel"
                [validation]="'required'"
                [form]="createAccrualForm"
                [controlName]="'starting_balance'"
                [fieldLabel]="'Starting balance'"
              ></app-form-error>
              <small *ngIf="createAccrualForm.controls['starting_balance'].hasError('min')" class="form-text text-danger d-flex justify-content-start">
                It can have min value of 0
              </small>
            </div>
          </th>

          <th class="text-center background-white">
            <div class="d-flex justify-content-center w-100 h-100">
              <a href="javascript:;void" class="form-group btn btn-icon btn-light btn-sm btn-hover-primary" (click)="onAddAccruals()">
                <span title="Save Position" [inlineSVG]="'assets/media/svg/icons/color-save.svg'" cacheSVG="true" class="svg-icon svg-icon-md"> </span>
              </a>
              <a href="javascript:;void" class="ml-2 form-group btn btn-icon btn-light btn-sm btn-hover-primary">
                <span
                  title="Delete Position"
                  [inlineSVG]="'assets/media/svg/icons/color-delete.svg'"
                  cacheSVG="true"
                  class="svg-icon svg-icon-md"
                  (click)="onResetAddAccrualsForm()"
                >
                </span>
              </a>
            </div>
          </th>
        </tr>
      </ng-template>

      <ng-template pTemplate="body" let-accrualsList let-editing="editing" let-ri="rowIndex" #row>
        <tr [pEditableRow]="accrualsList">
          <td pEditableColumn [pEditableColumnDisabled]="true" class="position-abs body-name text-center">
            <ng-template #exception>
              <p-dropdown
                [options]="exceptionTypes"
                formControlName="work_exception_type_id"
                styleClass="p-column-filter pi-icon form-control-custom work_exception-input"
                placeholder="Work Exception"
                appendTo="body"
                [disabled]="true"
              >
              </p-dropdown>
              <div [ngClass]="{ error: isFormInvalid() }">
                <app-form-error
                  class="position-rel"
                  [validation]="'required'"
                  [form]="createAccrualForm"
                  [controlName]="'work_exception_type_id'"
                  [fieldLabel]="'Exception'"
                ></app-form-error>
              </div>
            </ng-template>
            <ng-container *ngIf="accrualsList?.accrual?.id !== editRowId; else exception">
              <span (click)="onEditAccrual(accrualsList, ri, accrualsList?.accrual?.id)" class="pointer" pInitEditableRow>
                {{ accrualsList?.accrual?.work_exception_type?.name }}
              </span>
            </ng-container>
          </td>

          <td pEditableColumn [pEditableColumnDisabled]="true" class="position-abs body-name text-center">
            <ng-template #startDate>
              <p-calendar
                appendTo="body"
                styleClass="start-date-input"
                [readonlyInput]="false"
                inputId="startDate"
                formControlName="start_date"
                placeholder="Start Date"
                showButtonBar="true"
                [disabled]="true"
              >
              </p-calendar>
              <div [ngClass]="{ error: isFormInvalid() }">
                <app-form-error class="position-rel" [validation]="'required'" [form]="createAccrualForm" [controlName]="'start_date'" [fieldLabel]="'Start date'"></app-form-error>
              </div>
            </ng-template>
            <ng-container *ngIf="accrualsList?.accrual?.id !== editRowId; else startDate">
              <span class="pointer" (click)="onEditAccrual(accrualsList, ri, accrualsList?.accrual?.id)" pInitEditableRow>
                {{ accrualsList?.accrual?.start_date | date : 'MM/dd/yyyy' }}
              </span>
            </ng-container>
          </td>

          <td pEditableColumn [pEditableColumnDisabled]="true" class="position-abs body-name text-center">
            <ng-template #cycle>
              <input pInputText class="editRow w-100" type="text" formControlName="cycle" placeholder="Cycle" name="allocation" (input)="restrictDecimal($event)" />
              <div [ngClass]="{ error: isFormInvalid() }">
                <app-form-error class="position-rel" [validation]="'required'" [form]="createAccrualForm" [controlName]="'cycle'" [fieldLabel]="'Cycle'"></app-form-error>
                <app-form-error class="position-rel" [validation]="'min'" [form]="createAccrualForm" [controlName]="'cycle'" [fieldLabel]="'It'"></app-form-error>
              </div>
            </ng-template>
            <ng-container *ngIf="accrualsList?.accrual?.id !== editRowId; else cycle">
              <span class="pointer" (click)="onEditAccrual(accrualsList, ri, accrualsList?.accrual?.id)" pInitEditableRow>
                {{ accrualsList?.accrual?.cycle }}
              </span>
            </ng-container>
          </td>

          <td pEditableColumn [pEditableColumnDisabled]="true" class="position-abs body-name text-center">
            <ng-template #rate>
              <input
                sflIsDecimalNumber
                [decimals]="2"
                pInputText
                class="editRow w-100"
                type="string"
                formControlName="rate"
                name="rate"
                placeholder="Rate"
                (input)="validateInput($event, 0)"
                [min]="1"
              />
              <div [ngClass]="{ error: isFormInvalid() }">
                <app-form-error class="position-rel" [validation]="'required'" [form]="createAccrualForm" [controlName]="'rate'" [fieldLabel]="'Rate'"></app-form-error>
                <app-form-error class="position-rel" [validation]="'min'" [form]="createAccrualForm" [controlName]="'rate'" [fieldLabel]="'It'"></app-form-error>
              </div>
            </ng-template>
            <ng-container *ngIf="accrualsList?.accrual?.id !== editRowId; else rate">
              <span class="pointer" (click)="onEditAccrual(accrualsList, ri, accrualsList?.accrual?.id)" pInitEditableRow>
                {{ accrualsList?.accrual?.rate }}
              </span>
            </ng-container>
          </td>

          <td pEditableColumn [pEditableColumnDisabled]="true" class="position-abs body-name text-center">
            <ng-template #balance>
              <input
                sflIsDecimalNumber
                [decimals]="2"
                pInputText
                class="editRow w-100"
                type="string"
                placeholder="Balance"
                formControlName="starting_balance"
                name="allocation"
                (input)="validateInput($event, -1)"
              />
              <div [ngClass]="{ error: isFormInvalid() }">
                <app-form-error
                  class="position-rel"
                  [validation]="'required'"
                  [form]="createAccrualForm"
                  [controlName]="'starting_balance'"
                  [fieldLabel]="'Starting balance'"
                ></app-form-error>
                <small *ngIf="createAccrualForm.controls['starting_balance'].hasError('min')" class="form-text text-danger d-flex justify-content-start">
                  It can have min value of 0
                </small>
              </div>
            </ng-template>
            <ng-container *ngIf="accrualsList?.accrual?.id !== editRowId; else balance">
              <span class="pointer" (click)="onEditAccrual(accrualsList, ri, accrualsList?.accrual?.id)" pInitEditableRow>
                {{ accrualsList?.accrual?.starting_balance }}
              </span>
            </ng-container>
          </td>

          <td class="text-center">
            <div ngbDropdown class="d-inline-block" *ngIf="!(editing && accrualsList?.accrual?.id === editRowId)" container="body">
              <button class="btn btn-clean btn-sm btn-icon btn-icon-md btn-expand" id="dropdownBasic1" ngbDropdownToggle [disabled]="accrualEdit || isAddNewAccruals">
                <em class="flaticon-more"></em>
              </button>
              <div ngbDropdownMenu aria-labelledby="dropdownBasic1" isOpen="false" placement="left">
                <button #EditableColumn ngbDropdownItem pInitEditableRow (click)="onEditAccrual(accrualsList, ri, accrualsList?.accrual?.id)">Edit</button>
                <button ngbDropdownItem (click)="onDeleteAccrual(accrualsList?.accrual?.id)">Delete</button>
              </div>
            </div>
            <button
              *ngIf="editing && accrualsList?.accrual?.id === editRowId"
              pButton
              pRipple
              type="button"
              pSaveEditableRow
              icon="pi pi-check"
              title="Save accrual"
              class="p-button-rounded p-button-text p-button-success p-mr-2 pr-2"
              (click)="saveEditAccrual()"
            ></button>
            <button
              *ngIf="editing && accrualsList?.accrual?.id === editRowId"
              pButton
              pRipple
              type="button"
              pCancelEditableRow
              icon="pi pi-times"
              class="p-button-rounded p-button-text p-button-danger"
              (click)="cancelEditAccrual()"
            ></button>
          </td>
        </tr>
      </ng-template>
    </p-table>
  </form>
</div>

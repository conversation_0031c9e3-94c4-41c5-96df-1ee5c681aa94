@import '../../../../../../assets/sass/components/variables.bootstrap';

.filter-options-wrapper {
  .filter-option {
    height: 40px;

    .btn-filter-icon {
      margin-right: 0px !important;
    }
  }

  ::ng-deep .p-dropdown {
    background: $primary;
    border-radius: 6px !important;
    padding: 1px 6px;
    display: flex;
    align-items: center;
    min-width: 120px;
    border: 1px solid $primary;
    height: 100%;

    .p-dropdown-label {
      color: $white;
      font-size: 16px;
    }

    .p-dropdown-trigger {
      color: $white;
    }

    .p-dropdown-panel {
      width: 160px !important;

      .p-dropdown-items-wrapper {
        margin-left: 8px;
        margin-right: 8px;

        .p-dropdown-item {
          font-size: 15px;
          color: $black;
        }
      }
    }
  }

  ::ng-deep .p-dropdown:not(.p-disabled).p-focus {
    box-shadow: none !important;
  }
  ::ng-deep .p-datatable-scrollable-both .p-datatable-thead > tr > th,
  .p-datatable-scrollable-both .p-datatable-tbody > tr > td,
  .p-datatable-scrollable-both .p-datatable-tfoot > tr > td,
  .p-datatable-scrollable-horizontal .p-datatable-thead > tr > th .p-datatable-scrollable-horizontal .p-datatable-tbody > tr > td,
  .p-datatable-scrollable-horizontal .p-datatable-tfoot > tr > td,
  .p-datatable-scrollable-both .p-datatable-thead > tr > th {
    flex: 1 1 0 !important;
  }

  ::ng-deep .p-datatable-scrollable-both .p-datatable-tbody > tr > td,
  .p-datatable-scrollable-both .p-datatable-tfoot > tr > td,
  .p-datatable-scrollable-horizontal .p-datatable-thead > tr > th .p-datatable-scrollable-horizontal .p-datatable-tbody > tr > td,
  .p-datatable-scrollable-horizontal .p-datatable-tfoot > tr > td {
    flex: 1 1 0 !important;
  }
}

import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { CreateClientComponent } from './create-client/create-client.component';
import { ManageClientComponent } from './manage-client/manage-client.component';
import { PermissionAuthGuard } from '@shared/services/permission-auth-guard.service';
import { PermissionModules } from '@shared/models/permission.enum';
import { CheckAuthResolver } from '@shared/services';
const routes: Routes = [
  {
    path: '',
    redirectTo: 'manage'
  },
  {
    path: 'manage',
    component: ManageClientComponent,
    canActivate: [PermissionAuthGuard],
    data: {
      permissionModules: [PermissionModules.VIEW_CLIENT]
    },
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'create',
    component: CreateClientComponent,
    canActivate: [PermissionAuthGuard],
    data: {
      permissionModules: [PermissionModules.MANAGE_CLIENT]
    }
  },
  {
    path: 'create/:clientId',
    component: CreateClientComponent,
    canActivate: [PermissionAuthGuard],
    data: {
      permissionModules: [PermissionModules.MANAGE_CLIENT]
    },
    resolve: { authState: CheckAuthResolver }
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ClientRoutingModule {}

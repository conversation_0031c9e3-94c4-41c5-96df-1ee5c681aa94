import { DatePipe } from '@angular/common';
import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { AdministrationService } from '@entities/administration/administration.service';
import { AccrualObj } from '@entities/manage-people/mange-people.model';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { AlertType } from '@shared/models/alert-type.enum';
import { LayoutUtilsService } from '@shared/services/layout-utils.service';
import { Table } from 'jspdf-autotable';

@Component({
  selector: 'app-accrual-list',
  templateUrl: './accrual-list.component.html',
  styleUrls: ['./accrual-list.component.scss']
})
export class AccrualListComponent extends SflBaseComponent implements OnInit {
  exceptionTypes = [];
  editAccrual: AccrualObj;
  @Input() employeeId: number;
  accrualList: AccrualObj[];
  editRowId: number;
  accrualEdit = false;
  createAccrualForm: FormGroup;
  isAddNewAccruals = false;

  @ViewChild('dt') table: Table;

  constructor(private readonly layoutUtilsService: LayoutUtilsService, private readonly datePipe: DatePipe, private readonly administrationService: AdministrationService) {
    super();
  }

  ngOnInit(): void {
    this.initializeForm();
    this.getAccrualList();
    this.getExceptionTypes();
  }

  initializeForm(): void {
    this.createAccrualForm = new FormGroup({
      cycle: new FormControl('', [Validators.required, Validators.min(1)]),
      employee_id: new FormControl(null),
      start_date: new FormControl('', Validators.required),
      work_exception_type_id: new FormControl('', Validators.required),
      rate: new FormControl('', [Validators.required, Validators.min(1)]),
      starting_balance: new FormControl('', [Validators.required, Validators.min(0)])
    });
  }

  isFormInvalid(): boolean {
    return this.createAccrualForm.invalid && (this.createAccrualForm.dirty || this.createAccrualForm.touched);
  }

  getAccrualList(): void {
    this.loading$.next(true);
    const queryFilter = {
      employee_id: this.employeeId,
      start_date: this.datePipe.transform(new Date(), this.appConstants.format)
    };

    this.subscriptionManager.add(
      this.administrationService.getActiveAccrualsListByEmployee(queryFilter).subscribe(
        (res) => {
          this.accrualList = res?.data?.accruals;
          this.accrualEdit = false;
          this.isAddNewAccruals = false;
          this.createAccrualForm.reset();
          this.loading$.next(false);
        },
        () => this.loading$.next(false)
      )
    );
  }

  getExceptionTypes(): void {
    this.loading$.next(true);
    this.subscriptionManager.add(
      this.administrationService.getExceptionTypes().subscribe(
        (res) => {
          if (res?.data?.work_exception_types) {
            const types = res?.data?.work_exception_types;
            types?.forEach((type) => {
              this.exceptionTypes.push({
                label: type?.work_exception_type?.name,
                value: type?.work_exception_type?.id
              });
            });
          }
          this.loading$.next(false);
        },
        () => this.loading$.next(false)
      )
    );
  }

  onEditAccrual(accruals: AccrualObj, ri: number, id?: number): void {
    if (!this.isAddNewAccruals) {
      this.cancelEditAccrual();
      this.editRowId = id;
      this.accrualEdit = true;
      this.editAccrual = accruals;
      this.createAccrualForm.patchValue({
        cycle: this.editAccrual.accrual.cycle,
        employee_id: this.employeeId,
        start_date: new Date(this.editAccrual.accrual.start_date),
        work_exception_type_id: this.editAccrual.accrual.work_exception_type.id,
        rate: this.editAccrual.accrual.rate,
        starting_balance: this.editAccrual.accrual.starting_balance
      });
    }
  }

  saveEditAccrual(): void {
    if (this.createAccrualForm.invalid) {
      this.createAccrualForm.markAllAsTouched();
      this.createAccrualForm.markAsDirty();
      return;
    }
    const editObj = {
      cycle: this.createAccrualForm.get('cycle').value,
      rate: this.createAccrualForm.get('rate').value ?? this.editAccrual?.accrual?.rate,
      starting_balance: this.createAccrualForm.get('starting_balance').value ?? this.editAccrual?.accrual?.starting_balance
    };
    if (!this.editAccrual.accrual.id) {
      return;
    }
    this.loading$.next(true);
    this.subscriptionManager.add(
      this.administrationService.updateAccrual(editObj, this.editAccrual.accrual.id).subscribe(
        (res) => {
          this.accrualEdit = false;
          this.editRowId = null;
          this.getAccrualList();
          this.loading$.next(false);
          this.layoutUtilsService.showActionNotification('Accrual updated successfully', AlertType.Success);
        },
        () => this.loading$.next(false)
      )
    );
  }

  cancelEditAccrual(): void {
    this.accrualEdit = false;
    this.editRowId = null;
    this.createAccrualForm.reset();
  }

  onAddAccruals(): void {
    if (this.createAccrualForm.invalid) {
      this.createAccrualForm.markAllAsTouched();
      this.createAccrualForm.markAsDirty();
      return;
    }
    this.createAccrualForm.patchValue({
      employee_id: this.employeeId
    });
    const startDate = this.datePipe.transform(this.createAccrualForm.get('start_date').value, this.appConstants.format);
    this.createAccrualForm.patchValue({
      employee_id: this.employeeId,
      start_date: startDate
    });

    this.loading$.next(true);
    this.subscriptionManager.add(
      this.administrationService.createAccrual(this.createAccrualForm.value).subscribe(
        (res) => {
          this.isAddNewAccruals = false;
          this.getAccrualList();
          this.loading$.next(false);
          this.layoutUtilsService.showActionNotification('Accrual created successfully', AlertType.Success);
        },
        () => this.loading$.next(false)
      )
    );
  }

  onResetAddAccrualsForm(): void {
    this.createAccrualForm.reset();
    this.isAddNewAccruals = false;
    this.accrualEdit = false;
  }

  onDeleteAccrual(id: number): void {
    if (id) {
      this.loading$.next(true);
      this.subscriptionManager.add(
        this.administrationService.deleteAccrual(id).subscribe(
          (res) => {
            this.getAccrualList();
            this.loading$.next(false);
            this.layoutUtilsService.showActionNotification('Accrual delete successfully', AlertType.Success);
          },
          () => this.loading$.next(false)
        )
      );
    }
  }

  restrictDecimal(event: any): void {
    const inputValue = event.target.value;
    const validValue = inputValue.replace(this.appConstants.regexForNonDigit, '');
    event.target.value = validValue;
    this.createAccrualForm.get('cycle').setValue(validValue);
  }

  validateInput(event: any, min: number): void {
    if (event.target.value > min) {
      event.target.value = event.target.value;
    } else {
      event.target.value = '';
    }
  }
}

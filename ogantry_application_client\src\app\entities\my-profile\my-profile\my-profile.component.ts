import { Component, OnInit, AfterViewInit, ElementRef, ViewChild } from '@angular/core';
import { Location } from '@angular/common';

@Component({
  selector: 'app-my-profile',
  templateUrl: './my-profile.component.html',
  styleUrls: ['./my-profile.component.scss']
})
export class MyProfileComponent implements OnInit, AfterViewInit {
  @ViewChild('wizard', { static: true }) el: ElementRef;

  model: any = {
    fname: 'John',
    lname: 'Wick',
    phone: '+***********',
    email: '<EMAIL>'
  };
  submitted = false;

  constructor(readonly location: Location) {}

  ngOnInit() {}

  ngAfterViewInit(): void {
    // Initialize form wizard
    const wizard = new KTWizard(this.el.nativeElement, {
      startStep: 1
    });

    // Validation before going to next page
    wizard.on('beforeNext', (wizardObj) => {});

    // Change event
    wizard.on('change', () => {
      setTimeout(() => {
        KTUtil.scrollTop();
      }, 500);
    });
  }

  onAccountSettingsSubmit() {
    // Call profile save api here
  }

  onChangePasswordSubmit() {
    // call change password api here
  }

  goBack() {
    this.location.back();
  }
}

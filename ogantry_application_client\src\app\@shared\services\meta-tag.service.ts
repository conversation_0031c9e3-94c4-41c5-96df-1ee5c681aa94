/**
 * @sunflowerlab
 * <AUTHOR>
 */

import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { MetaTag } from '../models/meta-tag.model';
import { Title, Meta } from '@angular/platform-browser';

@Injectable({
  providedIn: 'root'
})
export class MetaTagService {
  constructor(private readonly http: HttpClient, private readonly titleService: Title, private readonly metaTagService: Meta) {}

  setMetaTag(page: string): void {
    const metaTag: MetaTag = new MetaTag();
    //for dynamic title and description using api
    // const params = new HttpParams().set('page', page);
    // metaTag = this.http.get<MetaTag>(ApiUrl.get_metatag, { params });
    if (page === 'Dashboard') {
      metaTag.title = 'Dashboard - SFL Base Project';
      metaTag.description = 'SFL - Dashboard with various charts and tables';
    } else if (page === 'Users') {
      metaTag.title = 'User List - SFL Base Project';
      metaTag.description = 'SFL - User list where all users are listed';
    } else {
      metaTag.title = 'SFL Base Project';
      metaTag.description = 'SFL - Base Project';
    }
    if (metaTag.title) {
      this.titleService.setTitle(metaTag.title);
    }
    if (metaTag.description) {
      this.metaTagService.updateTag({
        name: 'description',
        content: metaTag.description
      });
    }
  }
}

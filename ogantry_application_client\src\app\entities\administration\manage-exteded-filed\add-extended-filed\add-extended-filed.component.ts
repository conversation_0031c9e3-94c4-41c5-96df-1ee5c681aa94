import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { AbstractControl, FormControl, FormGroup, ValidationErrors, Validators } from '@angular/forms';
import { AuthNoticeService } from '@auth/auth-notice/auth-notice.service';
import { AdministrationService } from '@entities/administration/administration.service';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { AppConstants } from '@shared/constants';
import { ButtonParams } from '@shared/models';
import { Utility } from '@shared/utils/utils';
import { Observable } from 'rxjs';

@Component({
  selector: 'app-add-extended-filed',
  templateUrl: './add-extended-filed.component.html',
  styleUrls: ['./add-extended-filed.component.scss']
})
export class AddExtendedFiledComponent extends SflBaseComponent implements OnInit, OnD<PERSON>roy {
  cardTitle = 'Create Extended Field';
  cardSubTitle = null;
  buttons: ButtonParams[] = [];
  jsonPlaceHolder: any;
  jsonForm: FormGroup;
  placeHolder: any;
  constructor(private readonly administrationService: AdministrationService, private readonly authNoticeService: AuthNoticeService) {
    super();
  }

  ngOnInit(): void {
    this.setBtnParams();
    this.initForm();
    const data = {
      extendedFieldsConfig: [
        {
          component: 'Project',
          fields: []
        }
      ]
    };
    this.placeHolder = JSON.stringify(data);
  }

  initForm(): void {
    this.jsonForm = new FormGroup({
      id: new FormControl(Utility.getUniqueId(4)),
      jsonData: new FormControl('', [this.jsonValidator])
    });
  }

  validateJson(control: AbstractControl): Observable<any> | null {
    return new Observable((observer) => {
      try {
        JSON.parse(control.value);
        observer.next(null);
        return null;
        // Valid JSON
      } catch (error) {
        observer.next({ invalidJson: true }); // Invalid JSON
      }
      observer.complete();
    });
  }

  jsonValidator(control: AbstractControl): ValidationErrors | null {
    try {
      JSON.parse(control.value);
    } catch (e) {
      return { jsonInvalid: true };
    }
  }

  onSubmit(): void {
    if (this.jsonForm.valid) {
      // Process valid JSON data here
      this.save();
    } else {
      // Handle validation errors
      this.jsonForm.markAsPristine;
      this.jsonForm.markAsTouched;
      this.jsonForm.markAsDirty;
    }
  }

  setBtnParams(): void {
    this.buttons = [
      {
        btnClass: 'btn btn-cancel',
        btnText: 'Cancel',
        redirectPath: this.appRoutes.MANAGE_Extend_Field
      },
      {
        btnClass: 'btn-save',
        btnText: 'Save',
        btnType: 'submit',
        action: this.onSubmit.bind(this),
        loading: this.isSubmitting
      }
    ];
  }

  save(): void {
    if (this.jsonForm.valid) {
      this.isSubmitting = true;
      this.setBtnParams();
      const processData = this.extededdata({
        id: this.jsonForm.value.id,
        jsonData: JSON.parse(this.jsonForm.value.jsonData)
      });
      if (this.administrationService?.extended_Filed?.id) {
        this.subscriptionManager.add(
          this.administrationService.updateTag(processData, this.administrationService.extended_Filed.id).subscribe((res) => {
            this.isSubmitting = false;
            if (this.administrationService.extended_Filed.id) {
              this.onSuccess(res.data, this.appConstants.updateExtendedField);
            } else {
              this.onSuccess(res.data, this.appConstants.createExtendedField);
            }
          })
        );
      } else {
        this.subscriptionManager.add(
          this.administrationService.addExtendedField(processData).subscribe(
            (res) => {
              this.onSuccess(res.data, this.appConstants.createExtendedField);
            },
            (err) => this.onError(err)
          )
        );
      }
    }
  }

  onSuccess(role, successMsg: string): void {
    this.isSubmitting = false;
    this.setBtnParams();
    this.administrationService.extended_Filed = role.global_detail;
    this.authNoticeService.setNotice(successMsg, 'success');
  }

  onError(err): void {
    this.isSubmitting = false;
    this.setBtnParams();
    const error = err.error;
    this.authNoticeService.setNotice(error.errors, 'danger');
  }

  extededdata(value): any {
    let extededDataPrePare = {
      name: 'ManageExtendedFiled',
      extended_fields: {
        extendArray: []
      }
    };
    if (this.administrationService?.extended_Filed?.id) {
      extededDataPrePare = this.administrationService.extended_Filed;
    }
    extededDataPrePare.extended_fields.extendArray.push(value);
    return extededDataPrePare;
  }

  ngOnDestroy(): void {
    this.authNoticeService.setNotice(null);
  }
}

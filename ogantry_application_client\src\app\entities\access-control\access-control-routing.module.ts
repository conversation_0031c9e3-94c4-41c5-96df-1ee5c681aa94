import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PermissionModules } from '@shared/models/permission.enum';
import { CheckAuthResolver } from '@shared/services';
import { PageRefreshGuard } from '@shared/services/page-refresh-guard.service';
import { PermissionAuthGuard } from '@shared/services/permission-auth-guard.service';
import { CreateUserComponent } from './create-user/create-user.component';
import { ManageUserComponent } from './manage-user/manage-user.component';
import { CreateRoleComponent } from './roles/create-role/create-role.component';
import { ManageRoleComponent } from './roles/manage-role/manage-role.component';

const routes: Routes = [
  {
    path: 'role',
    redirectTo: 'role/manage'
  },
  {
    path: 'role/manage',
    component: ManageRoleComponent,
    canActivate: [PermissionAuthGuard],
    data: {
      permissionModules: [PermissionModules.VIEW_ROLE]
    },
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'role/create',
    component: CreateRoleComponent,
    canActivate: [PermissionAuthGuard, PageRefreshGuard],
    data: {
      permissionModules: [PermissionModules.MANAGE_ROLE]
    },
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'role/update/:roleId',
    component: CreateRoleComponent,
    canActivate: [PermissionAuthGuard, PageRefreshGuard],
    data: {
      permissionModules: [PermissionModules.MANAGE_ROLE]
    },
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'manage-user',
    component: ManageUserComponent,
    canActivate: [PermissionAuthGuard],
    data: {
      permissionModules: [PermissionModules.VIEW_MANAGE_USER]
    },
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'create-user',
    component: CreateUserComponent,
    canActivate: [PermissionAuthGuard],
    data: {
      permissionModules: [PermissionModules.CREATE_USER]
    },
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'update-user/:userId',
    component: CreateUserComponent,
    canActivate: [PermissionAuthGuard],
    data: {
      permissionModules: [PermissionModules.CREATE_USER]
    },
    resolve: { authState: CheckAuthResolver }
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class SecurityDomainsRoutingModule {}

<kt-portlet>
  <kt-portlet-body>
    <!--begin: Wizard -->
    <div #wizard class="wizard wizard-2" id="kt_wizard_v2" data-wizard-state="step-first">
      <div class="wizard-nav border-right py-8 px-8 py-lg-20 px-lg-10">
        <!--begin: Form Wizard Nav -->
        <div class="wizard-steps">
          <a class="wizard-step" href="javascript:;" data-wizard-type="step" data-wizard-state="current">
            <div class="wizard-wrapper">
              <div class="wizard-icon">
                <em class="change-pass fas fa-user"></em>
              </div>
              <div class="wizard-label" data-wizard-type="action-prev">
                <div class="wizard-title">Account Settings</div>
                <div class="wizard-desc">Setup Your Account Details</div>
              </div>
            </div>
          </a>
          <a class="wizard-step" href="javascript:;" data-wizard-type="step">
            <div class="wizard-wrapper">
              <div class="wizard-icon">
                <em class="change-pass fas fa-user-lock"></em>
              </div>
              <div class="wizard-label" data-wizard-type="action-next">
                <div class="wizard-title">Change Password</div>
                <div class="wizard-desc">Change The Password Of Your Account.</div>
              </div>
            </div>
          </a>
        </div>
        <!--end: Form Wizard Nav -->
      </div>
      <div class="wizard-body py-8 px-8 py-lg-20 px-lg-10">
        <!--------------------Begin User Form-------------------------->
        <div class="row">
          <div class="offset-xxl-2 col-xxl-8">
            <form class="form" id="kt_form" (ngSubmit)="onAccountSettingsSubmit()">
              <div class="pb-5" data-wizard-type="step-content" data-wizard-state="current">
                <h4 class="mb-5 font-weight-bold text-dark">Enter your Account Details</h4>
                <div class="kt-form__section kt-form__section--first">
                  <div class="kt-wizard-v2__form">
                    <div class="form-group">
                      <label>First Name</label>
                      <input type="text" class="form-control" name="fname" [(ngModel)]="model.fname" placeholder="First Name" value="John" />
                    </div>
                    <div class="form-group">
                      <label>Last Name</label>
                      <input type="text" class="form-control" name="lname" [(ngModel)]="model.lname" placeholder="Last Name" value="Wick" />
                    </div>
                    <div class="row">
                      <div class="col-xl-6">
                        <div class="form-group">
                          <label>Phone</label>
                          <input type="tel" class="form-control" name="phone" [(ngModel)]="model.phone" placeholder="phone" value="+***********" />
                        </div>
                      </div>
                      <div class="col-xl-6">
                        <div class="form-group">
                          <label>Email</label>
                          <input type="email" [disabled]="true" class="form-control" name="email" [(ngModel)]="model.email" placeholder="Email" value="<EMAIL>" />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="submit-btn d-flex justify-content-between pt-18">
                  <button (click)="goBack()" class="btn btn-secondary font-weight-bold text-uppercase px-9 py-4 mr-2">Cancel</button>
                  <button type="submit" class="btn btn-primary font-weight-bold text-uppercase px-9 py-4">Save</button>
                </div>
              </div>
            </form>
            <!-----------------------End Of User Form------------------------------->

            <!------------------------Begin Change Password Form---------------------->
            <form class="form" id="reset_password_form" (ngSubmit)="onChangePasswordSubmit()">
              <div class="pb-5" data-wizard-type="step-content">
                <h4 class="mb-3 font-weight-bold text-dark">Change Your Password</h4>
                <div class="form-group row kt-padding-t-20">
                  <label class="col-xl-3 col-form-label">Current Password</label>
                  <div class="col-xl-12">
                    <input type="password" class="form-control" placeholder="Current password" name="currentPasswordInput" sflPasswordEye />
                  </div>
                </div>
                <div class="form-group row">
                  <label class="col-xl-3 col-form-label">New Password</label>
                  <div class="col-xl-12">
                    <input type="password" class="form-control" placeholder="New password" name="newPasswordInput" sflPasswordEye />
                  </div>
                </div>
                <div class="form-group form-group-last row">
                  <label class="col-xl-4 col-form-label">Confirm New Password</label>
                  <div class="col-xl-12">
                    <input type="password" class="form-control" placeholder="Confirm new password" name="verifyPasswordInput" sflPasswordEye />
                  </div>
                </div>
              </div>
              <hr />
              <div class="password-btn d-flex justify-content-between pt-5">
                <button class="btn btn-secondary font-weight-bold text-uppercase px-9 py-4 mr-2" data-wizard-type="action-submit" (click)="goBack()">Cancel</button>
                <button type="submit" class="btn btn-primary font-weight-bold text-uppercase px-9 py-4" data-wizard-type="action-submit">Change Password</button>
              </div>
            </form>
            <!------------------------End Of Change Password Form--------------------------------->
          </div>
        </div>
      </div>
    </div>
  </kt-portlet-body>
</kt-portlet>

/**
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2020 <PERSON><PERSON><PERSON> <<EMAIL>>
 */

import Plugin from '../core/Plugin';

export interface AliasOptions {
    // Map the alias with defined validator name
    [alias: string]: string;
}

/**
 * This plugin allows to use multiple instances of the same validator by defining alias.
 * ```
 *  formValidation(form, {
 *      fields: {
 *          email: {
 *              validators: {
 *                  required: ...,
 *                  pattern: ...,
 *                  regexp: ...
 *              }
 *          }
 *      },
 *      plugins: {
 *          alias: new Alias({
 *              required: 'notEmpty',
 *              pattern: 'regexp'
 *          })
 *      }
 *  })
 * ```
 * Then, you can use the `required`, `pattern` as the same as `notEmpty`, `regexp` validators.
 */
export default class Alias extends Plugin<AliasOptions> {
    readonly validatorNameFilter: (...arg: any[]) => string;

    constructor(opts?: AliasOptions) {
        super(opts);
        this.opts = opts || {};
        this.validatorNameFilter = this.getValidatorName.bind(this);
    }

    public install(): void {
        this.core.registerFilter('validator-name', this.validatorNameFilter);
    }

    public uninstall(): void {
        this.core.deregisterFilter('validator-name', this.validatorNameFilter);
    }

    private getValidatorName(alias: string, field: string): string {
        return this.opts[alias] || alias;
    }
}

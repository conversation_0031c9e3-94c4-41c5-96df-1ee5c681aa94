import { StaffedPositionReportComponent } from './staffed-position-report/staffed-position-report.component';
import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { BenchReportComponent } from './bench-report/bench-report.component';
import { PermissionAuthGuard } from '@shared/services/permission-auth-guard.service';
import { PermissionModules } from '@shared/models/permission.enum';
import { CheckAuthResolver } from '@shared/services';
const routes: Routes = [
  {
    path: 'bench-report',
    component: BenchReportComponent,
    canActivate: [PermissionAuthGuard],
    data: {
      permissionModules: [PermissionModules.VIEW_BENCH_REPORT]
    },
    resolve: { authState: CheckAuthResolver }
  },
  // TODO: feature is removed as of Now
  // {
  //   path: 'lost-revenue',
  //   component: OpenPositionReportComponent,
  //   canActivate: [PermissionAuthGuard],
  //   data: {
  //     permissionModules: [PermissionModules.VIEW_OPEN_POSITION_REPORT]
  //   },
  //   resolve: { authState: CheckAuthResolver }
  // },
  {
    path: 'positions-report',
    component: StaffedPositionReportComponent,
    canActivate: [PermissionAuthGuard],
    data: {
      permissionModules: [PermissionModules.VIEW_STAFFED_POSITION_REPORT]
    },
    resolve: { authState: CheckAuthResolver }
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class UtilizationManagementRoutingModule {}

<!-- begin:: Header Mobile -->
<div id="kt_header_mobile" class="header-mobile align-items-center" [ngClass]="headerMobileClasses">
  <!--begin::Logo-->
  <a routerLink="/">
    <img alt="logo" [attr.src]="headerLogo" class="header-logo img-fluid" />
  </a>
  <!--end::Logo-->

  <!--begin::Toolbar-->
  <div class="d-flex align-items-center">
    <ng-container *ngIf="asideSelfDisplay">
      <!--begin::Aside Mobile Toggle-->
      <button class="btn p-0 burger-icon burger-icon-left" id="kt_aside_mobile_toggle">
        <span></span>
      </button>
      <!--end::Aside Mobile Toggle-->
    </ng-container>
  </div>
  <!--end::Toolbar-->
</div>
<!-- end:: Header Mobile -->

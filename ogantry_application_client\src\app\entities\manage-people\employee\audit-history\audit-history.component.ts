import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { NgbAccordion } from '@ng-bootstrap/ng-bootstrap';
import { Employee, EmployeeFinancialData, Job } from '../../../administration/administration.model';
import { AdministrationService } from '@entities/administration/administration.service';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { OGantryHttpResponse } from '@shared/models';
import { AuthNoticeService } from '@auth/auth-notice/auth-notice.service';
import moment from 'moment';
import { KtDialogService } from '@shared/services';

@Component({
  selector: 'app-audit-history',
  templateUrl: './audit-history.component.html',
  styleUrls: ['./audit-history.component.scss']
})
export class AuditHistoryComponent extends SflBaseComponent implements OnInit {
  @Input() showAccordian: boolean;
  @Input() showFinancial: boolean;
  financialHistory: EmployeeFinancialData[];
  financialLoader = false;
  auditHistory: Employee[] = [];
  auditLoader = false;
  pendingJobs: Job[] = [];
  pendingJobLoader = false;
  @ViewChild('acc') el: NgbAccordion;
  @Input() employeeId: any;
  constructor(
    private readonly ktDialogService: KtDialogService,
    private readonly administrationService: AdministrationService,
    private readonly authNoticeService: AuthNoticeService
  ) {
    super();
  }

  ngOnInit(): void {
    this.getFinancialHistory();
    this.loading$.next(false);
  }
  ngOnDestroy() {
    this.authNoticeService.setNotice(null);
    this.ktDialogService.hide();
  }

  getFinancialHistory(): void {
    this.financialLoader = true;
    this.subscriptionManager.add(
      this.administrationService.getEmployeeFinancialHistory(this.employeeId).subscribe(
        (res) => {
          this.financialHistory = res.data.employee_financials;
          this.financialHistory.sort((financialA: any, financialB: any): number => {
            const startDateA = new Date(financialA.employee_financial.start_date);
            const startDateB = new Date(financialB.employee_financial.start_date);
            return startDateB.getTime() - startDateA.getTime();
          });
          this.financialLoader = false;
        },
        (err) => {
          this.financialLoader = false;
          this.onError(err);
        }
      )
    );
  }

  // TODO
  // getAuditHistory(panelId){
  //   if(this.el.isExpanded(panelId)){
  //     this.auditLoader = true;
  //     this.auditHistory = [];
  //     this.subscriptionManager.add(this.administrationService.getAuditHistory(this.employeeId).subscribe(res => {
  //      this.auditHistory = res.data.employees;
  //      this.auditLoader = false;
  //     }, (err) => {
  //       this.auditLoader = false;
  //       this.onError(err)}));
  //   }
  // }
  // getPendingJobs(panelId){
  //   if(this.el.isExpanded(panelId)){
  //     this.pendingJobLoader = true;
  //     this.pendingJobs = [];
  //     this.subscriptionManager.add(this.administrationService.getPendingJobs(this.employeeId).subscribe(res => {
  //       this.pendingJobs = res.data.jobs;
  //       this.pendingJobLoader = false;
  //     }, (err) => {
  //       this.pendingJobLoader = false;
  //       this.onError(err)}));
  //   }
  // }

  onError(err) {
    this.isSubmitting = false;
    const error: OGantryHttpResponse<Employee> = err?.error;
    this.authNoticeService?.setNotice(error?.errors, 'danger');
  }
  getSkillSet(positionTypes) {
    const positionType = [];
    if (positionTypes.length) {
      positionTypes.forEach((type) => {
        positionType.push(type?.position_type?.name);
      });
      return positionType.join();
    }
    return '-';
  }
  getDateTime(obj) {
    return obj ? moment.utc(new Date(obj)).format('MM/DD/YYYY h:mm a ') : '';
  }
}

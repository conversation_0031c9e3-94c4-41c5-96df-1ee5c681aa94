import { DatePipe } from '@angular/common';
import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { AuthNoticeService } from '@auth/auth-notice/auth-notice.service';
import { WorkException } from '@entities/administration/administration.model';
import { AdministrationService } from '@entities/administration/administration.service';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { ButtonParams } from '@shared/models';
import { AlertType } from '@shared/models/alert-type.enum';
import { KtDialogService } from '@shared/services';
import { LayoutUtilsService } from '@shared/services/layout-utils.service';
@Component({
  selector: 'app-create-holidays',
  templateUrl: './create-holidays.component.html',
  styleUrls: ['./create-holidays.component.scss'],
  providers: [DatePipe]
})
export class CreateHolidaysComponent extends SflBaseComponent implements OnInit {
  cardTitle = 'Add Holiday';
  cardSubTitle = null;
  buttons: ButtonParams[] = [];
  statuses = [];
  employees = [];
  exceptionTypes = [];
  createHolidayForm: FormGroup;
  exceptionId: number;
  workException: WorkException;
  exceptionTypeFlag = false;
  employeeIdFlag = false;
  dateError = false;
  exception: any;
  constructor(
    private readonly administrationService: AdministrationService,
    private readonly authNoticeService: AuthNoticeService,
    private readonly cdf: ChangeDetectorRef,
    private readonly datePipe: DatePipe,
    private readonly ktDialogService: KtDialogService,
    private readonly layoutUtilsService: LayoutUtilsService,
    private readonly router: Router
  ) {
    super();
  }

  ngOnInit(): void {
    this.getExceptionTypes();
    this.initForm();
    this.setBtnParams();
  }

  initForm() {
    this.createHolidayForm = new FormGroup({
      description: new FormControl('', Validators.required),
      date: new FormControl('', Validators.required),
      work_exception_type_id: new FormControl('', Validators.required),
      hours: new FormControl(8, [Validators.max(24), Validators.min(1)])
    });
  }

  getExceptionTypes() {
    this.loading$.next(true);
    this.subscriptionManager.add(
      this.administrationService.getHolidayTypes().subscribe(
        (res) => {
          if (res?.data?.work_exception_types) {
            const types = res?.data?.work_exception_types;
            types.forEach((type) => {
              this.exceptionTypes.push({
                label: type?.work_exception_type?.name,
                value: type?.work_exception_type?.id
              });
            });
          }
          this.loading$.next(false);
        },
        () => this.loading$.next(false)
      )
    );
  }

  setExceptionType(exceptionType) {
    this.exception = exceptionType?.label;
    this.createHolidayForm.controls.work_exception_type_id.setValue(exceptionType?.value);
    this.cdf.detectChanges();
  }
  setBtnParams() {
    this.buttons = [
      {
        btnClass: 'btn btn-cancel',
        btnText: 'Cancel',
        redirectPath: this.appRoutes.MANAGE_HOLIDAY
      },
      {
        btnClass: 'btn-save',
        btnText: 'Save',
        btnType: 'submit',
        action: this.onSave.bind(this),
        loading: this.isSubmitting
      }
    ];
  }

  onSave() {
    if (!this.createHolidayForm.value.work_exception_type_id) {
      this.checkFormForValidation(this.createHolidayForm);
      this.exceptionTypeFlag = true;
    } else {
      if (!this.checkFormForValidation(this.createHolidayForm)) {
        this.isSubmitting = true;
        this.setBtnParams();
        const holidayObj = this.createHolidayForm.value;
        holidayObj.date = this.datePipe.transform(new Date(holidayObj.date), 'yyyy-MM-dd');
        this.subscriptionManager.add(
          this.administrationService.createHolidayWorkException(holidayObj).subscribe(
            (res) => {
              this.router.navigateByUrl(this.appRoutes.MANAGE_HOLIDAY);
              this.layoutUtilsService.showActionNotification('Holiday created successfully', AlertType.Success);
              this.isSubmitting = false;
              this.setBtnParams();
              this.workException = res.data.holiday;
              this.exceptionId = this.workException.id;
            },
            (err) => this.onError(err)
          )
        );
      }
    }
  }

  onSuccess(successMsg: string) {
    this.authNoticeService.setNotice(successMsg, 'success');
  }

  onError(err) {
    this.isSubmitting = false;
    this.setBtnParams();
    const error = err.error;
    this.authNoticeService.setNotice(error.errors, 'danger');
  }

  ngOnDestroy() {
    this.authNoticeService.setNotice(null);
    this.ktDialogService.hide();
  }

  exceptionTypeSelected() {
    this.exceptionTypeFlag = false;
  }
}

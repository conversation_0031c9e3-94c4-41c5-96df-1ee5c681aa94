// Angular
import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
// Object-Path
import * as objectPath from 'object-path';
import { LayoutConfigService } from '@shared/services/layout-config.service';
import { SplashScreenService } from '@shared/services/splash-screen.service';
// Layout

@Component({
  selector: 'kt-splash-screen',
  templateUrl: './splash-screen.component.html',
  styleUrls: ['./splash-screen.component.scss']
})
export class SplashScreenComponent implements OnInit {
  // Public properties
  loaderType: string;
  @ViewChild('splashScreen', { static: true }) splashScreen: ElementRef;

  /**
   * Component constructor
   *
   * @param layoutConfigService: LayoutConfigService
   * @param splashScreenService: SplashScreenService
   */
  constructor(private readonly layoutConfigService: LayoutConfigService, private readonly splashScreenService: SplashScreenService) {}

  /**
   * On init
   */
  ngOnInit() {
    // init splash screen, see loader option in layout.config.ts
    const loaderConfig = this.layoutConfigService.getConfig('loader');
    this.loaderType = objectPath.get(loaderConfig, 'page-loader.type');

    this.splashScreenService.init(this.splashScreen);
  }
}

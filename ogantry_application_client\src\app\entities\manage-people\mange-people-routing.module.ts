import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ManageEmployeeComponent } from './employee/manage-employee/manage-employee.component';
import { PermissionAuthGuard } from '@shared/services/permission-auth-guard.service';
import { PermissionModules } from '@shared/models/permission.enum';
import { CreateEmployeeComponent } from './employee/create-employee/create-employee.component';
import { CheckAuthResolver } from '@shared/services';
import { ManageWorkExceptionsComponent } from './work-exceptions/manage-work-exceptions/manage-work-exceptions.component';
import { CreateWorkExceptionsComponent } from './work-exceptions/create-work-exceptions/create-work-exceptions.component';
import { TimeSheetActualsComponent } from './time-sheet/time-sheet-actuals/time-sheet-actuals.component';
import { TerminateEmployeeComponent } from './employee/terminate-employee/terminate-employee.component';
import { EmployeeUtilizationComponent } from './employee/employee-utilization/employee-utilization.component';
import { EmployeeCostComponent } from './employee/employee-cost/employee-cost.component';
import { ManageTimeComponent } from './manage-time/manage-time.component';
import { CanDeactivateGuard } from './guards/can-deactivate.guard';
import { ManageTimeDashboardComponent } from './manage-time-dashboard/manage-time-dashboard.component';

const routes: Routes = [
  {
    path: 'employee',
    redirectTo: 'employee/manage'
  },
  {
    path: 'employee/manage',
    component: ManageEmployeeComponent,
    canActivate: [PermissionAuthGuard],
    data: {
      permissionModules: [PermissionModules.VIEW_EMPLOYEE]
    },
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'employee/create',
    component: CreateEmployeeComponent,
    canActivate: [PermissionAuthGuard],
    data: {
      permissionModules: [PermissionModules.MANAGE_EMPLOYEE]
    },
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'employee/create/:employeeId',
    component: CreateEmployeeComponent,
    canActivate: [PermissionAuthGuard],
    data: {
      permissionModules: [PermissionModules.MANAGE_EMPLOYEE]
    },
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'employee/cost/:employeeId',
    component: EmployeeCostComponent,
    canActivate: [PermissionAuthGuard],
    data: {
      permissionModules: [PermissionModules.MANAGE_EMPLOYEE]
    },
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'employee/utilization/:employeeId',
    component: EmployeeUtilizationComponent,
    canActivate: [PermissionAuthGuard],
    data: {
      permissionModules: [PermissionModules.MANAGE_EMPLOYEE]
    },
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'employee/terminate/:employeeId',
    component: TerminateEmployeeComponent,
    canActivate: [PermissionAuthGuard],
    data: {
      permissionModules: [PermissionModules.MANAGE_EMPLOYEE]
    },
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'work-exceptions',
    redirectTo: 'work-exceptions/manage'
  },
  {
    path: 'work-exceptions/manage',
    component: ManageWorkExceptionsComponent,
    canActivate: [PermissionAuthGuard],
    data: {
      permissionModules: [PermissionModules.VIEW_WORK_EXCEPTION]
    },
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'work-exceptions/create',
    component: CreateWorkExceptionsComponent,
    canActivate: [PermissionAuthGuard],
    data: {
      permissionModules: [PermissionModules.MANAGE_WORK_EXCEPTION]
    },
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'work-exceptions/create/:exceptionId',
    component: CreateWorkExceptionsComponent,
    canActivate: [PermissionAuthGuard],
    data: {
      permissionModules: [PermissionModules.MANAGE_WORK_EXCEPTION]
    },
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'import-actuals',
    component: TimeSheetActualsComponent,
    canActivate: [PermissionAuthGuard],
    canDeactivate: [CanDeactivateGuard],
    data: {
      permissionModules: [PermissionModules.VIEW_IMPORT_ACTUALS]
    },
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'manage-time',
    component: ManageTimeComponent,
    canActivate: [PermissionAuthGuard],
    canDeactivate: [CanDeactivateGuard],
    data: {
      permissionModules: [PermissionModules.VIEW_TIME_ENTRIES]
    },
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'timesheet-dashboard',
    component: ManageTimeDashboardComponent,
    canActivate: [PermissionAuthGuard],
    canDeactivate: [CanDeactivateGuard],
    data: {
      permissionModules: [PermissionModules.View_Create_Entries]
    },
    resolve: { authState: CheckAuthResolver }
  },

  // {
  //   path: 'create-timesheet',
  //   component: ManageTimeComponent,
  //   canActivate: [PermissionAuthGuard],
  //   canDeactivate: [CanDeactivateGuard],
  //   data: {
  //     permissionModules: [PermissionModules.Create_TIME_ENTRIES]
  //   },
  //   resolve: { authState: CheckAuthResolver }
  // },
  {
    path: 'create-timesheet',
    component: ManageTimeComponent,
    canActivate: [PermissionAuthGuard],
    canDeactivate: [CanDeactivateGuard],
    data: {
      permissionModules: [PermissionModules.View_Create_Entries]
    },
    resolve: { authState: CheckAuthResolver }
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class MangePeopleRoutingModule {}

<div class="card card-custom gutter-b" id="create_role_form">
  <app-card-header [cardTitle]="cardTitle" [cardSubTitle]="cardSubTitle" [buttons]="buttons"></app-card-header>
  <div class="card-body create-card">
    <form class="form" autocomplete="off" [formGroup]="createTagSubCategoryForm" autocomplete="off" novalidate="novalidate" (ngSubmit)="createTagSubCategoryForm.valid && onSave()">
      <div id="client-detail">
        <kt-auth-notice></kt-auth-notice>
        <div class="mx-auto content-center">
          <div class="row">
            <div class="col-6 pr-md-5">
              <div class="form-group first">
                <label class="form-label mr-3">Tag Category</label>
                <p-dropdown
                  appendTo="body"
                  [options]="categoryMasterData"
                  (onChange)="onParentCategoryUpdate()"
                  placeholder="Select Parent Category"
                  formControlName="parentCategoryId"
                  optionLabel="name"
                  optionValue="id"
                ></p-dropdown>
                <app-form-error [validation]="'required'" [form]="createTagSubCategoryForm" [controlName]="'parentCategoryId'" [fieldLabel]="'Parent Category'"></app-form-error>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-6 pr-md-5">
              <div class="form-group first">
                <label class="form-label">Tag Sub Category</label>
                <input type="text" class="form-control custom" placeholder="e.g. Football" formControlName="name" />
                <app-form-error [validation]="'required'" [form]="createTagSubCategoryForm" [controlName]="'name'" [fieldLabel]="'Sub Category Name'"></app-form-error>
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>

/**
 * @sunflowerlab
 * <AUTHOR>
 */

import { Directive, Input, ViewContainerRef, TemplateRef, ComponentFactoryResolver, OnDestroy } from '@angular/core';
import { BehaviorSubject } from 'rxjs/internal/BehaviorSubject';
import { Subscription } from 'rxjs/internal/Subscription';
import { SflLoadingComponent } from '@shared/components/sfl-loading/sfl-loading.component';
import { KtDialogService } from '@shared/services';

/**
 * @whatitdoes it injects the loader component dynamically based on the api calls.
 * it works on BehaviourSubject
 * @howtouse <div *isFetchingData="loading$"></div>
 */
@Directive({
  selector: '[isFetchingData]'
})
export class SflIsFetchingDirective implements OnDestroy {
  visible = false;
  loadingSubscription: Subscription = new Subscription();

  constructor(
    private readonly viewContainerRef: ViewContainerRef,
    private readonly template: TemplateRef<any>,
    private readonly componentFactoryResolver: ComponentFactoryResolver,
    private readonly ktDialogService: KtDialogService
  ) {}

  @Input()
  set isFetchingData(isFetching$: BehaviorSubject<boolean>) {
    this.loadingSubscription = isFetching$.subscribe((res) => this.toggleLoader(res));
  }

  private toggleLoader(isFetching: boolean) {
    this.toggleStickySnackbar(isFetching);
    if (isFetching) {
      const loaderComponent = this.componentFactoryResolver.resolveComponentFactory(SflLoadingComponent);
      this.viewContainerRef.createComponent(loaderComponent);
    } else {
      this.viewContainerRef.clear();
      this.viewContainerRef.createEmbeddedView(this.template);
    }
  }

  private toggleStickySnackbar(_incomingValue: boolean) {
    if (_incomingValue && !this.ktDialogService.checkIsShown()) {
      this.ktDialogService.show();
    }

    if (!_incomingValue && this.ktDialogService.checkIsShown()) {
      this.ktDialogService.hide();
    }
  }

  ngOnDestroy() {
    this.loadingSubscription.unsubscribe();
  }
}

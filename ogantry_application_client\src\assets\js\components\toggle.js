"use strict";var KTToggle=function(t,e){var g=this,r=KTUtil.getById(t);if(r){var n={targetToggleMode:"class"},a={construct:function(t){return KTUtil.data(r).has("toggle")?g=KTUtil.data(r).get("toggle"):(a.init(t),a.build(),KTUtil.data(r).set("toggle",g)),g},init:function(t){g.element=r,g.events=[],g.options=KTUtil.deepExtend({},n,t),g.target=KTUtil.getById(t.target),g.targetState=g.options.targetState,g.toggleState=g.options.toggleState,"class"==g.options.targetToggleMode?g.state=KTUtil.hasClasses(g.target,g.targetState)?"on":"off":g.state=KTUtil.hasAttr(g.target,"data-"+g.targetState)?KTUtil.attr(g.target,"data-"+g.targetState):"off"},build:function(){KTUtil.addEvent(r,"mouseup",a.toggle)},toggle:function(t){return a.eventTrigger("beforeToggle"),"off"==g.state?a.toggleOn():a.toggleOff(),a.eventTrigger("afterToggle"),t.preventDefault(),g},toggleOn:function(){return a.eventTrigger("beforeOn"),"class"==g.options.targetToggleMode?KTUtil.addClass(g.target,g.targetState):KTUtil.attr(g.target,"data-"+g.targetState,"on"),g.toggleState&&KTUtil.addClass(r,g.toggleState),g.state="on",a.eventTrigger("afterOn"),a.eventTrigger("toggle"),g},toggleOff:function(){return a.eventTrigger("beforeOff"),"class"==g.options.targetToggleMode?KTUtil.removeClass(g.target,g.targetState):KTUtil.removeAttr(g.target,"data-"+g.targetState),g.toggleState&&KTUtil.removeClass(r,g.toggleState),g.state="off",a.eventTrigger("afterOff"),a.eventTrigger("toggle"),g},eventTrigger:function(t){for(var e=0;e<g.events.length;e++){var r=g.events[e];if(r.name==t){if(1!=r.one)return r.handler.call(this,g);if(0==r.fired)return g.events[e].fired=!0,r.handler.call(this,g)}}},addEvent:function(t,e,r){return g.events.push({name:t,handler:e,one:r,fired:!1}),g}};return g.setDefaults=function(t){n=t},g.getState=function(){return g.state},g.toggle=function(){return a.toggle()},g.toggleOn=function(){return a.toggleOn()},g.toggleOff=function(){return a.toggleOff()},g.on=function(t,e){return a.addEvent(t,e)},g.one=function(t,e){return a.addEvent(t,e,!0)},a.construct.apply(g,[e]),g}};"undefined"!=typeof module&&void 0!==module.exports&&(module.exports=KTToggle);

import { Component, OnInit } from '@angular/core';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { ButtonParams } from '@shared/models';
import { GlobalDetailSubCategory, GlobalDetailTaggingCategory, SubCategory, TagCategory } from '../administration.model';
import { AdministrationService } from '../administration.service';

@Component({
  selector: 'app-manage-tag-sub-category',
  templateUrl: './manage-tag-sub-category.component.html',
  styleUrls: ['./manage-tag-sub-category.component.scss']
})
export class ManageTagSubCategoryComponent extends SflBaseComponent implements OnInit {
  tagSubCategory: SubCategory[] = [];
  tagCategories: TagCategory[] = [];
  loading = false;
  cardTitle = 'Manage Tag Sub Category';
  cardSubTitle = null;
  buttons: ButtonParams[] = [
    {
      btnClass: 'btn-save',
      btnText: 'Add New',
      redirectPath: this.appRoutes.CREATE_TAG_SUB_CATEGORY,
      permissions: [this.permissionModules.MANAGE_TAG_CATEGORY]
    }
  ];
  deleteCategoryId: string;
  showDeleteDialog = false;
  globalDetailsTaggingCategory: GlobalDetailTaggingCategory;
  globalDetailsTagSubCategory: GlobalDetailSubCategory;
  globalDetailId: number;

  constructor(private readonly adminService: AdministrationService) {
    super();
  }

  ngOnInit(): void {
    this.getCategoryMasterData();
  }

  getCategoryMasterData() {
    this.tagCategories = [];
    this.loading = true;
    this.subscriptionManager.add(
      this.adminService.getTagCategories('TagCategoryManagement').subscribe(
        (res) => {
          this.loading = false;
          if (res?.data?.global_details) {
            const globalDetail = res?.data?.global_details;
            if (globalDetail && globalDetail[0]?.global_detail?.name === 'TagCategoryManagement') {
              this.globalDetailsTaggingCategory = globalDetail[0];
              this.globalDetailId = globalDetail[0].global_detail.id;
              this.tagCategories = globalDetail[0].global_detail.extended_fields.tagCategory;
              this.adminService.setTagCategories(globalDetail[0].global_detail);
              this.getTagSubCategories();
            }
          }
        },
        () => (this.loading = false)
      )
    );
  }

  updateMasterDataObject() {
    for (const category of this.globalDetailsTaggingCategory.global_detail.extended_fields.tagCategory) {
      category.subTagCategory = [...this.tagSubCategory?.filter((subCate) => subCate?.parentCategoryId === category?.id)];
    }
  }

  getTagSubCategories() {
    this.tagSubCategory = [];
    this.loading = true;
    this.subscriptionManager.add(
      this.adminService.getTagSubCategories('SubCategoryManagement').subscribe(
        (res) => {
          this.loading = false;
          if (res?.data?.global_details) {
            const globalDetail = res?.data?.global_details;
            if (globalDetail && globalDetail[0]?.global_detail?.name === 'SubCategoryManagement') {
              this.globalDetailsTagSubCategory = globalDetail[0];
              this.globalDetailId = globalDetail[0].global_detail.id;
              this.tagSubCategory = globalDetail[0].global_detail.extended_fields.subCategory;
              this.adminService.setTagSubCategories(globalDetail[0].global_detail);
              this.updateMasterDataObject();
            }
          }
        },
        () => (this.loading = false)
      )
    );
  }

  confirmDeleteCategory(id: string) {
    this.deleteCategoryId = id;
    this.showDeleteDialog = true;
  }

  closeModal() {
    this.deleteCategoryId = null;
    this.showDeleteDialog = false;
  }

  deleteCategory() {
    this.isSubmitting = true;
    this.tagSubCategory = this.tagSubCategory.filter((subCate) => subCate.id !== this.deleteCategoryId);
    this.globalDetailsTagSubCategory.global_detail.extended_fields.subCategory = this.tagSubCategory;
    this.subscriptionManager.add(
      this.adminService.updateSubCategory(this.globalDetailsTagSubCategory?.global_detail, this.globalDetailId).subscribe(
        (res) => {
          this.isSubmitting = false;
          this.closeModal();
          this.adminService.setTagSubCategories(res?.data?.global_detail);
          this.updateMasterDataObject();
        },
        () => (this.isSubmitting = false)
      )
    );
  }
}

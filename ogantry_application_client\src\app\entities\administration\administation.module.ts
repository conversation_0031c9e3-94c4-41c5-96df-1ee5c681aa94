import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SharedModule } from '@shared/shared.module';
import { AdministationRoutingModule } from './administation-routing.module';
import { TableModule } from 'primeng/table';
import { DropdownModule } from 'primeng/dropdown';
import { InputTextModule } from 'primeng/inputtext';
import { CalendarModule } from 'primeng/calendar';
import { DialogModule } from 'primeng/dialog';
import { ManageExpenseTypesComponent } from './expense-types/manage-expense-types/manage-expense-types.component';
import { CreateExpenseTypesComponent } from './expense-types/create-expense-types/create-expense-types.component';
import { ManageEmployeeTypeComponent } from './employee-type/manage-employee-type/manage-employee-type.component';
import { CreateEmployeeTypeComponent } from './employee-type/create-employee-type/create-employee-type.component';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { ManagePositionTypeComponent } from './position-type/manage-position-type/manage-position-type.component';
import { CreatePositionTypeComponent } from './position-type/create-position-type/create-position-type.component';
import { ManageMonthlyExpenseTypeComponent } from './monthly-expense-types/manage-monthly-expense-types/manage-monthly-expense-type/manage-monthly-expense-type.component';
import { CreateMonthlyExpenseTypeComponent } from './monthly-expense-types/create-monthly-expense-types/create-monthly-expense-type/create-monthly-expense-type.component';
import { ManageHolidaysComponent } from './holidays/manage-holidays/manage-holidays.component';
import { CreateHolidaysComponent } from './holidays/create-holidays/create-holidays.component';
import { CreateWorkExceptionTypeComponent } from './work-exception-types/create-work-exception-type/create-work-exception-type.component';
import { ManageWorkExceptionTypeComponent } from './work-exception-types/manage-work-exception-type/manage-work-exception-type.component';
import { InputNumberModule } from 'primeng/inputnumber';
import { ManageTagCategoryComponent } from './manage-tag-category/manage-tag-category.component';
import { AddUpdateTagCategoryComponent } from './manage-tag-category/add-update-tag-category/add-update-tag-category.component';
import { ManageTagSubCategoryComponent } from './manage-tag-sub-category/manage-tag-sub-category.component';
import { AddUpdateSubCategoryComponent } from './manage-tag-sub-category/add-update-sub-category/add-update-sub-category.component';
import { AppendTagsComponent } from './append-tags/append-tags.component';
import { ManageTagComponent } from './manage-tag/manage-tag.component';
import { AddUpdateTagsComponent } from './manage-tag/add-update-tags/add-update-tags.component';
import { TreeSelectModule } from 'primeng/treeselect';
import { BadgeModule } from 'primeng/badge';
import { ManageExtendedFiledComponent } from './manage-exteded-filed/manage-exteded-filed.component';
import { AddExtendedFiledComponent } from './manage-exteded-filed/add-extended-filed/add-extended-filed.component';
import { ManageGlobalDetailsComponent } from './manage-global-details/manage-global-details.component';
import { AddGlobalDetailComponent } from './manage-global-details/add-global-detail/add-global-detail.component';

@NgModule({
  declarations: [
    ManageExpenseTypesComponent,
    CreateExpenseTypesComponent,
    ManageEmployeeTypeComponent,
    CreateEmployeeTypeComponent,
    ManagePositionTypeComponent,
    CreatePositionTypeComponent,
    ManageMonthlyExpenseTypeComponent,
    CreateMonthlyExpenseTypeComponent,
    ManageHolidaysComponent,
    CreateHolidaysComponent,
    CreateWorkExceptionTypeComponent,
    ManageWorkExceptionTypeComponent,
    ManageTagCategoryComponent,
    AddUpdateTagCategoryComponent,
    ManageTagSubCategoryComponent,
    AddUpdateSubCategoryComponent,
    ManageTagComponent,
    AddUpdateTagsComponent,
    AppendTagsComponent,
    ManageExtendedFiledComponent,
    AddExtendedFiledComponent,
    ManageGlobalDetailsComponent,
    AddGlobalDetailComponent
  ],
  imports: [
    CommonModule,
    SharedModule,
    AdministationRoutingModule,
    TableModule,
    DropdownModule,
    InputTextModule,
    CalendarModule,
    DialogModule,
    MatSlideToggleModule,
    InputNumberModule,
    TreeSelectModule,
    BadgeModule
  ]
})
export class AdministationModule {}

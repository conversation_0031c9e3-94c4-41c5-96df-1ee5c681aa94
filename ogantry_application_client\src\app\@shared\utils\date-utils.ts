export function ConvertDate(dateStr: string): string {
  // Split the date string into year, month, and day
  const parts = dateStr.split('-');
  const year = parseInt(parts[0], 10);
  const month = parseInt(parts[1], 10);
  const day = parseInt(parts[2], 10);

  // Array of month names abbreviated
  const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

  // Format the date to 'DD Mon' format
  const formattedDate = `${day} ${monthNames[month - 1]}`;

  return formattedDate;
}

export function formatDateToYYYYMMDD(date: Date) {
  if (!(date instanceof Date)) {
    throw new Error('Input must be a Date object');
  }

  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are 0-based
  const day = String(date.getDate()).padStart(2, '0');

  return `${year}-${month}-${day}`;
}

export function getLastDateOfPreviousMonth(inputDate) {
  const date = new Date(inputDate);
  return new Date(date.getFullYear(), date.getMonth() + 1, 0); // day 0 of current month = last day of previous month
}

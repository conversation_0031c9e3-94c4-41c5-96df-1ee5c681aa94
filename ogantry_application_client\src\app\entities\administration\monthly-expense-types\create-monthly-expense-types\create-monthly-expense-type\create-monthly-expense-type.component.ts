import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Params } from '@angular/router';
import { AuthNoticeService } from '@auth/auth-notice/auth-notice.service';
import { AdministrationService } from '@entities/administration/administration.service';
import { MonthlyExpenseType } from '@entities/project/project.model';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { ButtonParams, OGantryHttpResponse } from '@shared/models';
import { KtDialogService } from '@shared/services';

@Component({
  selector: 'app-create-monthly-expense-type',
  templateUrl: './create-monthly-expense-type.component.html',
  styleUrls: ['./create-monthly-expense-type.component.scss']
})
export class CreateMonthlyExpenseTypeComponent extends SflBaseComponent implements OnInit {
  cardTitle = 'Add Monthly Expense Type';
  cardSubTitle = null;
  buttons: ButtonParams[] = [];
  createExpenseTypeForm: FormGroup;
  typeId: number;
  expenseType: MonthlyExpenseType;
  constructor(
    private readonly administrationService: AdministrationService,
    private readonly authNoticeService: AuthNoticeService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly ktDialogService: KtDialogService
  ) {
    super();
  }
  ngOnInit(): void {
    this.loading$.next(false);
    this.initForm();
    this.getParams();
  }

  getParams() {
    this.activatedRoute.params.subscribe((params: Params) => {
      this.typeId = params.typeId;
      if (this.typeId) {
        this.cardTitle = 'Edit Monthly Expense Type';
        this.getExpenseType();
      }
      this.setBtnParams();
    });
  }

  getExpenseType() {
    this.loading$.next(true);
    this.subscriptionManager.add(
      this.administrationService.getMonthlyExpenseType(this.typeId).subscribe(
        (res) => {
          this.expenseType = res.data;
          this.setExpenseTypeForm();
          this.loading$.next(false);
        },
        () => this.loading$.next(false)
      )
    );
  }

  initForm() {
    this.createExpenseTypeForm = new FormGroup({
      name: new FormControl('', Validators.required)
    });
  }

  setBtnParams() {
    this.buttons = [
      {
        btnClass: 'btn btn-cancel',
        btnText: 'Cancel',
        redirectPath: this.appRoutes.MANAGE_MONTHLY_EXPENSE_TYPE
      },
      {
        btnClass: 'btn-save',
        btnText: 'Save',
        btnType: 'submit',
        action: this.typeId ? this.onEdit.bind(this) : this.onSave.bind(this),
        loading: this.isSubmitting
      }
    ];
  }

  onEdit() {
    if (!this.checkFormForValidation(this.createExpenseTypeForm)) {
      this.isSubmitting = true;
      this.setBtnParams();
      this.subscriptionManager.add(
        this.administrationService.updateMonthlyExpenseType(this.createExpenseTypeForm.value, this.typeId).subscribe(
          (res) => {
            this.onSuccess(res.data, 'Monthly Expense Type updated successfully');
          },
          (err) => this.onError(err)
        )
      );
    }
  }

  onSave() {
    if (!this.checkFormForValidation(this.createExpenseTypeForm)) {
      this.isSubmitting = true;
      this.setBtnParams();
      this.subscriptionManager.add(
        this.administrationService.createMonthlyExpenseType(this.createExpenseTypeForm.value).subscribe(
          (res) => {
            this.onSuccess(res.data, 'Monthly Expense Type created successfully');
          },
          (err) => this.onError(err)
        )
      );
    }
  }

  ngOnDestroy() {
    this.authNoticeService.setNotice(null);
    this.ktDialogService.hide();
  }

  onSuccess(expenseType: MonthlyExpenseType, successMsg: string) {
    this.isSubmitting = false;
    this.setBtnParams();
    this.expenseType = expenseType;
    this.typeId = this.expenseType.monthly_expense_type.id;
    this.setBtnParams();
    this.authNoticeService.setNotice(successMsg, 'success');
  }

  onError(err) {
    this.isSubmitting = false;
    this.setBtnParams();
    const error: OGantryHttpResponse<MonthlyExpenseType> = err.error;
    this.authNoticeService.setNotice(error.errors, 'danger');
  }

  setExpenseTypeForm() {
    const expenseType = this.expenseType.monthly_expense_type;
    this.createExpenseTypeForm.controls['name'].setValue(expenseType.name);
  }
}

import { Component, OnInit } from '@angular/core';
import { FormArray, FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Params } from '@angular/router';
import { AuthNoticeService } from '@auth/auth-notice/auth-notice.service';
import { SubCategory, TagCategory, Tags } from '@entities/administration/administration.model';
import { AdministrationService } from '@entities/administration/administration.service';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { ButtonParams } from '@shared/models';
import { Utility } from '@shared/utils/utils';

@Component({
  selector: 'app-add-update-tags',
  templateUrl: './add-update-tags.component.html',
  styleUrls: ['./add-update-tags.component.scss']
})
export class AddUpdateTagsComponent extends SflBaseComponent implements OnInit {
  cardTitle = 'Create Tag';
  cardSubTitle = null;
  buttons: ButtonParams[] = [];
  categoryMasterData: TagCategory[] = [];
  subCategoryMasterData: SubCategory[] = [];
  subCategories: SubCategory[] = [];
  constructor(
    private readonly administrationService: AdministrationService,
    private readonly authNoticeService: AuthNoticeService,
    private readonly activatedRoute: ActivatedRoute
  ) {
    super();
  }
  createTagForm: FormGroup;
  tagId: number;

  ngOnInit(): void {
    this.initForm();
    this.setCategoryMasterData();
    this.setSubCategoryMasterData();
  }

  setCategoryMasterData() {
    this.administrationService.getTagCategories('TagCategoryManagement').subscribe((categoryDetails) => {
      this.categoryMasterData = categoryDetails.data.global_details[0].global_detail.extended_fields.tagCategory;
    });
  }

  setSubCategoryMasterData() {
    this.administrationService.getTagSubCategories('SubCategoryManagement').subscribe((categoryDetails) => {
      this.subCategoryMasterData = categoryDetails.data.global_details[0].global_detail.extended_fields.subCategory;
      this.getParams();
    });
  }

  updatedSubCategoryList() {
    this.subCategories = this.subCategoryMasterData?.filter((subCate) => subCate?.parentCategoryId === this.createTagForm.controls['tagCategory'].value);
  }

  getParams() {
    this.activatedRoute.params.subscribe((params: Params) => {
      this.tagId = params.tagId;
      if (this.tagId) {
        this.cardTitle = 'Edit Tag';
        this.setTagForm();
      }
      this.setBtnParams();
    });
  }

  setTagForm() {
    const tagData = this.administrationService.tags?.extended_fields?.tags?.filter((tag) => tag.id === this.tagId);
    if (tagData?.length) {
      this.createTagForm.controls['name'].setValue(tagData[0].name);
      this.createTagForm.controls['id'].setValue(tagData[0].id);
      this.createTagForm.controls['tagCategory'].setValue(tagData[0].tagCategory);
      this.createTagForm.controls['subTagCategory'].setValue(tagData[0].subTagCategory);
      this.updatedSubCategoryList();
    }
  }

  initForm() {
    this.createTagForm = new FormGroup({
      id: new FormControl(Utility.getUniqueId(4)),
      name: new FormControl('', Validators.required),
      tagCategory: new FormControl('', Validators.required),
      subTagCategory: new FormControl('')
    });
  }

  setBtnParams() {
    this.buttons = [
      {
        btnClass: 'btn btn-cancel',
        btnText: 'Cancel',
        redirectPath: this.appRoutes.MANAGE_TAG
      },
      {
        btnClass: 'btn-save',
        btnText: 'Save',
        btnType: 'submit',
        action: this.onSave.bind(this),
        loading: this.isSubmitting
      }
    ];
  }

  toggleButton(role) {
    role.expand = !role.expand;
  }

  onSuccess(role, successMsg: string) {
    this.isSubmitting = false;
    this.setBtnParams();
    this.administrationService.tagCategories = role.global_detail;
    this.authNoticeService.setNotice(successMsg, 'success');
  }

  onError(err) {
    this.isSubmitting = false;
    this.setBtnParams();
    const error = err.error;
    this.authNoticeService.setNotice(error.errors, 'danger');
  }

  onSave() {
    if (!this.checkFormForValidation(this.createTagForm)) {
      this.isSubmitting = true;
      this.setBtnParams();
      this.createTagForm.value.name = this.createTagForm.value.name.replace(/ /g, '-');
      const categoryData = this.getExtendedData(this.createTagForm.value);
      if (this.administrationService.tags?.id) {
        this.subscriptionManager.add(
          this.administrationService.updateTag(categoryData, this.administrationService.tags.id).subscribe(
            (res) => {
              if (this.tagId) {
                this.onSuccess(res.data, 'Tag updated successfully');
              } else {
                this.onSuccess(res.data, 'Tag created successfully');
              }
            },
            (err) => this.onError(err)
          )
        );
      } else {
        this.subscriptionManager.add(
          this.administrationService.addTag(categoryData).subscribe(
            (res) => {
              this.onSuccess(res.data, 'Tag created successfully');
            },
            (err) => this.onError(err)
          )
        );
      }
    }
  }

  getExtendedData(tagFormValue: Tags) {
    const returnData = { name: '', extended_fields: { tags: [] } };
    tagFormValue = { ...tagFormValue };
    if (this.administrationService.tags?.id) {
      returnData.name = this.administrationService.tags.name;
      if (this.administrationService.tags && this.administrationService.tags?.extended_fields?.tags?.length) {
        this.administrationService.tags.extended_fields.tags = this.administrationService.tags.extended_fields.tags.filter((tag) => tag.id !== tagFormValue.id);
        returnData.extended_fields.tags = this.administrationService.tags.extended_fields.tags;
        returnData.extended_fields.tags.push(tagFormValue);
      } else {
        returnData.extended_fields.tags = [tagFormValue];
      }
    } else {
      returnData.name = 'TagManagement';
      returnData.extended_fields.tags = [tagFormValue];
    }
    return returnData;
  }

  ngOnDestroy() {
    this.authNoticeService.setNotice(null);
  }
}

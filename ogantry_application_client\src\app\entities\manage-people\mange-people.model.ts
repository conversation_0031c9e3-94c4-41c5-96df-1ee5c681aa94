export interface OGProjectList {
  clientName: string;
  projectName: string;
}

export interface OGPositionList {
  clientName: string;
  employeeFullName: string;
  positionId: number;
  positionName: string;
  projectName: string;
  startDate: string;
  endDate: string;
}

export interface timeSheetEntries {
  time_entry: {
    date: string;
    hours: number;
    position_id: number;
    projected_hours: number;
  };
}

export interface GlobalDetailTimeSheetEntries {
  global_details: GlobalDetailsTimeSheetFiles[];
}

export interface GlobalDetailsTimeSheetFiles {
  global_detail?: {
    extended_fields?: { uploadedFiles: GlobalDetailsTimeSheetFile[] };
    name?: string;
    id?: number;
  };
}

export interface GlobalDetailsTimeSheetFile {
  filePath: string;
  Date: Date;
  User: string;
}

export interface UpdateWorkExceptionRequest {
  date: string;
  description: string;
  hours: number;
  employee_id: number;
  work_exception_type_id: number;
}

export interface LabelValueObj {
  label: string;
  value: number;
}

export interface TimeEntries {
  time_entry: {
    date: string;
    hours: number | string;
    position_id: number;
    id?: number;
    comment: string | null;
    status: string;
  };
}

export interface HoursWithStatus {
  NOT_SUBMITTED: number;
  SUBMITTED: number;
  APPROVED: number;
  REJECTED: number;
}

export interface CalcAccrualResponse {
  calculations: CalcAccrualObj[];
}

export interface CalcAccrualObj {
  calculation: Calculation;
}

export interface Calculation {
  employee?: Employee;
  work_exception_type?: WorkExceptionType;
  active_date?: string;
  periods_elapsed?: number;
  amount_accrued?: string;
  amount_used?: number | string;
  remaining_balance?: string;
}
export interface AccrualList {
  accruals: AccrualObj[];
}

export interface AccrualObj {
  accrual: Accrual;
}

export interface Accrual {
  id?: number;
  start_date?: string;
  end_date?: string;
  cycle: number | string;
  rate: string;
  starting_balance: string;
  employee_id?: number;
  work_exception_type_id?: number;
  work_exception_type?: {
    id: number;
    name: string;
  };
  employee?: Employee;
}

export interface Employee {
  id: number;
  start_date: string;
  end_date: string;
  daily_billable_hours: number;
  email: string;
  hourly_cost: string;
  first_name: string;
  last_name: string;
  utilization_target: string;
  extended_fields: any;
}

export interface WorkExceptionType {
  id: number;
  name: string;
}

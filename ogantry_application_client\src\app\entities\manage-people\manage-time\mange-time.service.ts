import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ApiUrl } from '@shared/constants';
import { Observable } from 'rxjs';

import { EmployeeLookupApiResponse } from '@entities/utilization-management/utilization.model';

@Injectable({
  providedIn: 'root'
})
export class MangeTimeService {
  constructor(private readonly http: HttpClient) {}

  getTimesheet(filters: any): Observable<any> {
    let params = new HttpParams();
    Object.keys(filters).forEach((key) => {
      params = params.append(key, filters[key]);
    });

    return this.http.get<any>(ApiUrl.manageTime, { params });
  }

  employeeLookup(email: string): Observable<EmployeeLookupApiResponse> {
    let params = new HttpParams().set('email', email);
    return this.http.get<EmployeeLookupApiResponse>(ApiUrl.employeeLookup, { params });
  }
}

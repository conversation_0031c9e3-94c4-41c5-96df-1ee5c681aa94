.add-contact .card-body.create-card {
  box-shadow: none;
  margin: 0;
  padding: 20px !important;
}

.form-group {
  padding-bottom: 0;
  &:first-child {
    padding-top: 0;
  }
}

::ng-deep .dropdown .p-dropdown,
::ng-deep .p-multiselect {
  width: 100%;
  height: 100%;
  border-radius: 9px !important;
  border: none !important;
  background-color: #f8f8ff !important;
  min-height: 60px !important;
  padding: 1.2rem;
}

::ng-deep .dropdown .p-dropdown .p-dropdown-label,
::ng-deep .p-multiselect .p-multiselect-label {
  color: #000000;
  font-family: Poppins;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: -0.32px;
  line-height: 25px;
}

::ng-deep .dropdown .p-dropdown .p-dropdown-label.p-placeholder,
::ng-deep .p-multiselect .p-multiselect-label.p-placeholder {
  color: #b5b5c3 !important;
}

::ng-deep .p-multiselect:not(.p-disabled).p-focus {
  box-shadow: none;
}

::ng-deep .range-calender .p-calendar-w-btn {
  height: 100%;
  width: 100%;
}

::ng-deep .range-calender .p-calendar-w-btn .p-inputtext {
  border: 0;
  background-color: #f8f8ff !important;
}

::ng-deep .p-button {
  background: #4b3f72;
  border-color: #4b3f72;
}

::ng-deep .p-button:enabled:hover {
  background: #574985;
  border-color: #574985;
}

::ng-deep .p-button.p-button-icon-only {
  width: 3.357rem;
}

::ng-deep .p-inputtext:enabled:focus {
  box-shadow: none;
}

.form-check-label {
  color: #000000;
  font-family: Poppins;
  font-size: 14px;
  letter-spacing: 0;
  line-height: 21px;
}

input[type='radio'] {
  display: none;
}

input[type='radio']:checked + label:before {
  background: #4b3f72;
  color: #ffffff;
  content: '\2713';
  text-align: center;
}

input[type='radio'] + label:before {
  border: 1px solid #4b3f72;
  border-radius: 1rem;
  content: '\00a0';
  display: inline-block;
  font: 16px/1em sans-serif;
  height: 16px;
  margin: 0 0.25em 0 0;
  padding: 0;
  vertical-align: top;
  width: 16px;
}

.filter-tags {
  ::ng-deep .p-treeselect {
    width: 100% !important;
  }
}

::ng-deep .p-checkbox .p-checkbox-box.p-highlight {
  border-color: #827da0 !important;
  background: #827da0 !important;
}

::ng-deep .p-checkbox:not(.p-checkbox-disabled) .p-checkbox-box:hover {
  border-color: #827da0;
}

::ng-deep .p-checkbox:not(.p-checkbox-disabled) .p-checkbox-box .p-highlight:hover {
  border-color: #827da0 !important;
  background: #827da0 !important;
}

::ng-deep .p-multiselect-panel .p-multiselect-items .p-multiselect-item.p-highlight {
  border-color: #dcdcdd;
  background: #dcdcdd;
}

::ng-deep .p-multiselect-panel .p-multiselect-items .p-multiselect-item:focus {
  box-shadow: none;
}

::ng-deep .p-multiselect.p-multiselect-chip .p-multiselect-token {
  background: #dcdcdd;
}
.add-contact .card-body.create-card {
  box-shadow: none;
  margin: 0;
  padding: 20px !important;
}

.form-group {
  padding-bottom: 0;
  &:first-child {
    padding-top: 0;
  }
}

::ng-deep .dropdown .p-dropdown .p-dropdown-label,
::ng-deep .p-multiselect .p-multiselect-label {
  color: #000000;
  font-family: Poppins;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: -0.32px;
  line-height: 25px;
  padding-top: 0.5rem;
  padding-left: 1rem;
}

::ng-deep .dropdown .p-dropdown .p-dropdown-label.p-placeholder,
::ng-deep .p-multiselect .p-multiselect-label.p-placeholder {
  color: #b5b5c3 !important;
}

::ng-deep .p-multiselect:not(.p-disabled).p-focus {
  box-shadow: none;
}

::ng-deep .range-calender .p-calendar-w-btn {
  height: 100%;
  width: 100%;
}

::ng-deep .range-calender .p-calendar-w-btn .p-inputtext {
  border: 0;
  background-color: #f8f8ff !important;
}

::ng-deep .p-button {
  background: #4b3f72;
  border-color: #4b3f72;
}

::ng-deep .p-button:enabled:hover {
  background: #574985;
  border-color: #574985;
}

::ng-deep .p-button.p-button-icon-only {
  width: 3.357rem;
}

::ng-deep .p-inputtext:enabled:focus {
  box-shadow: none;
}

.form-check-label {
  color: #000000;
  font-family: Poppins;
  font-size: 14px;
  letter-spacing: 0;
  line-height: 21px;
}

input[type='radio'] {
  display: none;
}

input[type='radio']:checked + label:before {
  background: #4b3f72;
  color: #ffffff;
  content: '\2713';
  text-align: center;
}

input[type='radio'] + label:before {
  border: 1px solid #4b3f72;
  border-radius: 1rem;
  content: '\00a0';
  display: inline-block;
  font: 16px/1em sans-serif;
  height: 16px;
  margin: 0 0.25em 0 0;
  padding: 0;
  vertical-align: top;
  width: 16px;
}

::ng-deep .p-checkbox .p-checkbox-box.p-highlight {
  border-color: #827da0 !important;
  background: #827da0 !important;
}

::ng-deep .p-checkbox:not(.p-checkbox-disabled) .p-checkbox-box:hover {
  border-color: #827da0;
}

::ng-deep .p-checkbox:not(.p-checkbox-disabled) .p-checkbox-box .p-highlight:hover {
  border-color: #827da0 !important;
  background: #827da0 !important;
}

::ng-deep .p-multiselect-panel .p-multiselect-items .p-multiselect-item.p-highlight {
  border-color: #dcdcdd;
  background: #dcdcdd;
}

::ng-deep .p-multiselect-panel .p-multiselect-items .p-multiselect-item:focus {
  box-shadow: none;
}

::ng-deep .p-multiselect.p-multiselect-chip .p-multiselect-token {
  background: #dcdcdd;
}
.background {
  height: 38px;
  border-radius: 9px 9px 0 0;
  background-color: #eeebf4;
  display: flex;
  align-items: center;
  padding-left: 0.5rem;
}

.PL-border {
  box-sizing: border-box;
  border: 1px solid #eeebf4;
  border-radius: 9px;
  margin-bottom: 1rem;
}
.save-filter-radio {
  display: flex;
  justify-content: flex-end;
  width: 100%;
}
.width-65 {
  width: 65%;
}

#manageMonthlyExpenseType {
  ::ng-deep .p-dropdown .p-dropdown-trigger {
    width: 1.2rem;
  }
  ::ng-deep .p-dropdown {
    width: 100%;
  }
  ::ng-deep .p-inputtext {
    width: 100%;
  }

  ::ng-deep .p-dropdown-label {
    padding-right: 0rem !important;
  }

  .header-width {
    width: 16%;
  }

  .icon-background {
    background-color: #4b3f72 !important;
    padding: 0.2rem;
  }

  ::ng-deep .p-paginator .p-paginator-pages .p-paginator-page:not(.p-highlight):hover {
    border-radius: 50%;
  }

  ::ng-deep .p-paginator .p-paginator-first:not(.p-disabled):not(.p-highlight):hover,
  ::ng-deep .p-paginator .p-paginator-prev:not(.p-disabled):not(.p-highlight):hover,
  ::ng-deep .p-paginator .p-paginator-next:not(.p-disabled):not(.p-highlight):hover,
  ::ng-deep .p-paginator .p-paginator-last:not(.p-disabled):not(.p-highlight):hover {
    background: #e9ecef;
    border-color: transparent;
    color: #495057;
    border-radius: 50%;
  }

  ::ng-deep .p-paginator .p-paginator-first:not(.p-disabled):not(.p-highlight),
  ::ng-deep .p-paginator .p-paginator-prev:not(.p-disabled):not(.p-highlight),
  ::ng-deep .p-paginator .p-paginator-next:not(.p-disabled):not(.p-highlight),
  ::ng-deep .p-paginator .p-paginator-last:not(.p-disabled):not(.p-highlight) {
    border-radius: 50%;
    background-color: #f4f5f8;
  }

  ::ng-deep .p-paginator .p-paginator-pages .p-paginator-page {
    font-size: 14px;
    &.p-highlight {
      background: #4b3f72;
      border-color: #e3f2fd;
      color: #ffffff;
      border-radius: 50%;
      pointer-events: none;
    }
  }

  ::ng-deep .p-datatable .p-datatable-thead > tr > th {
    height: 20px;
    color: #4b3f72;
    font-family: Poppins;
    font-size: 14px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 21px;
    border-bottom: none;
    padding: 0.5rem 0.5rem;
    background-color: #ecedf6 !important;
  }

  ::ng-deep .p-datatable .p-datatable-thead tr > th:first-child,
  ::ng-deep .p-datatable .p-datatable-tbody tr > td:first-child {
    padding-left: 1rem;
  }

  ::ng-deep .p-datatable .p-datatable-tbody > tr > td {
    height: 20px;
    width: 123px;
    color: #000000;
    font-family: Poppins;
    font-size: 12px;
    letter-spacing: 0;
    line-height: 15px;
    padding: 0.5rem 0.5rem;
    font-weight: 500;
  }

  ::ng-deep .pi-icon .pi {
    font-size: 0.5rem;
  }

  ::ng-deep .svg-icon-white .svg-icon svg g [fill] {
    fill: white !important;
  }

  ::ng-deep .p-datatable .p-sortable-column.p-highlight:hover .p-sortable-column-icon {
    color: #4b3f72;
  }

  ::ng-deep .p-datatable .p-sortable-column.p-highlight .p-sortable-column-icon {
    color: #4b3f72;
  }

  ::ng-deep .p-datatable .p-sortable-column.p-highlight,
  .p-datatable .p-sortable-column.p-highlight:hover {
    background: #f8f9fa;
    color: #4b3f72;
  }

  ::ng-deep .p-datatable .p-sortable-column.p-highlight:hover {
    background: #f8f9fa;
    color: #4b3f72;
  }

  ::ng-deep .p-datatable .p-sortable-column:focus {
    box-shadow: inset 0 0 0 0.2rem #4b3f72;
    outline: 0 none;
  }
  ::ng-deep .p-paginator .p-dropdown {
    width: 91px;
  }

  ::ng-deep .p-datatable-tbody tr:last-child > td {
    border-bottom: none;
  }

  ::ng-deep .p-paginator {
    display: flex !important;
    justify-content: flex-start !important;
    border: none;
    margin-top: 15px;

    .p-paginator-current {
      position: absolute;
      right: 0;
      color: #575962;
      font-size: 14px;
      letter-spacing: 0;
      font-weight: 500;
      cursor: default;
    }

    .p-paginator-rpp-options {
      margin-left: 20px;
      height: 37px;
      width: 100px;
      border-radius: 20px;
      background-color: #f4f5f8;
      border: none;
    }
    .p-dropdown-label.p-inputtext {
      display: flex;
      align-items: center;
      padding-left: 15px;
      font-size: 14px;
    }

    .p-dropdown-trigger-icon {
      padding-right: 20px;
    }
  }

  @media (max-width: 500px) {
    ::ng-deep .p-paginator-current {
      bottom: 0;
    }

    ::ng-deep .p-paginator-rpp-options {
      margin-top: 10px;
    }
  }

  ::ng-deep .p-inputtext:enabled:focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 0 0 1px #4b3f72;
    border-color: #4b3f72;
  }

  ::ng-deep .p-inputtext:enabled:hover {
    border-color: #4b3f72;
  }
  .badge {
    height: 20px;
    width: 56px;
    color: #dc2f44;
    font-family: Poppins;
    font-size: 14px;
    letter-spacing: 0;
    line-height: 21px;
    text-align: center;
  }

  .center-align {
    text-align: center !important;
  }

  ::ng-deep .p-datatable .p-datatable-loading-overlay {
    top: 44px;
    background-color: transparent;
  }

  ::ng-deep .p-calendar .p-datepicker {
    position: absolute;
    width: 400px;
  }

  ::ng-deep .confirm-dialog-expense .p-dialog {
    width: 30vw;
  }
}

::ng-deep .p-datatable-responsive-scroll > .p-datatable-wrapper {
  overflow-x: initial !important;
}

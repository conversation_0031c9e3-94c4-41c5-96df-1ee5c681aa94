export interface FilterReport {
  employeeGroup?: string;
  consultantType?: string;
}

export interface QueryFilterParams {
  order_by?: string;
  open_positions?: boolean;
  util_start_date?: string;
  util_end_date?: string;
  start_date?: string;
  end_date?: string;
  start_date_lte?: string;
  start_date_lt?: string;
  end_date_gte?: string;
  projection_detail_level?: string;
  visibility?: string;
  employee_status?: string;
  effective_date?: Date | string;
  effective_date1?: Date | string;
  effective_date2?: Date | string;
  tags?: string;
}

export class IFilter {
  util_start_date?: string;
  util_end_date?: string;
  start_date?: string;
  end_date?: string;
  employee_type_name?: {
    employee_type?: {
      name?: string;
    };
  };
  name?: {
    name?: string;
    value?: string;
  };
  employee_ids?: string;
  employee_id?: number;
  date?: Date;
  pid?: number;
  position_ids?: number;
  open_positions?: boolean = false;
  customer_ids?: string;
  client?: {
    name?: string;
    value?: string;
  };
  customer_name?: {
    name?: string;
    value?: string;
  };
  emp_grp_name?: string;
  emp_grp_value?: string;
  project_grp_name?: string;
  project_grp_value?: string;
  client_grp_name?: string;
  client_grp_value?: string;
  project_ids?: string;
  project_name?: {
    name?: string;
    value?: string;
  };
  value?: any;
  period?: string;
  dataType?: string;
  effective_date?: Date;
  dataType1?: string;
  effective_date1?: Date | any;
  dataType2?: string;
  effective_date2?: Date | any;
  year?: number;
  quarter?: string;
  rollingOption?: string;
  start_date_lte?: string;
  start_date_lt?: string;
  end_date_gte?: string;
  start_month?: Date;
  end_month?: Date;
  project_statuses?: string;
  statuses?: string;
  status?: string[];
  order_by?: string;
  include_utilizations?: boolean;
  include_work_exceptions?: boolean;
  include_pl_plugs?: boolean;
  employee_status?: string;
  selectedUtilizationToView?: string;
  clientProjectIds?: number[];
  employeeName?: string[] = [];
  ClientName?: string[];
  projectName?: string[];
  showClientFilter?: boolean;
  clientName?: string[];
  selectedClient?: string[];
  showProjectFilter?: boolean;
  projectNameList?: string[];
  selectedProject?: string[];
  showCustomVariancePeriod?: boolean;
  variance?: boolean = true;
  varianceValue?: number;
  tags?: string;
  groupByClients?: boolean;
  groupByEmployee?: boolean;
  groupByProject?: boolean;
  selectedAllocationType?: string[];
  selectedoverAllocationValue?: number;
  selectedunderAllocationValue?: number;
  filterView?: string;
  selectedColumns?: any;
  filterFromMangeEmployee?: boolean;
  limit?: number;
  offset?: number;
}

export interface EmployeeWiseData {
  employees?: EmployeeData[];
}

export class EmployeeData {
  employee?: {
    first_name?: string;
    last_name?: string;
    employee_type?: { name?: string };
    utilizations?: Utilization[];
    email?: string;
    id?: number;
    start_date?: string;
    end_date?: string;
    hourly_cost?: string;
    tags?: string[];
    position_types?: PositionType[];
    name: string;
  };
  showHelpIconData?: boolean = false;
}

export interface Utilization {
  utilization?: {
    month?: number;
    year?: string;
    start_date?: string;
    utilization?: string;
    amount?: string;
    employee_hours?: string;
    position_hours?: string;
    display_utilization?: string;
  };
}

export interface PositionType {
  position_type?: {
    id?: number;
    name?: string;
  };
}

export class TableHeader {
  monthLabel?: string;
  month?: number;
  year?: string;
  id?: number;
  field?: string;
  sort?: boolean;
}

export interface EmployeeTypesList {
  employee_types: EmployeeType[];
}

export interface EmployeeType {
  employee_type: {
    id?: number;
    name?;
    string;
    is_employee?: boolean;
    is_utilized?: boolean;
  };
}
export interface Region {
  region: {
    id?: number;
    name?;
    string;
  };
}

export interface RegionList {
  regions: Region[];
}

export interface GlobalDetail {
  global_detail: {
    extended_fields: any;
    name: string;
  };
}
export interface GlobalDetailList {
  global_details: GlobalDetail[];
}

export interface Group {
  label?: string;
  value?: string | any;
}

export const MONTH_NAMES = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];

export interface OpenPositionReportList {
  positions?: OpenPositionReport[];
}

export interface ExtendedFields {
  [key: string]: string;
}

export interface OpenPositionReport {
  position?: {
    id?: number;
    name?: string;
    daily_billable_hours?: string;
    weekly_billable_hours?: string;
    cost?: string | number;
    project?: {
      id: number;
      name?: string;
      status: string;
      customer?: {
        name?: string;
        extended_fields: any;
      };
      extended_fields: any;
    };
    start_date?: string;
    end_date?: string;
    employee?: {
      first_name?: string;
      last_name?: string;
      id?: number;
      employeeFullName: string;
      extended_fields: any;
    };
    type?: string;
    tags?: string[];
    bill_rate?: number;
    validated_monthly_positions?: ValidatedMonthlyPositions[];
    extended_fields?: ExtendedFields;
    total?: {
      cost?: string;
      actual_hours?: string;
      billable_hours?: string;
      contract_cost?: string;
      direct_cost?: string;
      expenses?: string;
      gross_margin?: string;
      open_positions_cost?: string;
      revenue?: string;
      work_exception_cost?: string;
      percent_gross_margin?: string;
    };
  };
}

export interface ValidatedMonthlyPositions {
  validated_monthly_position?: {
    id?: number;
    pid?: number;
    month?: number;
    year?: number;
    open_positions_cost?: string;
    billable_hours?: string;
    contract_cost?: string;
    cost?: string;
    direct_cost?: string;
    expenses?: string;
    gross_margin?: string;
    percent_gross_margin?: string;
    revenue?: string;
    work_exception_cost?: string;
    actual_hours?: string;
  };
}

export interface PositionList {
  positions?: Position[];
}

export interface Position {
  position?: {
    name?: string;
    id?: string;
  };
}

export interface ProjectList {
  projects?: Project[];
}

export interface Project {
  project?: {
    amount: string;
    billing_type: string;
    contacts: any[];
    customer: Customer;
    deleted_date: any;
    description: string;
    effective_end_date: any;
    effective_start_date: string;
    end_date: string;
    extended_fields: ExtendedFields;
    id: number;
    last_validated_date: string;
    name: string;
    pid: number;
    region: string;
    start_date: string;
    status: string;
    tags: string[];
  };
}

export interface Customer {
  id: number;
  name: string;
}

export interface ExtendedFields {
  ext_newprice: string;
  ext_old_price: string;
  ext_oldprice: string;
  ext_testing2newprice: string;
  ext_testprice: string;
}

export interface Ids {
  employee_ids?: [];
  customer_ids?: [];
  project_ids?: [];
  data?: {};
}

export interface PNLPlugs {
  pnl_plugs: PNLPlug[];
}
export interface PNLPlug {
  pnl_plug: {
    amount?: string;
    id?: number;
    reason?: string;
    type?: string;
    year?: number;
    month?: number;
  };
}

export enum CalendarPageViewConfigType {
  year = 'year',
  month = 'month',
  week = 'week',
  day = 'day'
}

export interface ResorceType {
  id: number;
  name: string;
  customer: string;
  color: string;
}

export interface EmployeeObject {
  id: number;
  name: string;
}

export interface EmployeeDataTYPE {
  employee: EmployeeObject;
}

export interface Data {
  employees: EmployeeData[];
}

export interface EmployeeLookupApiResponse {
  data: Data;
}

export interface PdfColumnHeaderObj {
  dataKey: string;
  title: string;
  bydefaultSelected: boolean;
}

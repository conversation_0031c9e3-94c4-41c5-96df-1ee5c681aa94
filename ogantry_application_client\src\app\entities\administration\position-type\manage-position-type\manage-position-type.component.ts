import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { PositionType } from '@entities/administration/administration.model';
import { AdministrationService } from '@entities/administration/administration.service';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { ButtonParams } from '@shared/models';
import { AlertType } from '@shared/models/alert-type.enum';
import { LayoutUtilsService } from '@shared/services/layout-utils.service';
import { LazyLoadEvent } from 'primeng/api';
@Component({
  selector: 'app-manage-position-type',
  templateUrl: './manage-position-type.component.html',
  styleUrls: ['./manage-position-type.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ManagePositionTypeComponent extends SflBaseComponent implements OnInit {
  positionTypes: PositionType[] = [];
  loading = false;
  cardTitle = 'Manage Skill Sets';
  cardSubTitle = null;
  buttons: ButtonParams[] = [
    {
      btnClass: 'btn-save',
      btnText: 'Add New',
      redirectPath: this.appRoutes.CREATE_POSITION_TYPE,
      permissions: [this.permissionModules.MANAGE_POSITION_TYPE]
    }
  ];

  deleteTypeId: number;
  showDeleteDialog = false;
  constructor(private readonly adminService: AdministrationService, private readonly cdf: ChangeDetectorRef, private readonly layoutUtilsService: LayoutUtilsService) {
    super();
  }

  ngOnInit(): void {}

  getPositionTypes(event?: LazyLoadEvent) {
    this.positionTypes = [];
    this.loading = true;
    this.cdf.detectChanges();
    this.subscriptionManager.add(
      this.adminService.getPositionTypes().subscribe(
        (res) => {
          this.loading = false;
          if (res?.body?.data?.position_types) {
            this.positionTypes = res.body.data.position_types;
          }
          this.cdf.detectChanges();
        },
        () => (this.loading = false)
      )
    );
  }

  confirmDeleteType(id: number) {
    this.deleteTypeId = id;
    this.showDeleteDialog = true;
  }

  closeModal() {
    this.deleteTypeId = null;
    this.showDeleteDialog = false;
  }

  deleteType() {
    this.isSubmitting = true;
    this.subscriptionManager.add(
      this.adminService.deletePositionType(this.deleteTypeId).subscribe(
        (res) => {
          this.isSubmitting = false;
          this.closeModal();
          this.layoutUtilsService.showActionNotification('Skill Set has been archived successsfully', AlertType.Success);
          this.getPositionTypes();
          this.cdf.detectChanges();
        },
        () => {
          this.isSubmitting = false;
          this.layoutUtilsService.showActionNotification('Cannot delete a skill set that is attached to a position.', AlertType.Error);
          this.cdf.detectChanges();
        }
      )
    );
  }
}

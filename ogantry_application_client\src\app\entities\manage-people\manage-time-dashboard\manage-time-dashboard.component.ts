import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { AdministrationService } from '@entities/administration/administration.service';
import { UtilizationService } from '@entities/utilization-management/utilization.service';
import { LayoutUtilsService } from '@shared/services/layout-utils.service';
import { ManagePeopleService } from '../manage-people.service';
import { MangeTimeService } from '../manage-time/mange-time.service';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { EmployeeData, EmployeeLookupApiResponse } from '@entities/utilization-management/utilization.model';
import { DatePipe } from '@angular/common';
import { BookTimeOffComponent } from '@shared/components/book-time-off/book-time-off.component';
import { TimesheetButtonStatus } from '@shared/enum/mange-timesheet.enum';
import { DAY_SUFFIX, TIMESHEET_RANGE, VALUE_OF_WEEK_DAYS } from '@shared/constants/manage-timesheet.constant';
import { CalcAccrualObj, HoursWithStatus, LabelValueObj, TimeEntries } from '../mange-people.model';
import { forkJoin } from 'rxjs';

@Component({
  selector: 'app-manage-time-dashboard',
  templateUrl: './manage-time-dashboard.component.html',
  styleUrls: ['./manage-time-dashboard.component.scss'],
  providers: [DatePipe]
})
export class ManageTimeDashboardComponent extends SflBaseComponent implements OnInit {
  cardTitle = 'Timesheet Dashboard';
  employeeData: EmployeeData[];
  selectedEmployeeIds: LabelValueObj;
  loading = false;
  workExceptionsData = [];
  totalTimeOffDays: any;
  position = [];
  storedCurrentPostion = [];
  calculateTotalHoursCurrent = 0;
  storedPreviousPostion = [];
  calculateTotalHoursPrevious = 0;
  bookTimeOffPopUp = false;
  positionStartDate = new Date();
  currentDate = new Date();
  timeSheetStatus = TimesheetButtonStatus.Not_Submitted;
  totalDays = 0;
  totalWorkingHour = [];

  totalWorkingHourKeys = {
    NOT_SUBMITTED: 0,
    SUBMITTED: 0,
    APPROVED: 0
  };

  timeOff = 'timeOff';
  positionDate = 'positionDate';
  positionDueDate = 'positionDueDate';
  valueOfEight = 8;
  weekDays = 7;
  biWeekRange = 15;
  biweekDays = 14;
  startDayValue = 1;
  totalWeeks = 1;
  selectedTimesheetRange = TIMESHEET_RANGE.WEEKLY;
  startDate: Date;
  endDate: Date;
  currentTimesheetRange = [new Date(), new Date()];
  previousTimesheetRange = [new Date(), new Date()];
  currentTimesheetHoursWithStatus: HoursWithStatus[] = [];
  previousTimesheetHoursWithStatus: HoursWithStatus[] = [];
  accrualData: CalcAccrualObj[];
  usedAccrualsList: string[] = [];

  objectKeys = Object.keys;

  @ViewChild('bookTimeOFF') bookTimeOff: BookTimeOffComponent;

  constructor(
    private readonly managePeopleService: ManagePeopleService,
    private readonly router: Router,
    private readonly datePipe: DatePipe,
    private readonly cdr: ChangeDetectorRef,
    private readonly mangeTimeService: MangeTimeService,
    private readonly adminService: AdministrationService
  ) {
    super();
  }

  ngOnInit(): void {
    this.loading$.next(false);
    this.getEmployeeDetails();
    this.getGlobalDetail();
  }

  getUserRole(): string {
    return localStorage.getItem('role');
  }

  getUserEmail(): string {
    return localStorage.getItem('userEmail');
  }

  checkUserINSystem(userEmail: string): Promise<EmployeeLookupApiResponse> {
    return this.mangeTimeService.employeeLookup(userEmail).toPromise();
  }

  async getEmployeeDetails(): Promise<void> {
    const userEmail = this.getUserEmail();
    if (userEmail) {
      const userData = await this.checkUserINSystem(userEmail);
      this.employeeData = userData?.data?.employees ? userData.data.employees : [];
      if (userData?.data?.employees && userData?.data?.employees[0]?.employee.id && userData?.data?.employees[0]?.employee?.name) {
        this.selectedEmployeeIds = {
          label: userData?.data?.employees[0]?.employee?.name,
          value: userData?.data?.employees[0]?.employee?.id
        };
        this.calcAccrual();
        this.getPosition(this.selectedEmployeeIds?.value);
        this.getWorkExceptionByEmployee(this.selectedEmployeeIds?.value);
      }
    }
  }

  getPosition(employeeId: number): void {
    this.loading$.next(true);
    const date = this.datePipe.transform(new Date(), this.appConstants.format);
    const queryFilter = {
      end_date_gte: date
    };
    this.managePeopleService.getPositionActiveEmployee(employeeId, queryFilter).subscribe(
      (res) => {
        res?.data?.positions?.forEach((position) => {
          this.position.push(position?.position);
          if (this.positionStartDate > new Date(position?.position?.start_date)) {
            this.positionStartDate = this.formatDateToTimezone(position?.position?.start_date + this.appConstants.defaultTimePart);
          }
        });
        this.getTimesheet({ employee_id: this.selectedEmployeeIds?.value });
        this.countTotalDays(this.positionStartDate, this.currentDate);
        this.loading$.next(false);
        this.cdr.detectChanges();
      },
      () => this.loading$.next(false)
    );
  }

  getWorkExceptionByEmployee(employeeId: number): void {
    this.loading$.next(true);
    const queryFilter = {
      employee_ids: employeeId
    };
    this.subscriptionManager.add(
      this.adminService.getManageWorkExceptions(queryFilter).subscribe({
        next: (res) => {
          this.workExceptionsData = res?.body?.data?.work_exceptions ?? [];
          this.workExceptionDaysCount();
          this.cdr.detectChanges();
        },
        complete: () => {
          this.loading$.next(false);
        }
      })
    );
  }

  calcAccrual(): void {
    this.loading$.next(true);
    const param = {
      employee_id: this.selectedEmployeeIds.value,
      start_date: this.datePipe.transform(new Date(), this.appConstants.format)
    };

    this.managePeopleService.calcAccruals(param).subscribe({
      next: (res) => {
        this.accrualData = res?.data?.calculations;
        this.usedAccrualsList = [];
        this.accrualData?.forEach((data) => {
          const usedBalance = Number(data.calculation.amount_used);
          const usedFullDays = Math.floor(usedBalance / this.valueOfEight);
          const remainingHoursInDay = usedBalance % this.valueOfEight;
          data.calculation.amount_used = `${usedFullDays} days${remainingHoursInDay ? ` ${remainingHoursInDay.toFixed(0)} hours` : ''}`;

          const remainingBalance = Number(data.calculation.remaining_balance);
          const remainingFullDays = Math.floor(remainingBalance / this.valueOfEight);
          const remainingHours = remainingBalance % this.valueOfEight;
          data.calculation.remaining_balance = `${remainingFullDays} days${remainingHours ? ` ${remainingHours.toFixed(0)} hours` : ''}`;

          this.usedAccrualsList.push(data?.calculation?.work_exception_type?.name);
        });
        this.loading$.next(false);
        this.cdr.detectChanges();
      },
      error: () => this.loading$.next(false)
    });
  }

  getTimesheet(params?: any): void {
    this.loading$.next(true);

    const dates = [
      {
        time_entry_start_date: this.datePipe.transform(this.previousTimesheetRange[0], this.appConstants.format),
        time_entry_end_date: this.datePipe.transform(this.previousTimesheetRange[1], this.appConstants.format) // or any other end date
      },
      {
        time_entry_start_date: this.datePipe.transform(this.currentTimesheetRange[0], this.appConstants.format),
        time_entry_end_date: this.datePipe.transform(this.currentTimesheetRange[1], this.appConstants.format)
      }
    ];

    const observables = dates.map((dateRange) => {
      const param = {
        ...dateRange,
        ...params
      };
      return this.mangeTimeService.getTimesheet(param);
    });

    this.subscriptionManager.add(
      forkJoin(observables).subscribe({
        next: (res) => {
          if (res[1]?.data?.positions) {
            this.storedCurrentPostion = res[1].data.positions;
            this.calculateTotalHoursCurrent = this.calculateTotalHoursTimesheet(res[1].data);
          }
          if (res[0]?.data?.positions) {
            this.storedPreviousPostion = res[0].data.positions;
            this.calculateTotalHoursPrevious = this.calculateTotalHoursTimesheet(res[0].data);
          }
          this.processPositions(res[0].data.positions, false);
          this.processPositions(res[1].data.positions, true);
        },
        complete: () => {
          this.loading$.next(false);
        }
      })
    );
  }

  processPositions(positions: any[], isCurrentTimesheet: boolean): void {
    positions.forEach((position) => {
      const endDate = this.datePipe.transform(position?.position?.end_date, this.appConstants.format);
      const todayDate = this.datePipe.transform(new Date(), this.appConstants.format);

      if (endDate >= todayDate && position?.position?.time_entries) {
        this.checkTimesheetStatus(position.position.time_entries, isCurrentTimesheet);
      }
    });
  }

  isUpcomingTimeOff(exceptionDate: string | Date): boolean {
    const date = this.datePipe.transform(new Date(), this.appConstants.format);
    const workExceptionDate = this.datePipe.transform(exceptionDate, this.appConstants.format);
    return date < workExceptionDate;
  }

  workExceptionDaysCount(): void {
    let result = {};
    this.workExceptionsData?.forEach((entry) => {
      const workExceptionName = entry?.work_exception?.work_exception_type?.name;
      const hours = entry?.work_exception?.hours;
      const date = this.datePipe.transform(entry?.work_exception?.date, this.appConstants.format);

      if (result[workExceptionName]) {
        result[workExceptionName] += hours;
      } else {
        result[workExceptionName] = hours;
      }
    });

    for (let key in result) {
      const totalHours = result[key];
      const fullDays = Math.floor(totalHours / this.valueOfEight);
      const remainingHours = (totalHours % this.valueOfEight).toFixed(0);

      result[key] = {
        fullDays: fullDays,
        remainingHours: remainingHours
      };
    }

    this.totalTimeOffDays = Object.keys(result)?.map((key) => ({
      type: key,
      fullDays: result[key]?.fullDays,
      remainingHours: result[key]?.remainingHours
    }));
  }

  dateFormateWithSuffix(dateString: string | Date, formateFor: string): string {
    const formatDate = this.datePipe.transform(dateString, this.appConstants.format);
    const date = this.formatDateToTimezone(formatDate + this.appConstants.defaultTimePart);
    const day = date.getDate();
    const year = date.getFullYear();

    const suffix = this.getDaySuffix(day);

    if (formateFor === this.timeOff) {
      const month = date.toLocaleString('default', { month: 'long' });
      return `${day}${suffix} ${month}, ${year}`;
    }
    if (formateFor === this.positionDate) {
      const month = date.toLocaleString('default', { month: 'short' });
      return `${day}${suffix} ${month}, ${year}`;
    }
    if (formateFor === this.positionDueDate) {
      const month = date.toLocaleString('default', { month: 'short' });
      return `${month} ${day}${suffix}`;
    }
  }

  getDaySuffix(day: number): string {
    if (day >= 11 && day <= 13) {
      return DAY_SUFFIX.TH;
    }
    switch (day % 10) {
      case 1:
        return DAY_SUFFIX.ST;
      case 2:
        return DAY_SUFFIX.ND;
      case 3:
        return DAY_SUFFIX.RD;
      default:
        return DAY_SUFFIX.TH;
    }
  }

  formatDateToTimezone(date: Date | string, timezone = null): Date {
    if (!timezone) {
      timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    }

    return new Date(new Date(date).toLocaleString('en-US', { timeZone: timezone }));
  }

  checkTimesheetStatus(time_entries: TimeEntries[], currentTimesheet = true): void {
    let totalWorkingHourWithStatus: HoursWithStatus = {
      NOT_SUBMITTED: 0,
      SUBMITTED: 0,
      APPROVED: 0,
      REJECTED: 0
    };

    time_entries &&
      time_entries?.forEach((time_entry) => {
        if (time_entry?.time_entry?.status === TimesheetButtonStatus.Draft) {
          totalWorkingHourWithStatus.NOT_SUBMITTED += parseFloat(time_entry?.time_entry?.hours?.toString());
        } else if (time_entry?.time_entry?.status === TimesheetButtonStatus.SUBMITTED) {
          totalWorkingHourWithStatus.SUBMITTED += parseFloat(time_entry?.time_entry?.hours?.toString());
        } else if (time_entry?.time_entry?.status === TimesheetButtonStatus.APPROVED) {
          totalWorkingHourWithStatus.APPROVED += parseFloat(time_entry?.time_entry?.hours?.toString());
        } else if (time_entry?.time_entry?.status === TimesheetButtonStatus.REJECTED) {
          totalWorkingHourWithStatus.REJECTED += parseFloat(time_entry?.time_entry?.hours?.toString());
        }
      });

    this.totalWorkingHour.push(totalWorkingHourWithStatus);
    currentTimesheet ? this.currentTimesheetHoursWithStatus.push(totalWorkingHourWithStatus) : this.previousTimesheetHoursWithStatus.push(totalWorkingHourWithStatus);
  }

  closeBookTimeOffModel(value?: boolean): void {
    this.bookTimeOffPopUp = false;
    if (value) {
      this.getWorkExceptionByEmployee(this.selectedEmployeeIds?.value);
    }
    this.bookTimeOff.resetForm();
  }

  saveBookTimeOffAPI(): void {
    this.bookTimeOff?.onSave();
  }

  countTotalDays(startDate: string | Date, endDate: string | Date): void {
    const start = new Date(startDate);
    const end = new Date(endDate);
    let count = 0;

    while (start <= end) {
      const dayOfWeek = start.getDay();

      if (dayOfWeek !== 6 && dayOfWeek !== 0) {
        count++;
      }
      start.setDate(start.getDate() + 1);
    }
    this.totalDays = count;
  }

  generateMonthArray(): void {
    const year = this.currentDate.getFullYear();
    const month = this.currentDate.getMonth();
    const daysInMonth = new Date(year, month + 1, 0).getDate();

    const previousMonth = month === 0 ? 11 : month - 1;
    const previousYear = month === 0 ? year - 1 : year;

    const previousDaysInMonth = new Date(previousYear, previousMonth + 1, 0).getDate();

    if (TIMESHEET_RANGE.MONTHLY === this.selectedTimesheetRange) {
      this.startDate = new Date(year, month, 1);
      this.endDate = new Date(year, month, daysInMonth);
      this.currentTimesheetRange = [this.startDate, this.endDate];

      const previousStartDate = new Date(previousYear, previousMonth, 1);
      const previousEndDate = new Date(previousYear, previousMonth, previousDaysInMonth);
      this.previousTimesheetRange = [previousStartDate, previousEndDate];
    }
  }

  generateTimeRangeArray(inputDate: Date): void {
    if (TIMESHEET_RANGE.WEEKLY === this.selectedTimesheetRange || TIMESHEET_RANGE.BIWEEKLY === this.selectedTimesheetRange) {
      let daysInRange = this.selectedTimesheetRange === TIMESHEET_RANGE.WEEKLY ? this.weekDays * this.totalWeeks : this.biWeekRange;

      const currentDayRange = this.generateDateRanges(inputDate, this.selectedTimesheetRange);

      if (currentDayRange && currentDayRange?.selectedRange?.length) {
        this.startDate = new Date(currentDayRange?.selectedRange[0] + this.appConstants.defaultTimePart);
        this.endDate = new Date(currentDayRange?.selectedRange[currentDayRange?.selectedRange?.length - 1] + this.appConstants.defaultTimePart);
        this.currentTimesheetRange = [this.startDate, this.endDate];
      }

      const previousDate = new Date(inputDate);
      previousDate.setDate(inputDate.getDate() - daysInRange);

      const previousDayRange = this.generateDateRanges(previousDate, this.selectedTimesheetRange);

      const previousTimesheetLength = previousDayRange?.selectedRange?.length;
      if (previousDayRange?.selectedRange && previousTimesheetLength) {
        const previousStartDate = new Date(previousDayRange?.selectedRange[0] + this.appConstants.defaultTimePart);
        const previousEndDate = new Date(previousDayRange?.selectedRange[previousTimesheetLength - 1] + this.appConstants.defaultTimePart);
        this.previousTimesheetRange = [previousStartDate, previousEndDate];
      }
    }
  }

  generateDateRanges(selectedDate, returnType = TIMESHEET_RANGE.WEEKLY) {
    if (![TIMESHEET_RANGE.WEEKLY, TIMESHEET_RANGE.BIWEEKLY]?.includes(returnType)) {
      return;
    }

    let daysInRange = returnType === TIMESHEET_RANGE.WEEKLY ? this.weekDays * this.totalWeeks : this.biWeekRange;

    const selected = this.formatDateToTimezone(selectedDate);
    const compareDate = this.formatDateToTimezone(selectedDate);

    const start = new Date(selected);

    if (returnType === TIMESHEET_RANGE.BIWEEKLY) {
      if (selected?.getDate() <= this.biWeekRange) {
        start?.setDate(1);
      } else {
        start?.setDate(this.biWeekRange + 1);
        const monthEndDate = new Date(start?.getFullYear(), start?.getMonth() + 1, 0);
        daysInRange = monthEndDate?.getDate() - this.biWeekRange;
      }
    } else {
      if (selected?.getDate() && selected?.getDay()) {
        if (selected?.getDay() < this.startDayValue) {
          start?.setDate(selected.getDate() - selected.getDay() - this.weekDays + this.startDayValue);
        } else {
          start?.setDate(selected.getDate() - selected.getDay() + this.startDayValue);
        }
      }
    }

    const ranges = [];
    let currentStart = new Date(start);

    for (let i = 0; i < 10; i++) {
      const rangeDays = [];
      for (let j = 0; j < daysInRange; j++) {
        const currentDate = new Date(currentStart);
        currentDate?.setDate(currentStart?.getDate() + j);
        rangeDays.push(this.formattedDateForRange(currentDate));
      }
      ranges.push(rangeDays);
      currentStart?.setDate(currentStart?.getDate() + daysInRange);
    }

    const selectedRange = ranges?.find((range) => {
      const rangeStart = new Date(this.datePipe?.transform(range[0], this.appConstants.format));
      const rangeEnd = new Date(this.datePipe?.transform(range[range?.length - 1], this.appConstants.format));
      return (
        new Date(this.datePipe?.transform(compareDate, this.appConstants.format)) >= rangeStart &&
        new Date(this.datePipe?.transform(compareDate, this.appConstants.format)) <= rangeEnd
      );
    });

    return { ranges, selectedRange };
  }

  formattedDateForRange(currentDate: Date): string {
    const year = currentDate.getFullYear();
    const month = String(currentDate.getMonth() + 1).padStart(2, '0'); // Months are 0-indexed
    const day = String(currentDate.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  getGlobalDetail(): void {
    this.subscriptionManager.add(
      this.adminService.getGlobalDetailsFields().subscribe({
        next: (res) => {
          if (res?.data?.global_details) {
            res?.data?.global_details?.forEach((field) => {
              const timesheetConfig = field?.global_detail?.extended_fields?.timesheetConfig;
              if (timesheetConfig) {
                this.selectedTimesheetRange = timesheetConfig?.frequency;
                if (this.selectedTimesheetRange === TIMESHEET_RANGE.WEEKLY) {
                  const startDay = timesheetConfig?.startDay?.toUpperCase();
                  this.startDayValue = VALUE_OF_WEEK_DAYS[startDay];
                  this.totalWeeks = timesheetConfig?.weeks;
                }
                this.selectedTimesheetRange === TIMESHEET_RANGE.MONTHLY ? this.generateMonthArray() : this.generateTimeRangeArray(this.currentDate);
              }
            });
          }
        }
      })
    );
  }

  navigateToCreateTimesheet(date?: Date | string): void {
    this.router.navigate([this.appRoutes.CREATE_TIMESHEET], { queryParams: { date: date } });
  }

  calculateTotalHours(postion): number {
    return postion?.time_entries?.reduce((total, entry) => {
      const hours = parseFloat(entry.time_entry.hours) || 0; // Ensure hours are parsed as float
      return total + hours;
    }, 0);
  }

  calculateTotalHoursTimesheet(data): number {
    let totalHours = 0;

    data.positions.forEach((positionObj) => {
      const timeEntries = positionObj.position.time_entries;

      timeEntries.forEach((entry) => {
        const hours = parseFloat(entry.time_entry.hours) || 0; // Ensure hours are parsed as a float
        totalHours += hours;
      });
    });

    return totalHours;
  }

  isNotUsedAccruals(exceptionType: string): boolean {
    return !this.usedAccrualsList.find((acc) => acc === exceptionType);
  }
}

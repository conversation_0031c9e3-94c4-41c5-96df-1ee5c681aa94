<mat-sidenav-container id="openpositionReport">
  <mat-sidenav class="detail-sidebar" #sidebarFilter mode="over" position="end" disableClose>
    <div class="card card-custom add-contact">
      <app-card-header [cardTitle]="filterCardTitle" [buttons]="filterButtons" [cardLabelClass]="'mb-0'"></app-card-header>
      <div class="card-body create-card">
        <form class="form" autocomplete="off" novalidate="novalidate" id="applyFilter" (ngSubmit)="filterReport()">
          <div class="row">
            <div class="col-12">
              <app-shared-range-selector
                [showDateRangeRadioButton]="dataFilter.start_month && dataFilter.end_month ? true : false"
                [rollingRadioBtnSelected]="dataFilter.rollingOption ? true : false"
                [yearRadioBtnSelected]="dataFilter.year ? true : false"
                [quaterRadioBtnSelected]="dataFilter.quarter ? true : false"
                [start_month]="dataFilter.start_month"
                [end_month]="dataFilter.end_month"
                [selectedYear]="dataFilter.year"
                [selectedQuarterValue]="dataFilter.quarter"
                [selectedRollingOption]="dataFilter.rollingOption"
                (selected_end_date)="endMonthSelected($event)"
                (selected_start_date)="startMonthSelected($event)"
                (updatedYearValue)="dataFilter.year = $event"
                (updatedRollingValue)="dataFilter.rollingOption = $event"
                (updatedquaterValue)="dataFilter.quarter = $event"
                [dateError]="dateError"
                [dateRequired]="dateRequired"
              ></app-shared-range-selector>
              <div class="PL-border">
                <div class="form-group">
                  <label class="form-label background" id="project-status">Project Status</label>
                </div>
                <div class="form-group first dropdown p-2">
                  <p-multiSelect
                    #multiSelectComp
                    [(ngModel)]="projectStatus"
                    [overlayVisible]="true"
                    [showHeader]="false"
                    [options]="statuses"
                    [ngModelOptions]="{ standalone: true }"
                    placeholder="Status"
                    display="chip"
                    (onChange)="statusSelected($event)"
                  >
                  </p-multiSelect>
                </div>
              </div>
              <div class="PL-border">
                <div class="form-group">
                  <label class="form-label background">
                    <div class="width-65">
                      {{ showClientFilter ? 'Saved Client Filter' : 'Client' }}
                    </div>
                    <div class="form-group pt-0 save-filter-radio">
                      <div class="form-check form-check-inline mt-2">
                        <input
                          class="form-check-input"
                          type="radio"
                          id="saveClientFilter"
                          [checked]="showClientFilter"
                          autocomplete="off"
                          (click)="showSaveClientFilterSelected()"
                        />
                        <label class="mb-0" for="saveClientFilter"> Use Saved Client Filter </label>
                      </div>
                    </div>
                  </label>
                </div>
                <div class="form-group first dropdown p-2" *ngIf="showClientFilter">
                  <p-dropdown
                    appendTo="body"
                    placeholder="Select"
                    [(ngModel)]="dataFilter.customer_name"
                    [options]="clientGroup"
                    [ngModelOptions]="{ standalone: true }"
                    (ngModelChange)="getClientIds()"
                  >
                  </p-dropdown>
                </div>
                <div class="form-group first dropdown p-2" *ngIf="!showClientFilter">
                  <p-multiSelect
                    appendTo="body"
                    #multiSelectComp2
                    [(ngModel)]="clientName"
                    [overlayVisible]="false"
                    [showHeader]="false"
                    [options]="client"
                    [ngModelOptions]="{ standalone: true }"
                    placeholder="Select"
                    display="chip"
                    (onChange)="clientSelected($event)"
                  >
                  </p-multiSelect>
                </div>
              </div>
              <div class="PL-border">
                <div class="form-group">
                  <label class="form-label background">
                    <div class="width-65">
                      {{ showProjectFilter ? 'Saved Project Filter' : 'Project' }}
                    </div>
                    <div class="form-group pt-0 save-filter-radio">
                      <div class="form-check form-check-inline mt-2">
                        <input
                          class="form-check-input"
                          type="radio"
                          id="saveProjFilter"
                          [checked]="showProjectFilter"
                          autocomplete="off"
                          (click)="showSaveProjectFilterSelected()"
                        />
                        <label class="mb-0" for="saveProjFilter"> Use Saved Project Filter </label>
                      </div>
                    </div>
                  </label>
                </div>
                <div class="form-group first dropdown p-2" *ngIf="showProjectFilter">
                  <p-dropdown
                    appendTo="body"
                    placeholder="Select"
                    [(ngModel)]="dataFilter.project_name"
                    [options]="projectGroup"
                    [ngModelOptions]="{ standalone: true }"
                    (ngModelChange)="getProjectIds()"
                  >
                  </p-dropdown>
                </div>
                <div class="form-group first dropdown p-2" *ngIf="!showProjectFilter">
                  <p-multiSelect
                    appendTo="body"
                    #multiSelectComp3
                    [(ngModel)]="projectName"
                    [overlayVisible]="false"
                    [showHeader]="false"
                    [options]="projectList"
                    [ngModelOptions]="{ standalone: true }"
                    placeholder="Select"
                    display="chip"
                    (onChange)="projectSelected($event)"
                  >
                  </p-multiSelect>
                </div>
              </div>

              <!-- <div class="form-group first dropdown">
                <label class="form-label">Client</label>
                <p-dropdown appendTo="body" [(ngModel)]="dataFilter.client" name="projectGroup" [options]="client" placeholder="Select" (onChange)="onClientSelect()"></p-dropdown>
              </div> -->
              <!-- <div class="form-group first dropdown">
                <label class="form-label">Saved Client Filter</label>
                <p-dropdown appendTo="body" [options]="clientGroup" placeholder="Select" [(ngModel)]="dataFilter.customer_name" [ngModelOptions]="{standalone: true}" (onChange)="getClientIds()">
                </p-dropdown>
              </div>
              <div class="form-group first dropdown">
                <label class="form-label">Saved Project Filter</label>
                <p-dropdown appendTo="body" [options]="projectGroup" placeholder="Select" [(ngModel)]="dataFilter.project_name" [ngModelOptions]="{standalone: true}" (onChange)="getProjectIds()">
                </p-dropdown>
              </div> -->

              <!-- <div class="form-group first dropdown">
                <label class="form-label">Project Status</label>
                <p-multiSelect #multiSelectComp [(ngModel)]="projectStatus" [overlayVisible]="true" [showHeader]="false" [options]="statuses" [ngModelOptions]="{standalone: true}" placeholder="Status"
                  display="chip" (onChange)="statusSelected($event)">
                </p-multiSelect>
              </div> -->

              <div class="PL-border">
                <div class="form-group">
                  <label class="form-label background">Position</label>
                </div>
                <div class="form-group first dropdown p-2">
                  <p-dropdown
                    appendTo="body"
                    [options]="positionList"
                    [ngModel]="selectedPositionOption"
                    [ngModelOptions]="{ standalone: true }"
                    class="position-dropdown-wrapper"
                    placeholder="Select"
                    showClear="true"
                    optionLabel="label"
                    (onChange)="onPositionChange($event)"
                  >
                    <ng-template pTemplate="selectedItem">
                      <div class="flex align-items-center gap-2" *ngIf="selectedPositionOption">
                        <div>{{ selectedPositionOption.label }}</div>
                      </div>
                    </ng-template>
                  </p-dropdown>
                </div>
              </div>

              <!-- <div class="form-group first dropdown">
                <label class="form-label">Position</label>
                <p-dropdown appendTo="body" [options]="positionList" placeholder="Select" [(ngModel)]="dataFilter.position_ids" [ngModelOptions]="{standalone: true}"></p-dropdown>
              </div> -->

              <!-- <div class="row mt-4">
                <div class="col-6">
                  <div class="form-group pb-0">
                    <label class="form-label">Start Month</label>
                  </div>
                  <div class="form-group pt-0 range-calender">
                    <p-calendar appendTo="body" view="month" [showIcon]="true" [(ngModel)]="dataFilter.start_month" [ngModelOptions]="{standalone: true}" dateFormat="MM/yy" [yearNavigator]="true"
                      yearRange="2000:2030" [readonlyInput]="true" inputId="monthpicker1" placeholder="Month/Year" (onSelect)="startMonthSelected()">
                    </p-calendar>
                  </div>
                </div>
                <div class="col-6">
                  <div class="form-group pb-0">
                    <label class="form-label">End Month</label>
                  </div>
                  <div class="form-group pt-0 range-calender">
                    <p-calendar appendTo="body" view="month" [showIcon]="true" [(ngModel)]="dataFilter.end_month" [ngModelOptions]="{standalone: true}" dateFormat="MM/yy" [yearNavigator]="true"
                      yearRange="2000:2030" [readonlyInput]="true" inputId="monthpicker2" placeholder="Month/Year" (onSelect)="endMonthSelected()">
                    </p-calendar>
                  </div>
                </div>

              </div>
              <div class="form-group pb-0" *ngIf="dateError">
                <small class="form-text text-danger"> Start Month should be less than End Month</small>
              </div>
              <div class="form-group pb-0" *ngIf="dateRequired">
                <small class="form-text text-danger"> Start Month and End Month is required</small>
              </div> -->
            </div>
          </div>
          <div class="form-group d-flex flex-wrap justify-content-end align-items-center mt-2 pb-4">
            <button id="addContactCancel" type="button" class="btn-cancel" (click)="clearFilters()">Reset</button>
            <button id="addContactSubmit" type="submit" [isSubmitting]="isSubmitting" class="btn-save">Apply</button>
          </div>
        </form>
      </div>
    </div>
  </mat-sidenav>
  <mat-sidenav-content class="detail-sidebar-content">
    <ng-container *ngTemplateOutlet="positionReport"></ng-container>
  </mat-sidenav-content>
</mat-sidenav-container>
<ng-template #positionReport>
  <div class="card card-custom gutter-b">
    <app-card-header
      [cardTitle]="cardTitle"
      [cardSubTitle]="cardSubTitle"
      [showSplitButton]="true"
      [splitButtonDropDownOption]="splitButtonDropDownOption"
      [exportButton]="exportButtons"
    ></app-card-header>
    <div class="card-body table" [ngClass]="positionReportData.length < 14 ? 'active' : ''">
      <div class="d-flex justify-content-between align-items-center">
        <div>
          <app-selected-filter-tags
            (filterReset)="resetFilter()"
            (saveFilter)="onSaveFilter()"
            [tags]="tags"
            (onCancel)="onCloseFromFilterPanel($event)"
            (onRemoveStatus)="onRemoveStatusFilter($event)"
          ></app-selected-filter-tags>
        </div>
        <div>
          <div class="float-right mr-4 py-2 pointer" (click)="isShowHideColumns = !isShowHideColumns" #coulmnToggel>
            <app-filter-icon-shared></app-filter-icon-shared>
          </div>
          <div class="popup-column card">
            <app-filter-table-fields
              *ngIf="isShowHideColumns"
              [selectedColumns]="selectedColumns"
              [frozenCols]="hideAbleColumns"
              dynamicBindingKey="monthLabel"
              (onSelectColumChange)="onSelectColumsChange($event)"
            ></app-filter-table-fields>
          </div>
        </div>
      </div>

      <ng-container *ngIf="showApplyMsg">
        <p class="filter-note ml-5 apply-filter-msg">Please Apply Filter To Load The Lost Revenue Report.</p>
      </ng-container>
      <ng-container *ngIf="customePtableSpinner && !resizeFlag">
        <div class="custome-p-table-spinner">
          <em class="pi pi-spin pi-spinner" style="font-size: 2rem"></em>
        </div>
      </ng-container>
      <div class="scrollable-content" *ngIf="!resizeFlag; else resize_table">
        <p-table
          [sortField]="sortFieldName"
          [resizableColumns]="true"
          [sortOrder]="sortOrderNumber"
          [scrollHeight]="height"
          (onSort)="sortColumn()"
          [columns]="tableHeaders"
          [scrollable]="true"
          class="pb-2"
          #positionReportTable
          [value]="positionReportData"
          [rows]="10"
          dataKey="id"
          scrollDirection="both"
          *ngIf="!loading && !customePtableSpinner"
        >
          <ng-template pTemplate="header" let-columns>
            <tr>
              <ng-container *ngFor="let col of frozenCols; let i = index">
                <th class="fix-col-3" pFrozenColumn [pSortableColumn]="col?.field" [pSortableColumnDisabled]="!col.sort">
                  {{ col.monthLabel }}
                  <span *ngIf="col?.sort">
                    <p-sortIcon [field]="col?.field"></p-sortIcon>
                  </span>
                </th>
              </ng-container>
              <ng-container *ngFor="let col of hideAbleColumns">
                <ng-container *ngIf="_pCols?.includes(col.field)">
                  <th
                    class="fix-col-3"
                    [pSortableColumn]="col?.field"
                    [pSortableColumnDisabled]="!col.sort"
                    [ngClass]="{
                      'text-number-right start-end-width-wrapper': col?.field === 'position.start_date' || col?.field === 'position.end_date'
                    }"
                    pResizableColumn
                  >
                    {{ col.monthLabel }}
                    <span *ngIf="col?.sort">
                      <p-sortIcon [field]="col?.field"></p-sortIcon>
                    </span>
                  </th>
                </ng-container>
              </ng-container>
              <th
                class="text-number-right"
                *ngFor="let col of columns"
                [ngClass]="getDynCssClass(columns)"
                pResizableColumn
                [pSortableColumn]="col?.field"
                [pSortableColumnDisabled]="!col.sort"
              >
                {{ col.monthLabel }}
                <span *ngIf="col?.sort">
                  <p-sortIcon [field]="col?.field"></p-sortIcon>
                </span>
              </th>
              <th id="actions" class="header-width-action pt-0"></th>
            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-data let-columns="columns">
            <tr>
              <ng-container>
                <td [ngClass]="getFixCssClass(columns)" pFrozenColumn id="showData">
                  <div class="d-flex">
                    <div class="ellipses" [title]="data?.position?.project?.customer?.name">
                      {{ data?.position?.project?.customer?.name }}
                    </div>
                  </div>
                </td>
                <!-- (clickOutside)="data.showHelpIconData = false" -->
                <td [ngClass]="getFixCssClass(columns)" pFrozenColumn id="showData">
                  <div class="d-flex">
                    <div class="ellipses" [title]="data?.position?.project?.name">
                      {{ data?.position?.project?.name }}
                    </div>
                    <!-- <ng-template #popOver>
                      <div *isFetchingData="loading$">
                        <div [ngStyle]="styleObj.heading">
                          CLIENT NAME
                        </div>
                        <div [ngStyle]="styleObj.subHeading">
                          {{data?.position?.customer_name || '-'}}
                        </div>
                        <div [ngStyle]="styleObj.heading">
                          CONTACT PERSON
                        </div>
                        <div *ngIf="data?.position?.contact_person?.length else noContact">
                          <div [ngStyle]="styleObj.subHeading" *ngFor="let contacts of data?.position?.contact_person">
                            - {{contacts?.contact?.name || '-'}}
                          </div>
                        </div>
                        <ng-template #noContact>
                          -
                        </ng-template>
                      </div>
                    </ng-template> -->
                    <!-- <div>
                      <a type="button" popoverClass="my-custom-class" placement="right" container="body" (click)="showHelpData(data)" [ngbPopover]="popOver">
                        <fa-icon [icon]="'info-circle'" class="ml-1 help-icon"></fa-icon>
                      </a>
                    </div> -->
                  </div>
                </td>
                <td [ngClass]="getFixCssClass(columns)" pFrozenColumn>
                  <div class="d-flex">
                    <a
                      class="ellipses"
                      *hasAnyPermission="permissionModules.MANAGE_PROJECT; disableEvent: true"
                      [routerLink]="[appRoutes.EDIT_PROJECT, data?.position?.project?.id]"
                    >
                      <span [title]="data?.position?.name">{{ data?.position?.name }}</span>
                    </a>
                  </div>
                </td>
                <td [ngClass]="getFixCssClass(columns)" *ngIf="_pCols?.includes('position.project.status')">
                  <div class="d-flex">
                    <a
                      class="ellipses"
                      *hasAnyPermission="permissionModules.MANAGE_PROJECT; disableEvent: true"
                      [routerLink]="[appRoutes.EDIT_PROJECT, data?.position?.project?.id]"
                    >
                      <span [title]="data?.position?.project?.status">{{ data?.position?.project?.status }}</span>
                    </a>
                  </div>
                </td>
                <td [ngClass]="getFixCssClass(columns)" class="text-number-right start-end-width-wrapper" *ngIf="_pCols?.includes('position.start_date')">
                  {{ data?.position?.start_date | date : 'MM/dd/yyyy' }}
                </td>
                <td [ngClass]="getFixCssClass(columns)" class="text-number-right start-end-width-wrapper" *ngIf="_pCols?.includes('position.end_date')">
                  {{ data?.position?.end_date | date : 'MM/dd/yyyy' }}
                </td>
              </ng-container>
              <ng-container *ngFor="let col of columns">
                <td class="text-number-right" [ngClass]="getDynCssClass(columns)" *ngIf="col?.id">${{ getValues(col, data) | addCommasToNumbers }}</td>
              </ng-container>
              <td class="header-width-action"></td>
            </tr>
          </ng-template>
          <ng-template pTemplate="emptymessage">
            <tr *ngIf="!showApplyMsg && positionReportData.length === 0 && !(loading$ | async)">
              <td colspan="4" class="center-align">No Data found.</td>
            </tr>
          </ng-template>
        </p-table>
      </div>
      <ng-template #resize_table>
        <div class="scrollable-content">
          <p-table
            responsiveLayout="scroll"
            [columns]="tableHeaders"
            scrollDirection="both"
            [scrollable]="true"
            [scrollHeight]="height"
            class="pb-2"
            #positionReportTable
            [value]="positionReportData"
            [rows]="10"
            [sortField]="sortFieldName"
            [sortOrder]="sortOrderNumber"
            [lazy]="true"
            (onSort)="sortColumn()"
            (onLazyLoad)="loadReport($event)"
            [resizableColumns]="true"
            dataKey="id"
            [loading]="loading"
          >
            <ng-template pTemplate="header" let-columns>
              <tr>
                <ng-container *ngFor="let col of frozenCols; let i = index">
                  <th
                    class="fix-col-3"
                    pFrozenColumn
                    [pSortableColumn]="col?.field"
                    [pSortableColumnDisabled]="!col.sort"
                    [ngClass]="{
                      'text-number-right start-end-width-wrapper': col?.field === 'start_date' || col?.field === 'end_date'
                    }"
                    pResizableColumn
                  >
                    {{ col.monthLabel }}
                    <span *ngIf="col?.sort">
                      <p-sortIcon [field]="col?.field"></p-sortIcon>
                    </span>
                  </th>
                </ng-container>
                <ng-container *ngFor="let col of hideAbleColumns">
                  <ng-container *ngIf="_pCols?.includes(col.field)">
                    <th
                      class="fix-col-3"
                      [pSortableColumn]="col?.field"
                      [pSortableColumnDisabled]="!col.sort"
                      [ngClass]="{
                        'text-number-right start-end-width-wrapper': col?.field === 'start_date' || col?.field === 'end_date'
                      }"
                      pResizableColumn
                    >
                      {{ col.monthLabel }}
                      <span *ngIf="col?.sort">
                        <p-sortIcon [field]="col?.field"></p-sortIcon>
                      </span>
                    </th>
                  </ng-container>
                </ng-container>
                <th *ngFor="let col of columns" style="width: 130px" [pSortableColumn]="col?.field" [pSortableColumnDisabled]="!col.sort" class="text-right">
                  {{ col.monthLabel }}
                  <span *ngIf="col?.sort">
                    <p-sortIcon [field]="col?.field"></p-sortIcon>
                  </span>
                </th>
                <th id="actionsresizetable" class="header-width-action pt-0"></th>
              </tr>
            </ng-template>
            <ng-template pTemplate="body" let-data let-columns="columns">
              <tr>
                <ng-container>
                  <td [ngClass]="getFixCssClass(columns)" pFrozenColumn id="showData">
                    <div class="d-flex">
                      <div class="ellipses" [title]="data?.position?.project?.customer?.name">
                        {{ data?.position?.project?.customer?.name }}
                      </div>
                    </div>
                  </td>
                  <td [ngClass]="getFixCssClass(columns)" pFrozenColumn id="showData">
                    <div class="d-flex">
                      <div class="ellipses" [title]="data?.position?.project?.name">
                        {{ data?.position?.project?.name }}
                      </div>
                      <!-- <ng-template #popOver>
                        <div *isFetchingData="loading$">
                          <div [ngStyle]="styleObj.heading">
                            CLIENT NAME
                          </div>
                          <div [ngStyle]="styleObj.subHeading">
                            {{data?.position?.customer_name || '-'}}
                          </div>
                          <div [ngStyle]="styleObj.heading">
                            CONTACT PERSON
                          </div>
                          <div *ngIf="data?.position?.contact_person?.length else noContact">
                            <div [ngStyle]="styleObj.subHeading" *ngFor="let contacts of data?.position?.contact_person">
                              - {{contacts?.contact?.name || '-'}}
                            </div>
                          </div>
                          <ng-template #noContact>
                            -
                          </ng-template>
                        </div>
                      </ng-template>
                      <div>
                        <a type="button" popoverClass="my-custom-class" placement="right" container="body" (click)="showHelpData(data)" [ngbPopover]="popOver">
                          <fa-icon [icon]="'info-circle'" class="ml-1 help-icon"></fa-icon>
                        </a>
                      </div> -->
                    </div>
                  </td>
                  <td [ngClass]="getFixCssClass(columns)" pFrozenColumn>
                    <div class="d-flex">
                      <a
                        class="ellipses"
                        *hasAnyPermission="permissionModules.MANAGE_PROJECT; disableEvent: true"
                        [routerLink]="[appRoutes.EDIT_PROJECT, data?.position?.project?.id]"
                      >
                        <span [title]="data?.position?.name">{{ data?.position?.name }}</span>
                      </a>
                    </div>
                  </td>
                  <td [ngClass]="getFixCssClass(columns)" *ngIf="_pCols?.includes('project_status')">
                    <div class="d-flex">
                      <a
                        class="ellipses"
                        *hasAnyPermission="permissionModules.MANAGE_PROJECT; disableEvent: true"
                        [routerLink]="[appRoutes.EDIT_PROJECT, data?.position?.project?.id]"
                      >
                        <span [title]="data?.position?.project?.status">{{ data?.position?.project?.status }}</span>
                      </a>
                    </div>
                  </td>
                  <td [ngClass]="getFixCssClass(columns)" class="text-number-right" *ngIf="_pCols?.includes('start_date')">
                    {{ data?.position?.start_date | date : 'MM/dd/yyyy' }}
                  </td>
                  <td [ngClass]="getFixCssClass(columns)" class="text-number-right" *ngIf="_pCols?.includes('end_date')">
                    {{ data?.position?.end_date | date : 'MM/dd/yyyy' }}
                  </td>
                </ng-container>
                <ng-container *ngFor="let col of columns">
                  <td class="text-number-right" *ngIf="col?.id" [ngClass]="getDynCssClass(columns)">${{ getValues(col, data) | addCommasToNumbers }}</td>
                </ng-container>
                <td class="header-width-action"></td>
              </tr>
            </ng-template>
            <ng-template pTemplate="emptymessage">
              <tr *ngIf="!showApplyMsg && positionReportData.length === 0 && !(loading$ | async)">
                <td colspan="4" class="center-align">No Data found.</td>
              </tr>
            </ng-template>
          </p-table>
        </div>
      </ng-template>
    </div>
  </div>
</ng-template>
<p-dialog
  dismissableMask="true"
  backdrop="false"
  position="top-right"
  [(visible)]="showFilterListDialog"
  [modal]="true"
  class="filter-dialog"
  [draggable]="false"
  [resizable]="false"
>
  <ng-template pTemplate>
    <div class="filter-listing" *ngIf="showSavedFilter">
      <input type="search" class="form-control mb-2" placeholder="Search" (input)="searchFilters($event.target.value)" />
      <div *hasAnyPermission="permissionModules.VIEW_SHARE_FILTER; hideTemplate: true" class="title">Shared Filters</div>
      <ng-container *hasAnyPermission="permissionModules.VIEW_SHARE_FILTER; hideTemplate: true">
        <span *ngIf="sharedFilters?.length; else noData">
          <div
            class="form-check filter-body"
            *ngFor="let filterOption of sharedFilters"
            [ngClass]="{
              'selected-filter': filterOption?.query_filter?.id === selectedFilterFormControl?.value?.query_filter?.id
            }"
          >
            <label class="form-check-label">
              <input
                [formControl]="selectedFilterFormControl"
                (ngModelChange)="applySelectedFilterAndUpdateUrl()"
                type="radio"
                class="form-check-input custom-radio"
                [value]="filterOption"
                name="filteroption"
              />{{ filterOption?.query_filter?.name }}
            </label>
            <div class="filter-icons">
              <ng-container *hasAnyPermission="permissionModules.MANAGE_SHARE_FILTER; hideTemplate: true">
                <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary copy-icon" (click)="copyLinkToTheFilter(filterOption?.query_filter?.id)">
                  <em title="Copy Filter Link" class="fa-regular fa-copy"></em>
                </a>
                <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary share-icon" (click)="unShareFilter(filterOption)">
                  <em class="fa-solid fa-share" title="UnShare Filter"></em>
                </a>
                <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary">
                  <span title="Edit" [inlineSVG]="'assets/media/svg/icons/edit.svg'" cacheSVG="true" class="svg-icon svg-icon-md" (click)="editFilter(filterOption)"> </span>
                </a>
                <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary">
                  <span title="Delete" [inlineSVG]="'assets/media/svg/icons/delete.svg'" cacheSVG="true" class="svg-icon svg-icon-md" (click)="deleteFilter(filterOption)"> </span>
                </a>
              </ng-container>
            </div>
          </div>
        </span>
      </ng-container>

      <ng-template #noData>
        <div>-</div>
      </ng-template>
      <div class="title">My Filters</div>
      <span *ngIf="myFilters?.length; else noData">
        <div
          class="form-check filter-body"
          *ngFor="let filterOption of myFilters"
          [ngClass]="{
            'selected-filter': filterOption?.query_filter?.id === selectedFilterFormControl?.value?.query_filter?.id
          }"
        >
          <label class="form-check-label">
            <input
              [formControl]="selectedFilterFormControl"
              (ngModelChange)="applySelectedFilterAndUpdateUrl()"
              type="radio"
              class="form-check-input custom-radio"
              [value]="filterOption"
              name="filteroption"
            />{{ filterOption?.query_filter?.name }}
          </label>
          <div class="filter-icons">
            <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary copy-icon" (click)="copyLinkToTheFilter(filterOption?.query_filter?.id)">
              <em title="Copy Filter Link" class="fa-regular fa-copy"></em>
            </a>
            <a
              class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary"
              (click)="shareFilter(filterOption)"
              *hasAnyPermission="permissionModules.MANAGE_SHARE_FILTER; hideTemplate: true"
            >
              <em class="fa-solid fa-share" title="Share Filter"></em>
            </a>
            <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary">
              <span title="Edit" [inlineSVG]="'assets/media/svg/icons/edit.svg'" cacheSVG="true" class="svg-icon svg-icon-md" (click)="editFilter(filterOption)"> </span>
            </a>
            <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary">
              <span title="Delete" [inlineSVG]="'assets/media/svg/icons/delete.svg'" cacheSVG="true" class="svg-icon svg-icon-md" (click)="deleteFilter(filterOption)"> </span>
            </a>
          </div>
        </div>
      </span>
    </div>
  </ng-template>
</p-dialog>

<p-dialog header="Rename" [(visible)]="showEditDialog" [modal]="true" class="confirm-dialog" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
  <div class="form-group first" *ngIf="editFilterObj">
    <input type="text" pInputText class="form-control custom" [(ngModel)]="editFilterObj.query_filter.name" (input)="inputFilterName()" />
    <small *ngIf="showNameError" class="form-text text-danger"> Name is required</small>
  </div>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center">
      <button type="button" class="btn-cancel" (click)="closeModal()">Cancel</button>
      <button type="button" class="btn-save" (click)="saveEditFilter()" [isSubmitting]="isSubmitting">Update</button>
    </div>
  </ng-template>
</p-dialog>

<p-dialog header="Delete" [(visible)]="showDeleteDialog" [modal]="true" class="confirm-dialog" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
  <h5 class="p-m-0 delete-title">Do you want to delete "{{ deleteFilterObj?.query_filter?.name }}" Filter?</h5>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center">
      <button type="button" class="btn-cancel" (click)="closeModal()">No</button>
      <button type="button" class="btn-save" (click)="saveDeleteFilter()" [isSubmitting]="isSubmitting">Yes</button>
    </div>
  </ng-template>
</p-dialog>

<p-dialog [header]="shareFilterObj?.header" [(visible)]="showShareDialog" [modal]="true" class="confirm-dialog" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
  <h5 class="p-m-0 delete-title">{{ shareFilterObj?.text }}</h5>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center">
      <button type="button" class="btn-cancel" (click)="closeModal()">No</button>
      <button type="button" class="btn-save" (click)="saveShareFilter()" [isSubmitting]="isSubmitting">Yes</button>
    </div>
  </ng-template>
</p-dialog>

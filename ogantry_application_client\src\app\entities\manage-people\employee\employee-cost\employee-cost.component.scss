#employeeCost {
  ::ng-deep .range-calender .p-calendar-w-btn {
    height: 100%;
    width: 100%;
  }
  .warning-background {
    background-color: #f8f8ff;
    height: 42px;
  }
  .warning-msg {
    color: #4b3f72;
    font-family: Poppins;
    font-size: 14px;
    font-weight: 500;
    letter-spacing: 0;

    line-height: 21px;

    // width: 1285px;
  }

  ::ng-deep .range-calender .p-calendar-w-btn .p-inputtext {
    border: none !important;
    background-color: #f8f8ff !important;
  }

  ::ng-deep .p-calendar .p-inputtext {
    height: 60px !important;
  }
  ::ng-deep .p-inputgroup {
    width: 100%;
    border-radius: 9px !important;
    border: none !important;
    background-color: #f8f8ff !important;
    min-height: 60px !important;
  }

  ::ng-deep .p-inputgroup .p-inputtext {
    border: none !important;
    background-color: #f8f8ff !important;
    padding-left: 1rem;
  }

  ::ng-deep .p-inputgroup-addon {
    border: 0 !important;
    width: 4rem;
  }

  ::ng-deep .p-inputtext:enabled:focus {
    box-shadow: none;
  }
  ::ng-deep .p-button {
    background: #4b3f72;
    border-color: #4b3f72;
  }
  ::ng-deep .p-button.p-button-icon-only {
    width: 4.357rem;
  }

  ::ng-deep .p-button:enabled:hover {
    background: #574985;
    border-color: #574985;
  }
  .scrollable-content {
    max-height: calc(100vh - 50px) !important;
    overflow-y: auto !important;
  }
  .title {
    height: 23px;
    width: 118px;
    color: #000000;
    font-family: Poppins;
    font-size: 16px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 25px;
  }
  .subTitle {
    height: 20px;
    width: 123px;
    color: #5e5e5e;
    font-family: Poppins;
    font-size: 14px;
    letter-spacing: 0;
    line-height: 21px;
  }
}
@media (max-width: 1199.98px) {
  .card-body {
    padding: 15px !important;
  }
  .create-card {
    padding: 15px !important;
  }
  .travel-input {
    width: 100% !important;
  }
}
::ng-deep .p-datatable .p-datatable-thead > tr > th {
  height: 20px;
  color: #4b3f72;
  font-family: Poppins;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 21px;
  border-bottom: none;
  padding: 0.5rem 0.5rem;
  background-color: #ecedf6 !important;
}

::ng-deep .p-datatable .p-datatable-thead tr > th:first-child,
::ng-deep .p-datatable .p-datatable-tbody tr > td:first-child {
  padding-left: 1rem;
}

::ng-deep .p-datatable .p-datatable-tbody > tr > td {
  height: 20px;
  width: 123px;
  color: #000000;
  font-family: Poppins;
  font-size: 12px;
  letter-spacing: 0;
  line-height: 15px;
  padding: 0.5rem 0.5rem;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
::ng-deep .confirm-dialog-expense .p-dialog {
  width: 30vw;
}

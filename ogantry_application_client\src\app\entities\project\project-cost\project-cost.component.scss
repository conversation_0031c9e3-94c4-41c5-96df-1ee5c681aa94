@import "../../../../assets/sass/components/_variables.bootstrap.scss" ;
#projectCost {
  .card-title {
    height: 25px;
    color: #4a4a4a;
    font-family: Poppins;
    font-size: 12px;
    letter-spacing: 0;
    line-height: 25px;
    margin-bottom: 0.5rem;
    text-align: center;
  }

  .card-subtitle {
    height: 25px;
    color: #000000 !important;
    font-family: Poppins;
    font-size: 16px;
    font-weight: 600;
    letter-spacing: 0;
    line-height: 25px;
    margin-top: 0.5rem;
    text-align: center;
  }
  .margin {
    color: #119da4 !important;
  }
  .card {
    border: 0;
    background-color: #f8f8ff;
    margin: 1rem;
    margin-bottom: 0rem;
  }

  .card-body {
    padding: 0rem;
    margin-left: 1rem;
    margin-right: 1rem;
  }

  .extraBg {
    height: 26px;
    background-color: #f3f3fc;
  }
}

@media (max-width: 580px) {
  #projectCost {
    .proCost-mobile {
      display: flex !important;
      flex-wrap: wrap !important;
      padding-top: 10px !important;
      .flex-column {
        min-width: 33%;
        .card-subtitle,
        .card-title {
          margin-top: 0 !important;
          line-height: 1;
          margin-bottom: 0 !important;
        }
      }
    }
  }
}

@media (max-width: 767px) and (min-width: 581px) {
  #projectCost {
    .proCost-mobile {
      padding-top: 10px !important;
      display: flex !important;
      flex-wrap: wrap !important;
      .flex-column {
        min-width: 25%;
        .card-subtitle,
        .card-title {
          margin-top: 0 !important;
          line-height: 1;
          margin-bottom: 0 !important;
        }
      }
    }
  }
}
.recalculate {
  height: 15px;
  width: 77px;
  color: #4b3f72;
  font-family: Poppins;
  font-size: 10px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 16px;
  text-align: right;
  padding-top: 0.5rem;
}

.row-height{
  height: 50px !important;
}

.recalculating-message {
  color: $primary;
  font-weight: 500;
  animation: pulse 1.5s infinite;
}

.rotating-icon {
  display: inline-block;
  animation: rotate 3s infinite linear;
  color: $primary;
  font-size: 16px;
  vertical-align: middle;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  50%{
    transform:  rotate(180deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

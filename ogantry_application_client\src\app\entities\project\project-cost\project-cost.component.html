<div id="projectCost">
  <div class="card fixed-content">
    <div class="card-body">
      <div class="row">
        <div class="col-12 d-flex proCost-mobile">
          <div class="col px-0 d-flex flex-column">
            <h6 class="card-subtitle">
              <ng-template [ngTemplateOutlet]="showFormattedData" [ngTemplateOutletContext]="{ data: projectCost?.revenue }"></ng-template>
            </h6>
            <h6 class="card-title">Revenue</h6>
          </div>
          <div class="col px-0 d-flex flex-column">
            <h6 class="card-subtitle">
              <ng-template [ngTemplateOutlet]="showFormattedData" [ngTemplateOutletContext]="{ data: projectCost?.direct_cost }"></ng-template>
            </h6>
            <h6 class="card-title">Direct Labor</h6>
          </div>
          <div class="col px-0 d-flex flex-column">
            <h6 class="card-subtitle">
              <ng-template [ngTemplateOutlet]="showFormattedData" [ngTemplateOutletContext]="{ data: projectCost?.contract_cost }"></ng-template>
            </h6>
            <h6 class="card-title">Contract Labor</h6>
          </div>
          <div class="col px-0 d-flex flex-column">
            <h6 class="card-subtitle">
              <ng-template [ngTemplateOutlet]="showFormattedData" [ngTemplateOutletContext]="{ data: projectCost?.open_positions_cost }"></ng-template>
            </h6>
            <h6 class="card-title">Open Position Labor</h6>
          </div>
          <div class="col px-0 d-flex flex-column">
            <h6 class="card-subtitle">
              <ng-template [ngTemplateOutlet]="showFormattedData" [ngTemplateOutletContext]="{ data: projectCost?.work_exception_cost }"></ng-template>
            </h6>
            <h6 class="card-title">Work Exceptions</h6>
          </div>
          <div class="col px-0 d-flex flex-column">
            <h6 class="card-subtitle">
              <ng-template [ngTemplateOutlet]="showFormattedData" [ngTemplateOutletContext]="{ data: projectCost?.expenses }"></ng-template>
            </h6>
            <h6 class="card-title">Expenses</h6>
          </div>
          <div class="col px-0 d-flex flex-column">
            <h6 class="card-subtitle">
              <ng-template [ngTemplateOutlet]="showFormattedData" [ngTemplateOutletContext]="{ data: projectCost?.net_profit }"></ng-template>
            </h6>
            <h6 class="card-title">Profit</h6>
          </div>
          <div class="col px-0 d-flex flex-column">
            <h6 class="card-subtitle">
              <ng-template [ngTemplateOutlet]="showFormattedData" [ngTemplateOutletContext]="{ data: projectCost?.percent_gross_margin * 100, suffix: '%' }"></ng-template>
            </h6>
            <h6 class="card-title">Margin</h6>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-12 row-height extraBg pl-0 pr-3 recalculate align-content-center">
          <ng-container *ngIf="projectId && !recalculating && currentProjectState === currentProjectStateValue.Paused">
            <span class="card-title mr-4 mb-0">Financial calculations are paused while edits are in progress</span>
            <button class="btn btn-primary btn-outline-primary animate-on-click mr-2" (click)="resumeRecalculating()">Recalculate</button>
          </ng-container>
          <span [ngClass]="{ 'recalculating-message': recalculating }" *ngIf="recalculating">
            <i class="material-icons rotating-icon mr-2">hourglass_empty</i>
            <ng-container>{{ rotatingMessage }}...</ng-container>
          </span>
        </div>
      </div>
    </div>
  </div>
</div>

<ng-template #showFormattedData let-data="data" let-suffix="suffix">
  {{ loading || !projectCost || currentProjectState !== validateState ? '- -' : suffix ? (data | addCommasToNumbers) + '%' : (data | addCommasToNumbers : '$') }}
</ng-template>

import { AdminOnlyPermission, PermissionModules } from '../permission.enum';

export interface ButtonParams {
  btnText?: string;
  btnClass?: string;
  action?: Function;
  redirectPath?: string;
  btnType?: string;
  btnIcon?: string;
  loading?: boolean;
  permissions?: PermissionModules[] | AdminOnlyPermission[];
  btnSvg?: string;
  isActive?: boolean;
  isSwitcherButton?: boolean;
  viewType?: string;
  title?: string;
}

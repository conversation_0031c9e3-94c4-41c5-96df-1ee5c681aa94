<div class="card card-custom gutter-b" id="manageHolidays">
  <app-card-header [cardTitle]="cardTitle" [cardSubTitle]="cardSubTitle" [buttons]="buttons"></app-card-header>
  <div class="card-body">
    <p-table
      responsiveLayout="scroll"
      #dt
      [value]="holidayData"
      [lazy]="true"
      (onLazyLoad)="getHolidays($event)"
      dataKey="id"
      [loading]="loading"
      styleClass="p-datatable-customers"
      [filterDelay]="0"
    >
      <ng-template pTemplate="header">
        <tr>
          <th id="description" pSortableColumn="description" class="header-width">Holiday Name<p-sortIcon field="description"></p-sortIcon></th>
          <th id="date" pSortableColumn="date" class="header-width">Date<p-sortIcon field="date"></p-sortIcon></th>
          <th id="type" class="header-width">Type</th>
          <th id="actions" class="header-width text-center" colspan="2">Actions</th>
        </tr>
        <tr>
          <th id="description_search">
            <span class="p-input-icon-right">
              <em class="pi pi-times" *ngIf="dataFilter.description_search" (click)="clearFilter('description_search')"></em>
              <input pInputText type="text" class="p-column-filter" placeholder="Description" [(ngModel)]="dataFilter.description_search" (input)="filter()" />
            </span>
          </th>
          <th id="date">
            <p-calendar
              appendTo="body"
              placeholder="Date"
              [(ngModel)]="dataFilter.date"
              showButtonBar="true"
              (onClearClick)="clearStartDate()"
              [readonlyInput]="true"
              (onSelect)="filter()"
            ></p-calendar>
          </th>
          <th id="empty-header"></th>
          <th id="filter-operation">
            <div class="d-flex justify-content-center" id="resetFilter">
              <a title="Reset Filter" (click)="resetFilters()" class="btn btn-icon btn-icon-light btn-sm icon-background">
                <span [inlineSVG]="'assets/media/svg/icons/clear-filter.svg'" cacheSVG="true" class="svg-icon svg-icon-md"> </span>
              </a>
            </div>
          </th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-exception>
        <tr>
          <td>
            <span [title]="exception?.holiday?.description">{{ exception?.holiday?.description }}</span>
          </td>
          <td class="text-number-right">{{ exception?.holiday?.date | date : 'MM/dd/yyyy' }}</td>
          <td>{{ exception?.holiday?.work_exception_type?.name }}</td>
          <td class="text-center" colspan="2">
            <a
              class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary"
              *hasAnyPermission="permissionModules.MANAGE_HOLIDAY; disableEvent: true"
              (click)="confirmDeleteHoliday(exception?.holiday?.id)"
            >
              <span title="Archive" [inlineSVG]="'assets/media/svg/icons/archive.svg'" cacheSVG="true" class="svg-icon svg-icon-md"> </span>
            </a>
          </td>
        </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="4" class="center-align">No Holidays found.</td>
        </tr>
      </ng-template>
    </p-table>
  </div>
</div>

<p-dialog header="Delete Holiday" [(visible)]="showDeleteDialog" [modal]="true" class="confirm-dialog" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
  <h5 class="p-m-0">Are you sure you want to delete this holiday?</h5>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center">
      <button type="button" class="btn-cancel" (click)="closeModal()">No</button>
      <button type="button" class="btn-save" (click)="deleteHoliday()" [isSubmitting]="isSubmitting">Yes</button>
    </div>
  </ng-template>
</p-dialog>

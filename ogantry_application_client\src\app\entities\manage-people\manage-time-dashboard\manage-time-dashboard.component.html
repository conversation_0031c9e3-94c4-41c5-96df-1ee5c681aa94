<div id="manage-time-dashboard">
  <div class="card card-custom gutter-b">
    <app-card-header [cardTitle]="cardTitle"></app-card-header>
  </div>
  <div class="container" *isFetchingData="loading$">
    <div class="row mt-4 align-items-baseline">
      <div class="col-md-6">
        <div class="m-3">
          <div class="timesheet cursor-pointer" (click)="navigateToCreateTimesheet()">
            <div class="title-link d-flex justify-content-between">
              <label class="form-label mr-3"> My Timesheet </label>
              <!-- <span class="form-label" (click)="navigateToCreateTimesheet()"><a href="javascript:;void" class="purple-color pointer">+ Add Timesheet Logs</a></span> -->
            </div>

            <!-- TODO: Code for My Timesheet for all Status, Separate Status with total working hours-->
            <!-- <div *ngFor="let status of objectKeys(totalWorkingHour1)" class="data-border d-flex justify-content-between">
                                <div class="date-hour">
                                    <div class="date-value">
                                        {{ dateFormateWithSuffix(positionStartDate, positionDate) }} - {{ dateFormateWithSuffix(currentDate, positionDate) }}
                                    </div>
                                    <div class="due-hour d-flex justify-content-between f-12 pt-2">
                                        <span>
                                            Due: {{ dateFormateWithSuffix(currentDate, positionDueDate) }}
                                        </span>
                                        <span>
                                             Hours: {{ totalWorkingHour1[status].toFixed(2) }}
                                        </span>
                                    </div>
                                </div>
                            <div class="status" [ngClass]="status.toLowerCase()">
                                <span class="status-value f-12 mr-3">{{ status === 'NOT_SUBMITTED' ? 'Not Submitted' : status }}</span>
                                <em class="pi pi-angle-right"></em>
                            </div>
                        </div> -->

            <!-- <div *ngFor="let status of objectKeys(totalWorkingHourKeys)" class="data-border d-flex justify-content-between pb-0">
                            <div class="date-hour">
                                <ng-container *ngFor="let position of position; let index = index">
                                    <div class="d-flex flex-column mb-3">
                                        <span class="form-label mb-1">{{ position.project.name }} </span>
                                        <span class="f-12 mb-1">{{ dateFormateWithSuffix(position?.start_date, positionDate) }} - {{ dateFormateWithSuffix(position?.end_date, positionDate) }}</span>
                                        <div class="d-flex">
                                            <span class="f-12 mr-3"><b>Position: </b> {{ position.name }} </span>
                                            <span class="f-12 mr-3" *ngIf="totalWorkingHour[index] && totalWorkingHour[index][status] !== undefined">
                                                <b>Hours: </b> {{ totalWorkingHour[index][status].toFixed(2) }}
                                            </span>
                                            <span class="f-12">
                                                <b>Due: </b> {{ dateFormateWithSuffix(currentDate, positionDueDate) }}
                                            </span>
                                        </div>
                                    </div>
                                </ng-container>
                            </div>
                            <div class="">
                                <div class="status d-flex align-items-center justify-content-end mb-3" [ngClass]="status.toLowerCase()">
                                    <span class="status-value f-12 mr-3">{{ status === 'NOT_SUBMITTED' ? 'Not Submitted' : status }}</span>
                                    <em class="pi pi-angle-right"></em>
                                </div>
                            </div>
                        </div> -->

            <!-- <div class="data-border">
                            <div *ngFor="let position of position; let index = index" class="pb-2">
                                <hr *ngIf="index > 0">
                                <div class="d-flex justify-content-between">
                                    <div class="d-flex flex-column">
                                        <div class="flex"><span class="form-label mb-1">{{ position.project.name }}</span> - <span> {{ position.name }} </span></div>
                                        <span class="f-12 mb-1">{{ dateFormateWithSuffix(position?.start_date, positionDate) }} - {{ dateFormateWithSuffix(position?.end_date, positionDate) }}</span>

                                        <div class="row">
                                            <div class="col-md-4"></div>
                                            <div class="col-md-4 d-flex flex-column"><span class="form-label current-timesheet">Current Timesheet - </span><span class="ml-1">{{ dateFormateWithSuffix(currentTimesheetRange[0], positionDate) }} to {{ dateFormateWithSuffix(currentTimesheetRange[1], positionDate) }}</span></div>
                                            <div class="col-md-4 d-flex flex-column"><span class="form-label previous-timesheet">Previous Timesheet - </span><span class="ml-1"> {{ dateFormateWithSuffix(previousTimesheetRange[0], positionDate) }} to {{ dateFormateWithSuffix(previousTimesheetRange[1], positionDate) }}</span></div>
                                        </div>
                                        <div class="d-flex"><span class="form-label current-timesheet">Current Timesheet - </span><span class="ml-1">{{ dateFormateWithSuffix(currentTimesheetRange[0], positionDate) }} to {{ dateFormateWithSuffix(currentTimesheetRange[1], positionDate) }}</span></div>
                                        <div class="mb-1">
                                            <span class="f-12 mr-3"><b>Not-Submitted: </b>{{ currentTimesheetHoursWithStatus[index]?.NOT_SUBMITTED }}h</span>
                                            <span class="f-12 mr-3"><b>Submitted: </b> {{ currentTimesheetHoursWithStatus[index]?.SUBMITTED }}h</span>
                                            <span class="f-12 mr-3"><b>Approved: </b> {{ currentTimesheetHoursWithStatus[index]?.APPROVED }}h</span>
                                            <span class="f-12 mr-3"><b>Rejected: </b> {{ currentTimesheetHoursWithStatus[index]?.REJECTED }}h</span>
                                        </div> -->

            <!-- <span class="f-12 mr-3"><b>Position: </b> {{ position.name }} </span> -->
            <!-- <span class="f-12 mr-3" *ngIf="totalWorkingHour[index] && totalWorkingHour[index][status] !== undefined">
                                            <b>Hours: </b> {{ totalWorkingHour[index][status].toFixed(2) }}
                                        </span>
                                        <span class="f-12">
                                            <b>Due: </b> {{ dateFormateWithSuffix(currentDate, positionDueDate) }}
                                        </span> -->

            <!-- <div class="d-flex"><span class="form-label previous-timesheet">Previous Timesheet - </span><span class="ml-1"> {{ dateFormateWithSuffix(previousTimesheetRange[0], positionDate) }} to {{ dateFormateWithSuffix(previousTimesheetRange[1], positionDate) }}</span></div>
                                        <div class="">
                                            <span class="f-12 mr-3"><b>Not-Submitted: </b>{{ previousTimesheetHoursWithStatus[index]?.NOT_SUBMITTED }}h</span>
                                            <span class="f-12 mr-3"><b>Submitted: </b> {{ previousTimesheetHoursWithStatus[index]?.SUBMITTED }}h</span>
                                            <span class="f-12 mr-3"><b>Approved: </b> {{ previousTimesheetHoursWithStatus[index]?.APPROVED }}h</span>
                                            <span class="f-12 mr-3"><b>Rejected: </b> {{ currentTimesheetHoursWithStatus[index]?.REJECTED }}h</span>
                                        </div> -->

            <!-- <span class="f-12 mr-3"><b>Position: </b> {{ position.name }} </span> -->
            <!-- <span class="f-12 mr-3" *ngIf="totalWorkingHour[index] && totalWorkingHour[index][status] !== undefined">
                                            <b>Hours: </b> {{ totalWorkingHour[index][status].toFixed(2) }}
                                        </span>
                                        <span class="f-12">
                                            <b>Due: </b> {{ dateFormateWithSuffix(currentDate, positionDueDate) }}
                                        </span> -->

            <!-- </div> -->
            <!-- </div>
                                <div class="d-flex align-items-center">
                                    <em class="pi pi-angle-right"></em>
                                </div>-->
            <!-- </div>   -->
            <!-- </div> -->

            <div class="data-border overflow-handel">
              <div class="p-2">
                <h3 class="font-weight-bold">Current Timesheet</h3>
                <p class="mb-0 f-14">{{ dateFormateWithSuffix(currentTimesheetRange[0], positionDate) }} - {{ dateFormateWithSuffix(currentTimesheetRange[1], positionDate) }}</p>
              </div>
              <p-table [value]="storedCurrentPostion" *ngIf="storedPreviousPostion.length; else noDataFound">
                <ng-template pTemplate="header">
                  <tr>
                    <th>Project</th>
                    <th>Position</th>
                    <th>Hours</th>
                    <th>Status</th>
                  </tr>
                </ng-template>
                <ng-template pTemplate="body" let-pos>
                  <tr>
                    <td>{{ pos?.position?.project?.name }}</td>
                    <td>{{ pos?.position?.name }}</td>
                    <td>
                      {{ calculateTotalHours(pos?.position) }}
                    </td>
                    <td>
                      {{ pos?.position?.time_entries[0]?.time_entry?.status || '-' }}
                    </td>
                  </tr>
                </ng-template>
                <ng-template pTemplate="footer">
                  <tr>
                    <td></td>
                    <td>Total</td>
                    <td>{{ calculateTotalHoursCurrent }}</td>
                    <td></td>
                  </tr>
                </ng-template>
              </p-table>

              <hr />
              <div class="p-2">
                <h3 class="font-weight-bold">Previous Timesheet</h3>
                <p class="mb-0 f-14">{{ dateFormateWithSuffix(previousTimesheetRange[0], positionDate) }} - {{ dateFormateWithSuffix(previousTimesheetRange[1], positionDate) }}</p>
              </div>
              <p-table [value]="storedPreviousPostion" *ngIf="storedPreviousPostion; else noDataFound">
                <ng-template pTemplate="header">
                  <tr>
                    <th>Project</th>
                    <th>Position</th>
                    <th>Hours</th>
                    <th>Status</th>
                  </tr>
                </ng-template>
                <ng-template pTemplate="body" let-pos>
                  <tr>
                    <td>{{ pos?.position?.project?.name }}</td>
                    <td>{{ pos?.position?.name }}</td>
                    <td>{{ calculateTotalHours(pos?.position) }}</td>
                    <td>{{ pos?.position?.time_entries[0]?.time_entry?.status || '-' }}</td>
                  </tr>
                </ng-template>
                <ng-template pTemplate="footer">
                  <tr>
                    <td></td>
                    <td>Total</td>
                    <td>{{ calculateTotalHoursPrevious }}</td>
                    <td colspan="1"></td>
                  </tr>
                </ng-template>
              </p-table>

              <ng-template #noDataFound> No data Found </ng-template>
            </div>
          </div>

          <!-- TODO:  Code for Section My Assignments (All Positions) -->
          <!-- <div class="position">
                        <div class="position-title">
                            <label class="form-label mr-3">
                                My Project Assignments
                            </label>
                        </div>
                        <div class="position-data">
                            <ng-container *ngFor="let position of position">
                                <div class="data-border d-flex justify-content-between">
                                    <div class="d-flex flex-column">
                                        <span class="form-label mb-1">{{ position.project.name }} </span>
                                        <span class="f-12"><b>Position: </b> {{ position.name }} </span>
                                    </div>
                                    <div class="date-value">
                                        {{ dateFormateWithSuffix(position?.start_date, positionDate) }} - {{ dateFormateWithSuffix(position?.end_date, positionDate) }}
                                    </div>
                                </div>
                            </ng-container>
                        </div>
                    </div> -->
          <!-- <em class="pi pi-angle-right"></em> -->
        </div>
      </div>
      <div class="col-md-6">
        <div class="timeoff">
          <div class="d-flex justify-content-between">
            <label class="form-label"> My Time Off </label>
            <span class="form-label"
              ><a href="javascript:;void" class="purple-color pointer" (click)="bookTimeOffPopUp = true" [ngClass]="{ 'disable-link': !selectedEmployeeIds?.value }"
                >+ Book Time Off</a
              ></span
            >
          </div>

          <div class="data-border">
            <div class="accruals row m-0 pt-0">
              <ng-container *ngFor="let item of accrualData">
                <div class="time-off-days col-md-6 mt-3">
                  <label class="form-label time-off-type">
                    {{ item?.calculation?.work_exception_type?.name }}
                  </label>
                  <span class="days"> <b>Used : </b> {{ item?.calculation?.amount_used }} </span>
                  <span class="days"> <b>Remaining : </b> {{ item?.calculation?.remaining_balance }} </span>
                </div>
              </ng-container>
            </div>
            <hr />
            <div class="time-off row m-0 pt-0">
              <ng-container *ngFor="let item of this.totalTimeOffDays">
                <div class="time-off-days col-md-6 mt-3" *ngIf="isNotUsedAccruals(item.type)">
                  <label class="form-label time-off-type">
                    {{ item?.type }}
                  </label>
                  <span class="days">
                    {{ item?.fullDays }} days <span *ngIf="item.remainingHours">{{ item.remainingHours }} hours</span>
                  </span>
                </div>
              </ng-container>
            </div>
          </div>
        </div>

        <div class="upcoming-timeoff mt-4">
          <label class="form-label"> Upcoming Time Off </label>
          <div class="upcoming-timeoff-data">
            <ng-container *ngFor="let workException of workExceptionsData">
              <div
                *ngIf="isUpcomingTimeOff(workException?.work_exception?.date)"
                class="data-border d-flex justify-content-between cursor-pointer"
                (click)="navigateToCreateTimesheet(workException?.work_exception?.date)"
              >
                <div class="date-type">
                  <div class="form-label">
                    {{ dateFormateWithSuffix(workException?.work_exception?.date, timeOff) }}
                  </div>
                  <div class="f-12 pt-2">
                    {{ workException?.work_exception?.work_exception_type?.name }}
                  </div>
                </div>
                <div class="status approved">
                  <span class="status-value f-12 mr-3">Approved</span>
                  <em class="pi pi-angle-right"></em>
                </div>
              </div>
            </ng-container>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<p-dialog
  *ngIf="selectedEmployeeIds?.value"
  [(visible)]="bookTimeOffPopUp"
  header="Book Time Off"
  [style]="{ width: '40vw' }"
  [modal]="true"
  [draggable]="false"
  [resizable]="false"
  (onHide)="closeBookTimeOffModel()"
>
  <app-book-time-off class="my-5" #bookTimeOFF [employee_id]="selectedEmployeeIds?.value" (closeModelSignal)="closeBookTimeOffModel(true)"></app-book-time-off>

  <ng-template pTemplate="footer">
    <div class="d-flex justify-content-end align-items-center">
      <button type="button" class="btn-outline-primary btn mx-2" (click)="closeBookTimeOffModel()">Cancel</button>
      <button [disabled]="bookTimeOff?.createWorkExceptionForm?.invalid" type="button" class="btn-save" (click)="saveBookTimeOffAPI()">Book Now</button>
    </div>
  </ng-template>
</p-dialog>

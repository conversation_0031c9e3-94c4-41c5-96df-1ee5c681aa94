import { DatePipe } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { AccessControlService } from '@entities/access-control/access-control.service';
import { Users } from '@entities/access-control/manage-user/manage-user.component';
import { AuthService } from '@auth/index';
import { GlobalDetail, GlobalDetailList, Roles } from '@entities/administration/administration.model';
import { AdministrationService } from '@entities/administration/administration.service';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { ButtonParams } from '@shared/models';
import { MessageService } from 'primeng/api';

@Component({
  selector: 'app-manage-role',
  templateUrl: './manage-role.component.html',
  styleUrls: ['./manage-role.component.scss'],
  providers: [MessageService, DatePipe],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ManageRoleComponent extends SflBaseComponent implements OnInit {
  roles: Roles[] = [];
  loading = false;
  cardTitle = 'Manage Permissions';
  cardSubTitle = null;
  buttons: ButtonParams[] = [
    {
      btnClass: 'btn-save',
      btnText: 'Add New',
      redirectPath: this.appRoutes.CREATE_ROLE,
      permissions: [this.permissionModules.MANAGE_ROLE]
    }
  ];
  deleteRoleId: number;
  showDeleteDialog = false;
  globalDetails: GlobalDetail;
  globalDetailId: number;
  users: Users;
  listOfAssignedRoles = [];
  permissonDeniedModel: boolean;

  constructor(
    private readonly adminService: AdministrationService,
    private readonly cdf: ChangeDetectorRef,
    private readonly accessControlService: AccessControlService,
    private readonly authService: AuthService
  ) {
    super();
  }

  ngOnInit(): void {}

  getUsers() {
    this.loading = true;
    this.subscriptionManager.add(
      this.accessControlService.getUsers().subscribe(
        (res) => {
          this.users = res?.data;
          for (const user of this.users.user_authorizations) {
            this.listOfAssignedRoles.push(JSON.parse(JSON.stringify(this.getValueFromStringObject(user.user_authorization.self_service_storage))));
          }
          // set up if user can remove or update specific role
          for (const userRole of this.roles) {
            if (this.listOfAssignedRoles.includes(userRole.name)) {
              userRole.blockUpdateDelete = true;
            } else {
              userRole.blockUpdateDelete = false;
            }
          }
          this.cdf.detectChanges();
          this.loading = false;
        },
        (error) => {
          if (error.error.message === 'Sorry, your account does not permit api access') {
            this.permissonDeniedModel = true;
          }
          this.loading = false;
          this.cdf.detectChanges();
        }
      )
    );
  }

  getValueFromStringObject(valueString: string) {
    // first we are splitting the string and extracting after :, now we have string which includes " } so we will now be matching and extracting only string part between '' e.g. 'user'
    return valueString.split(':')[1].match(/'([^']+)'/)[1];
  }

  getRoles() {
    this.roles = [];
    this.loading = true;
    this.cdf.detectChanges();
    this.subscriptionManager.add(
      this.adminService.getRoles('UserRolePermissions').subscribe(
        (res) => {
          if (res?.data?.global_details) {
            const globalDetail = res?.data?.global_details;
            if (globalDetail && globalDetail[0]?.global_detail?.name === 'UserRolePermissions') {
              this.globalDetails = globalDetail[0];
              this.globalDetailId = globalDetail[0].global_detail.id;
              this.roles = globalDetail[0].global_detail.extended_fields.roles;
              this.adminService.setRoles(globalDetail[0].global_detail);
            }
            this.loading = false;
            this.getUsers();
          }
          this.loading = false;
          this.cdf.detectChanges();
        },
        () => {
          this.loading = false;
          this.cdf.detectChanges();
        }
      )
    );
  }

  confirmDeleteRole(id: number) {
    this.deleteRoleId = id;
    this.showDeleteDialog = true;
  }

  closeModal() {
    this.deleteRoleId = null;
    this.showDeleteDialog = false;
  }

  closePermissionModel() {
    this.permissonDeniedModel = false;
  }

  deleteRole() {
    this.isSubmitting = true;
    this.roles = this.roles.filter((role) => role.id !== this.deleteRoleId);
    this.globalDetails.global_detail.extended_fields.roles = this.roles;
    this.subscriptionManager.add(
      this.adminService.updateRoles(this.globalDetails?.global_detail, this.globalDetailId).subscribe(
        (res) => {
          this.isSubmitting = false;
          this.closeModal();
          this.adminService.setRoles(res?.data?.global_detail);
          const globalDetailsList: GlobalDetailList = {
            global_details: [res?.data]
          };
          // as soon as the user removes the role we would be updating our local/ cache roles with updated global details.
          this.authService.globalDetailsList$.next(globalDetailsList);
          this.cdf.detectChanges();
        },
        () => (this.isSubmitting = false)
      )
    );
  }
}

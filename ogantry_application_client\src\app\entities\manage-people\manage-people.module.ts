import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { SharedModule } from '@shared/shared.module';
import { MangePeopleRoutingModule } from './mange-people-routing.module';
import { CreateEmployeeComponent } from './employee/create-employee/create-employee.component';
import { ManageEmployeeComponent } from './employee/manage-employee/manage-employee.component';
import { CreateWorkExceptionsComponent } from './work-exceptions/create-work-exceptions/create-work-exceptions.component';
import { ManageWorkExceptionsComponent } from './work-exceptions/manage-work-exceptions/manage-work-exceptions.component';
import { BadgeModule } from 'primeng/badge';
import { TimeSheetActualsComponent } from './time-sheet/time-sheet-actuals/time-sheet-actuals.component';
import { TabViewModule } from 'primeng/tabview';
import { AuditHistoryComponent } from './employee/audit-history/audit-history.component';
import { InputNumberModule } from 'primeng/inputnumber';
import { TerminateEmployeeComponent } from './employee/terminate-employee/terminate-employee.component';
import { EmployeeCostComponent } from './employee/employee-cost/employee-cost.component';
import { EmployeeUtilizationComponent } from './employee/employee-utilization/employee-utilization.component';
import { FileUploadModule } from 'primeng/fileupload';
import { ImportCsvComponent } from './time-sheet/import-csv/import-csv.component';
import { CsvDataSetupComponent } from './time-sheet/import-csv/csv-data-setup/csv-data-setup.component';
import { RepairCsvDataComponent } from './time-sheet/import-csv/repair-csv-data/repair-csv-data.component';
import { ReviewCsvDataComponent } from './time-sheet/import-csv/review-csv-data/review-csv-data.component';
import { InputSwitchModule } from 'primeng/inputswitch';
import { ManageTimeComponent } from './manage-time/manage-time.component';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MbscModule } from '@mobiscroll/angular';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ListboxModule } from 'primeng/listbox';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { SlideMenuModule } from 'primeng/slidemenu';
import { CheckboxModule } from 'primeng/checkbox';
import { ManageTimeDashboardComponent } from './manage-time-dashboard/manage-time-dashboard.component';
import { AccrualListComponent } from './employee/accrual-list/accrual-list.component';
@NgModule({
  declarations: [
    CreateEmployeeComponent,
    ManageEmployeeComponent,
    CreateWorkExceptionsComponent,
    ManageWorkExceptionsComponent,
    TimeSheetActualsComponent,
    AuditHistoryComponent,
    TerminateEmployeeComponent,
    EmployeeUtilizationComponent,
    EmployeeCostComponent,
    ImportCsvComponent,
    CsvDataSetupComponent,
    ImportCsvComponent,
    RepairCsvDataComponent,
    ReviewCsvDataComponent,
    ManageTimeComponent,
    ManageTimeDashboardComponent,
    AccrualListComponent
  ],
  imports: [
    CommonModule,
    SharedModule,
    MangePeopleRoutingModule,
    BadgeModule,
    TabViewModule,
    InputNumberModule,
    FileUploadModule,
    InputSwitchModule,
    MatSidenavModule,
    ConfirmDialogModule,
    MbscModule,
    ListboxModule,
    OverlayPanelModule,
    SlideMenuModule,
    CheckboxModule
  ],
  providers: []
})
export class MangePeopleModule {}

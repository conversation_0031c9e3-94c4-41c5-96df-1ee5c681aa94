{"compileOnSave": false, "compilerOptions": {"baseUrl": "./", "outDir": "./dist/out-tsc", "sourceMap": true, "declaration": false, "downlevelIteration": true, "experimentalDecorators": true, "module": "es2020", "moduleResolution": "node", "importHelpers": true, "target": "es2015", "typeRoots": ["src/app/node_modules/@types"], "lib": ["es2018", "dom"], "paths": {"@auth/*": ["src/app/@auth/*"], "@core/*": ["src/app/core/*"], "@entities/*": ["src/app/entities/*"], "@layout/*": ["src/app/layout/*"], "@shared/*": ["src/app/@shared/*"], "@environment/*": ["src/environments/*"]}, "allowSyntheticDefaultImports": true}, "angularCompilerOptions": {"fullTemplateTypeCheck": true, "strictInjectionParameters": true, "resolveJsonModule": true}}
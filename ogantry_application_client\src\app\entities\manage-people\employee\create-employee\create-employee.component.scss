#createEmployee {
  ::ng-deep .dropdown .p-dropdown,
  ::ng-deep .dropdown .p-multiselect {
    width: 100%;
    height: 100%;
    border-radius: 9px !important;
    border: none !important;
    background-color: #f8f8ff !important;
    min-height: 60px !important;
    padding-top: 1rem;
  }

  ::ng-deep .dropdown .p-dropdown .p-dropdown-label,
  ::ng-deep .dropdown .p-multiselect .p-multiselect-label {
    color: #495057;
    font-family: Poppins;
    font-size: 16px;
    // font-weight: 500;
    letter-spacing: -0.32px;
    line-height: 25px;
    padding-top: 0.5rem;
    // padding-left: 1rem;
  }

  ::ng-deep .dropdown .p-dropdown .p-dropdown-label.p-placeholder,
  ::ng-deep .dropdown .p-multiselect .p-multiselect-label.p-placeholder {
    color: #b5b5c3 !important;
  }

  ::ng-deep .p-inputgroup {
    width: 100%;
    border-radius: 9px !important;
    border: none !important;
    background-color: #f8f8ff !important;
    min-height: 60px !important;
  }

  ::ng-deep .p-inputgroup .p-inputtext {
    border: none !important;
    background-color: #f8f8ff !important;
    font-size: 16px;
    // padding-left: 1rem;
  }

  ::ng-deep .p-inputgroup-addon {
    border: 0 !important;
    width: 4rem;
  }

  ::ng-deep .p-inputtext:enabled:focus {
    box-shadow: none;
  }

  ::ng-deep .range-calender .p-calendar-w-btn {
    height: 100%;
    width: 100%;
  }

  ::ng-deep .range-calender .p-calendar-w-btn .p-inputtext {
    border: none !important;
    background-color: #f8f8ff !important;
    height: 100%;
    font-size: 16px;
  }

  .form-control {
    padding: 0.65rem 0.65rem;
  }

  ::ng-deep .p-button {
    background: #4b3f72;
    border-color: #4b3f72;
  }

  ::ng-deep .p-button:enabled:hover {
    background: #574985;
    border-color: #574985;
  }

  .travel-input {
    width: 60%;
  }

  ::ng-deep .p-button.p-button-icon-only {
    width: 4.357rem;
  }

  ::ng-deep .p-dropdown:not(.p-disabled).p-focus {
    box-shadow: none;
  }

  .terminate-text {
    color: #000000;
    font-family: Poppins;
    font-size: 14px;
    letter-spacing: 0;
    line-height: 21px;
  }
  .terminate-text-checkbox {
    font-weight: 500;
    color: #000000;
    font-family: Poppins;
    font-size: 14px;
    letter-spacing: 0;
    line-height: 21px;
  }
  .scrollable-content {
    max-height: calc(100vh - 50px) !important;
    overflow-y: auto !important;
  }
  ::ng-deep .p-datatable .p-datatable-thead > tr > th {
    height: 20px;
    color: #4b3f72;
    font-family: Poppins;
    font-size: 14px;
    font-weight: 500;
    letter-spacing: 0;
    line-height: 21px;
    border-bottom: none;
    padding: 0.5rem 0.5rem;
    background-color: #ecedf6 !important;
  }

  ::ng-deep .p-datatable .p-datatable-thead tr > th:first-child,
  ::ng-deep .p-datatable .p-datatable-tbody tr > td:first-child {
    padding-left: 1rem;
  }

  ::ng-deep .p-datatable .p-datatable-tbody > tr > td {
    height: 20px;
    width: 123px;
    color: #000000;
    font-family: Poppins;
    font-size: 12px;
    letter-spacing: 0;
    line-height: 15px;
    padding: 0.5rem 0.5rem;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  ::ng-deep .p-datatable .p-sortable-column.p-highlight:hover .p-sortable-column-icon {
    color: #4b3f72;
  }

  ::ng-deep .p-datatable .p-sortable-column.p-highlight .p-sortable-column-icon {
    color: #4b3f72;
  }

  ::ng-deep .p-datatable .p-sortable-column.p-highlight,
  .p-datatable .p-sortable-column.p-highlight:hover {
    background: #f8f9fa;
    color: #4b3f72;
  }

  ::ng-deep .p-datatable .p-sortable-column.p-highlight:hover {
    background: #f8f9fa;
    color: #4b3f72;
  }

  ::ng-deep .p-datatable .p-sortable-column:focus {
    box-shadow: inset 0 0 0 0.2rem #4b3f72;
    outline: 0 none;
  }
  ::ng-deep .p-paginator .p-dropdown {
    width: 91px;
  }

  ::ng-deep .p-datatable-tbody tr:last-child > td {
    border-bottom: none;
  }
  .center-align {
    text-align: center !important;
  }
  .text-number-right {
    text-align: right;
  }
  .header-width-type {
    width: 10%;
  }
  .header-width {
    width: 16%;
  }
  .header-width-cost {
    width: 9%;
  }
  .header-width-daily {
    width: 19%;
  }

  .header-width-avail {
    width: 9%;
  }
  .header-width-start {
    width: 11%;
  }

  ::ng-deep .p-datatable .p-datatable-loading-overlay {
    top: 35px;
    background-color: transparent;
  }

  .accruals-header,
  .accruals-body {
    height: 30px !important;

    .work-exception,
    .start-date,
    .balance {
      width: 20% !important;
    }

    .cycle,
    .rate {
      width: 15% !important;
    }

    .action {
      width: 10% !important;
    }
  }

  .pointer,
  .pointer:hover {
    text-decoration: underline !important;
  }
}
::ng-deep .confirm-dialog-expense .p-dialog {
  width: 30vw;
}

.p-0 {
  padding: 0;
}

.p-calendar .p-inputtext {
  height: 60px !important;
  font-size: 16px;
}
::ng-deep .accordion .card .card-header {
  background-color: transparent;
  padding: 0.5rem;
}
::ng-deep .accordion > .card {
  border: 0;
}

@media (max-width: 1199.98px) {
  .card-body {
    padding: 15px !important;
  }
  .create-card {
    padding: 15px !important;
  }
  .travel-input {
    width: 100% !important;
  }
}
@media (max-width: 500px) {
  ::ng-deep .p-datatable table {
    display: block;
    overflow: auto;
    white-space: nowrap;
  }

  ::ng-deep .p-paginator-current {
    right: 32px;
    bottom: 10px;
  }

  ::ng-deep .p-paginator-rpp-options {
    margin: 20px 0 0 0 !important;
  }
}

::ng-deep .position-rel small {
  position: absolute;
}

.background-white {
  background-color: #ffffff !important;
}

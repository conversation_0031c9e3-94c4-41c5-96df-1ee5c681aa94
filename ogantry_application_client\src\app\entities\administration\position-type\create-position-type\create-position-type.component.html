<div class="card card-custom gutter-b" id="createPosType">
  <app-card-header [cardTitle]="cardTitle" [cardSubTitle]="cardSubTitle" [buttons]="buttons"></app-card-header>
  <div class="card-body create-card" *isFetchingData="loading$">
    <form
      class="form"
      autocomplete="off"
      [formGroup]="createPositionTypeForm"
      autocomplete="off"
      novalidate="novalidate"
      id="create_role_form"
      (ngSubmit)="createPositionTypeForm.valid && onSave()"
    >
      <div id="client-detail">
        <kt-auth-notice></kt-auth-notice>
        <div class="mx-auto content-center">
          <div class="row">
            <div class="col-12 pr-md-5">
              <div class="form-group first">
                <label class="form-label">Skill Set</label>
                <input type="text" class="form-control custom" placeholder="e.g. Skill Set" formControlName="name" />
                <app-form-error [validation]="'required'" [form]="createPositionTypeForm" [controlName]="'name'" [fieldLabel]="'Skill Set'"></app-form-error>
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>

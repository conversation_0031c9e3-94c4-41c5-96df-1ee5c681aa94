@import '../../../../../src/assets/sass/pages/wizard/wizard4';
@import '../../../../../src/assets/sass/layout/variables';
#createProject {
  .card.card-custom {
    height: 100%;
    box-shadow: none;
    border: 0;
  }

  .card-shadowless {
    overflow-x: hidden;
    overflow-y: hidden !important;
  }

  .btn-prev {
    border-color: #4b3f72;
    background-color: transparent;
  }

  .btn-prev:hover {
    background-color: #574985 !important;
    border-color: #574985 !important;
    color: #ffffff !important;
  }

  .card::-webkit-scrollbar {
    width: 0.5em !important;
  }

  .card::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3) !important;
  }

  .card::-webkit-scrollbar-thumb {
    background-color: darkgray !important;
    outline: 1px solid slategray !important;
  }

  .btn-success,
  .btn-success:hover {
    border-color: #4b3f72;
    background-color: #4b3f72;
  }

  .pb-8 {
    padding-bottom: 6rem !important;
  }

  .font-weight-bold {
    font-size: 12px;
    line-height: 18px;
  }

  .full-height {
    height: 100%;
  }
  .cal-height {
    max-height: 70%;
  }

  .position-relative {
    position: relative;
  }
}
.btn-height {
  height: 40px;
  line-height: 1.2;
}

@media (min-width: 501px) {
  .btn-footer {
    position: fixed !important;
    top: calc(100vh - 105px) !important;
  }
}

@media (max-width: 500px) {
  .btn-footer {
    position: initial !important;
    margin-top: 15px;
  }
  .btn-fix {
    position: initial !important;
    margin-top: 15px;
  }
}

@media (max-width: 767px) {
  .wizard-steps .wizard-step {
    margin-bottom: 0 !important;
    padding: 5px 0 0 !important;
  }
  .start-date {
    margin-left: 0 !important;
  }
}

.create-project-wrapper {
  ::ng-deep .mat-drawer.mat-drawer-end {
    transform: 100px !important;
  }
}

::ng-deep .p-datatable-scrollable-both .p-datatable-thead > tr > th,
.p-datatable-scrollable-both .p-datatable-tbody > tr > td,
.p-datatable-scrollable-both .p-datatable-tfoot > tr > td,
.p-datatable-scrollable-horizontal .p-datatable-thead > tr > th .p-datatable-scrollable-horizontal .p-datatable-tbody > tr > td,
.p-datatable-scrollable-horizontal .p-datatable-tfoot > tr > td,
.p-datatable-scrollable-both .p-datatable-thead > tr > th {
  flex: 1 1 0 !important;
}

::ng-deep .p-datatable-scrollable-both .p-datatable-tbody > tr > td,
.p-datatable-scrollable-both .p-datatable-tfoot > tr > td,
.p-datatable-scrollable-horizontal .p-datatable-thead > tr > th .p-datatable-scrollable-horizontal .p-datatable-tbody > tr > td,
.p-datatable-scrollable-horizontal .p-datatable-tfoot > tr > td {
  flex: 1 1 0 !important;
}
.confirm-dialog-paused {
  max-width: 400px !important;
  width: 400px !important;
}

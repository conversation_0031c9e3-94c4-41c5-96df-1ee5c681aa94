<div class="card card-custom gutter-b" id="time-sheet-actual">
  <app-card-header [cardTitle]="cardTitle" [cardSubTitle]="cardSubTitle" [buttons]="buttons"></app-card-header>
  <div class="card-body" *ngIf="!timeSheetImported">
    <div *ngIf="!showNoDataMsg" class="h-100">
      <p-table responsiveLayout="scroll" [columns]="historyTableHeader" [value]="tableHistoryData" class="imported-history-table-wrapper" [scrollable]="true">
        <ng-template pTemplate="header" let-columns>
          <tr>
            <th *ngFor="let col of columns">
              {{ col.header }}
            </th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-rowData let-columns="columns">
          <tr>
            <td>{{ rowData.Date | date : 'M/d/yyyy, h:mm a' }}</td>
            <td>{{ rowData.filePath }}</td>
            <td>{{ rowData.User }}</td>
          </tr>
        </ng-template>
      </p-table>
    </div>
    <div class="h-100 d-flex align-items-center justify-content-center flex-column" *ngIf="showNoDataMsg">
      <p class="mb-0 no-log-msg">No log file imported yet.</p>
      <span class="no-log-info">Click on import timesheet and Upload CSV file</span>
    </div>
  </div>
  <app-import-csv
    *ngIf="timeSheetImported"
    [selectedHeaderAlongWithOGFields]="tableHeader"
    [formMapping]="this.columnMappingForm.value"
    [CSVData]="uploadedCSVFileData"
    [columnMapping]="columnMappingForm.value"
    [OGClientList]="OGClientList"
    [OGProjectList]="OGProjectList"
    [OGPositionList]="OGPositionList"
    [uploadedCSVFile]="uploadedCSVFile"
    [timeEntriesGlobalDetails]="globalDetailsForTimeEntries"
    [formMapping]=""
  ></app-import-csv>
</div>

<p-dialog header="Import TimeSheet" [(visible)]="showFileUploadDialog" [modal]="true" class="upload-file-dialog" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
  <div class="d-flex flex-column align-items-center align-items-center">
    <div>
      <p-fileUpload
        accept=".csv"
        #fileUpload
        [multiple]="false"
        [auto]="true"
        chooseLabel="Upload CSV"
        (onSelect)="onCsvUpload($event)"
        (onRemove)="onFileRemove($event)"
        class="upload-csv-btn"
      >
      </p-fileUpload>
    </div>
  </div>
  <!-- <p *ngIf="errorInFileRead" class="red-text">Errors found in rows. Please correct data and try re-importing.</p> -->
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center">
      <button type="button" class="btn-save" [disabled]="!this.uploadedCSVFile" (click)="importFile()">Import & Next</button>
      <button type="button" class="btn-cancel" (click)="hideUploadModal()">Cancel</button>
    </div>
  </ng-template>
</p-dialog>

<p-dialog header="Field Mapping" [(visible)]="fieldMappingDialog" [modal]="true" class="field-mapping-dialog" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
  <p class="mapping-msg-wrapper">For each column in your CSV file, select a corresponding field in Ogantry</p>

  <div class="d-flex justify-content-center flex-column align-items-center">
    <form [formGroup]="columnMappingForm" class="w-75">
      <div *ngFor="let field of columnMappingFields">
        <div class="d-flex align-items-center w-100 justify-content-between my-2">
          <label class="field-mapping-label">{{ field.label }}</label>
          <!-- <label class="field-mapping-label" *ngIf="field.label === 'Position'"
            >Optional:</label
          > -->
          <p-inputSwitch
            class="pl-2"
            *ngIf="field.label === 'Position'"
            [(ngModel)]="positionISOptional"
            [ngModelOptions]="{ standalone: true }"
            (onChange)="updatePositionValidator()"
          ></p-inputSwitch>

          <p-dropdown
            appendTo="body"
            [formControl]="columnMappingForm.get(field.label)"
            [options]="uploadedCsvHeader"
            placeholder="Please Select Your Value"
            class="OG-fields-dropdown-wrapper"
            (onChange)="onDropdownChange($event, field.label)"
            [showClear]="true"
          ></p-dropdown>
        </div>
      </div>
    </form>
  </div>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center">
      <button type="button" class="btn-save" [disabled]="columnMappingForm.invalid" (click)="submitFieldMapping()">Import</button>
      <button type="button" class="btn-cancel" (click)="hideFieldMappingDialog()">Cancel</button>
    </div>
  </ng-template>
</p-dialog>

<p-dialog header="Import TimeSheet" [(visible)]="errorInFileRead" [modal]="true" class="upload-file-dialog" [draggable]="false" [resizable]="false" [baseZIndex]="10000">
  <p class="red-text ml-2 f-13 pb-5">Empty Cell in Rows. Please correct data and try re-importing.</p>
</p-dialog>

<p-confirmDialog key="positionDialog" [position]="'top'" rejectButtonStyleClass="p-button-outlined"></p-confirmDialog>

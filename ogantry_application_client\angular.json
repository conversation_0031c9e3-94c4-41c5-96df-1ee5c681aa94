{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"ogantry": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets", "src/manifest.webmanifest"], "styles": ["src/styles.scss"], "scripts": ["node_modules/popper.js/dist/umd/popper.js", "node_modules/tooltip.js/dist/umd/tooltip.min.js", "node_modules/perfect-scrollbar/dist/perfect-scrollbar.js", "node_modules/clipboard/dist/clipboard.js", "src/assets/js/layout/extended/examples.js"], "stylePreprocessorOptions": {"includePaths": ["src/assets"]}, "allowedCommonJsDependencies": ["lodash", "chartjs-plugin-datalabels", "file-saver", "chart.js", "store", "rxjs", "object-path", "ngx-google-places-autocomplete", "ng2-stompjs", "@stomp/rx-stomp", "xlsx", "jspdf-autotable", "crypto-js", "raf", "@babel/runtime-corejs3"], "serviceWorker": true, "ngswConfigPath": "ngsw-config.json", "vendorChunk": true, "extractLicenses": false, "buildOptimizer": false, "sourceMap": true, "optimization": false, "namedChunks": true}, "configurations": {"prod": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "20kb", "maximumError": "50kb"}]}, "dev": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.dev.ts"}], "optimization": false, "outputHashing": "all", "sourceMap": true, "namedChunks": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "35mb", "maximumError": "100mb"}, {"type": "anyComponentStyle", "maximumWarning": "100kb", "maximumError": "500kb"}]}, "stage": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.stage.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "35mb", "maximumError": "100mb"}, {"type": "anyComponentStyle", "maximumWarning": "100kb", "maximumError": "500kb"}]}}, "defaultConfiguration": ""}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "ogantry:build"}, "configurations": {"prod": {"browserTarget": "ogantry:build:prod"}, "dev": {"browserTarget": "ogantry:build:dev"}, "stage": {"browserTarget": "ogantry:build:stage"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "ogantry:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "assets": ["src/favicon.ico", "src/assets", "src/manifest.webmanifest"], "styles": ["src/styles.scss"], "scripts": []}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["tsconfig.app.json", "tsconfig.spec.json", "e2e/tsconfig.json"], "exclude": ["**/node_modules/**"]}}, "e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "ogantry:serve"}, "configurations": {"production": {"devServerTarget": "ogantry:serve:production"}}}}}}, "defaultProject": "ogantry", "cli": {"analytics": "77e06b6d-5484-4999-87a4-27f2066d5035"}}
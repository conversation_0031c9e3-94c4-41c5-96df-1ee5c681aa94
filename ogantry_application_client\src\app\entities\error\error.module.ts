import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { ErrorComponent } from './error.component';
import { PartialsModule } from '@entities/partials/partials.module';
import { SharedModule } from '@shared/shared.module';

@NgModule({
  declarations: [ErrorComponent],
  imports: [
    CommonModule,
    PartialsModule,
    SharedModule,
    RouterModule.forChild([
      {
        path: '',
        component: ErrorComponent
      }
    ])
  ]
})
export class ErrorModule {}

import { Component, OnInit } from '@angular/core';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { ButtonParams } from '@shared/models';
import { GlobalDetailTaggingCategory, TagCategory } from '../administration.model';
import { AdministrationService } from '../administration.service';

@Component({
  selector: 'app-manage-tag-category',
  templateUrl: './manage-tag-category.component.html',
  styleUrls: ['./manage-tag-category.component.scss']
})
export class ManageTagCategoryComponent extends SflBaseComponent implements OnInit {
  tagCategory: TagCategory[] = [];
  loading = false;
  cardTitle = 'Manage Tag Category';
  cardSubTitle = null;
  buttons: ButtonParams[] = [
    {
      btnClass: 'btn-save',
      btnText: 'Add New',
      redirectPath: this.appRoutes.CREATE_TAG_CATEGORY,
      permissions: [this.permissionModules.MANAGE_TAG_CATEGORY]
    }
  ];
  deleteCategoryId: string;
  showDeleteDialog = false;
  globalDetailsTaggingCategory: GlobalDetailTaggingCategory;
  globalDetailId: number;

  constructor(private readonly adminService: AdministrationService) {
    super();
  }

  ngOnInit(): void {}

  getTagCategories() {
    this.tagCategory = [];
    this.loading = true;
    this.subscriptionManager.add(
      this.adminService.getTagCategories('TagCategoryManagement').subscribe(
        (res) => {
          this.loading = false;
          if (res?.data?.global_details) {
            const globalDetail = res?.data?.global_details;
            if (globalDetail && globalDetail[0]?.global_detail?.name === 'TagCategoryManagement') {
              this.globalDetailsTaggingCategory = globalDetail[0];
              this.globalDetailId = globalDetail[0].global_detail.id;
              this.tagCategory = globalDetail[0].global_detail.extended_fields.tagCategory;
              this.adminService.setTagCategories(globalDetail[0].global_detail);
            }
          }
        },
        () => (this.loading = false)
      )
    );
  }

  confirmDeleteCategory(id: string) {
    this.deleteCategoryId = id;
    this.showDeleteDialog = true;
  }

  closeModal() {
    this.deleteCategoryId = null;
    this.showDeleteDialog = false;
  }

  deleteCategory() {
    this.isSubmitting = true;
    this.tagCategory = this.tagCategory.filter((category) => category.id !== this.deleteCategoryId);
    this.globalDetailsTaggingCategory.global_detail.extended_fields.tagCategory = this.tagCategory;
    this.subscriptionManager.add(
      this.adminService.updateCategory(this.globalDetailsTaggingCategory?.global_detail, this.globalDetailId).subscribe(
        (res) => {
          this.isSubmitting = false;
          this.closeModal();
          this.adminService.setTagCategories(res?.data?.global_detail);
        },
        () => (this.isSubmitting = false)
      )
    );
  }
}

"use strict";var KTOffcanvas=function(t,e){var o=this,n=KTUtil.getById(t),s=KTUtil.getBody();if(n){var a={attrCustom:""},i={construct:function(t){return KTUtil.data(n).has("offcanvas")?o=KTUtil.data(n).get("offcanvas"):(i.init(t),i.build(),KTUtil.data(n).set("offcanvas",o)),o},init:function(t){o.events=[],o.options=KTUtil.deepExtend({},a,t),o.classBase=o.options.baseClass,o.attrCustom=o.options.attrCustom,o.classShown=o.classBase+"-on",o.classOverlay=o.classBase+"-overlay",o.target,o.state=KTUtil.hasClass(n,o.classShown)?"shown":"hidden"},build:function(){if(o.options.toggleBy)if("string"==typeof o.options.toggleBy)KTUtil.addEvent(KTUtil.getById(o.options.toggleBy),"click",function(t){t.preventDefault(),o.target=this,i.toggle()});else if(o.options.toggleBy&&o.options.toggleBy[0])if(o.options.toggleBy[0].target)for(var t in o.options.toggleBy)KTUtil.addEvent(KTUtil.getById(o.options.toggleBy[t].target),"click",function(t){t.preventDefault(),o.target=this,i.toggle()});else for(var t in o.options.toggleBy)KTUtil.addEvent(KTUtil.getById(o.options.toggleBy[t]),"click",function(t){t.preventDefault(),o.target=this,i.toggle()});else o.options.toggleBy&&o.options.toggleBy.target&&KTUtil.addEvent(KTUtil.getById(o.options.toggleBy.target),"click",function(t){t.preventDefault(),o.target=this,i.toggle()});var e=KTUtil.getById(o.options.closeBy);e&&KTUtil.addEvent(e,"click",function(t){t.preventDefault(),o.target=this,i.hide()})},isShown:function(){return"shown"==o.state},toggle:function(){i.eventTrigger("toggle"),"shown"==o.state?i.hide():i.show()},show:function(){"shown"!=o.state&&(i.eventTrigger("beforeShow"),i.toggleClass("show"),KTUtil.attr(s,"data-offcanvas-"+o.classBase,"on"),KTUtil.addClass(n,o.classShown),o.attrCustom.length>0&&KTUtil.attr(s,"data-offcanvas-"+o.classCustom,"on"),o.state="shown",o.options.overlay&&(o.overlay=KTUtil.insertAfter(document.createElement("DIV"),n),KTUtil.addClass(o.overlay,o.classOverlay),KTUtil.addEvent(o.overlay,"click",function(t){t.preventDefault(),i.hide(o.target)})),i.eventTrigger("afterShow"))},hide:function(){"hidden"!=o.state&&(i.eventTrigger("beforeHide"),i.toggleClass("hide"),KTUtil.removeAttr(s,"data-offcanvas-"+o.classBase),KTUtil.removeClass(n,o.classShown),o.attrCustom.length>0&&KTUtil.removeAttr(s,"data-offcanvas-"+o.attrCustom),o.state="hidden",o.options.overlay&&o.overlay&&KTUtil.remove(o.overlay),i.eventTrigger("afterHide"))},toggleClass:function(t){var e,n=KTUtil.attr(o.target,"id");if(o.options.toggleBy&&o.options.toggleBy[0]&&o.options.toggleBy[0].target)for(var s in o.options.toggleBy)o.options.toggleBy[s].target===n&&(e=o.options.toggleBy[s]);else o.options.toggleBy&&o.options.toggleBy.target&&(e=o.options.toggleBy);if(e){var a=KTUtil.getById(e.target);"show"===t&&KTUtil.addClass(a,e.state),"hide"===t&&KTUtil.removeClass(a,e.state)}},eventTrigger:function(t,e){for(var n=0;n<o.events.length;n++){var s=o.events[n];if(s.name==t){if(1!=s.one)return s.handler.call(this,o,e);if(0==s.fired)return o.events[n].fired=!0,s.handler.call(this,o,e)}}},addEvent:function(t,e,n){o.events.push({name:t,handler:e,one:n,fired:!1})}};return o.setDefaults=function(t){a=t},o.isShown=function(){return i.isShown()},o.hide=function(){return i.hide()},o.show=function(){return i.show()},o.on=function(t,e){return i.addEvent(t,e)},o.one=function(t,e){return i.addEvent(t,e,!0)},i.construct.apply(o,[e]),!0,o}};"undefined"!=typeof module&&void 0!==module.exports&&(module.exports=KTOffcanvas);

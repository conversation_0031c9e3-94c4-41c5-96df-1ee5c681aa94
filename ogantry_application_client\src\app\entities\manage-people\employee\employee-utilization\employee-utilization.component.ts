import { DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { AuthNoticeService } from '@auth/auth-notice/auth-notice.service';
import { Employee } from '@entities/administration/administration.model';
import { AdministrationService } from '@entities/administration/administration.service';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { APP_ROUTES } from '@shared/constants';
import { ButtonParams, OGantryHttpResponse } from '@shared/models';
import { KtDialogService } from '@shared/services';
import moment from 'moment';

@Component({
  selector: 'app-employee-utilization',
  templateUrl: './employee-utilization.component.html',
  styleUrls: ['./employee-utilization.component.scss'],
  providers: [DatePipe]
})
export class EmployeeUtilizationComponent extends SflBaseComponent implements OnInit {
  cardTitle = 'Edit Employee Utilization Target';
  cardSubTitle = null;
  employeeId: number;
  createEmployeeCostForm: FormGroup;
  buttons: ButtonParams[] = [];
  employee: Employee;
  maxDate = null;
  minDate = null;
  showDialogBox = false;
  showAccordian = false;
  nextMonthName: string;

  constructor(
    private readonly activatedRoute: ActivatedRoute,
    private readonly administrationService: AdministrationService,
    private readonly router: Router,
    private readonly datePipe: DatePipe,
    private readonly authNoticeService: AuthNoticeService,
    private readonly ktDialogService: KtDialogService
  ) {
    super();
  }

  ngOnInit(): void {
    this.loading$.next(false);
    this.initForm();
    this.getParams();
  }

  initForm(): void {
    this.createEmployeeCostForm = new FormGroup({
      utilization_target: new FormControl('', [Validators.required, Validators.min(0), Validators.max(100)]),
      active_date: new FormControl('', Validators.required)
    });
  }

  setNextMonthName(): void {
    const activeDate = this.createEmployeeCostForm.get('active_date')?.value;
    const nextMonthDate = new Date(activeDate);
    nextMonthDate.setMonth(nextMonthDate.getMonth() + 1);

    const monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];

    this.nextMonthName = monthNames[nextMonthDate.getMonth()];
  }

  getParams() {
    this.activatedRoute.params.subscribe((params: Params) => {
      this.employeeId = params.employeeId;
      if (this.employeeId) {
        this.getEmployee();
        this.showAccordian = true;
      }
      this.setBtnParams();
    });
  }

  getEmployee() {
    this.loading$.next(true);
    this.subscriptionManager.add(
      this.administrationService.getEmployee(this.employeeId).subscribe(
        (res) => {
          this.employee = res.data;
          this.setEmployeeForm();
          this.loading$.next(false);
        },
        () => this.loading$.next(false)
      )
    );
  }

  async setEmployeeForm() {
    const employee = this.employee.employee;
    this.createEmployeeCostForm.controls['utilization_target'].setValue(employee.utilization_target);
    this.maxDate = moment(employee.end_date).toDate();
    this.minDate = moment(employee.start_date).toDate();
  }

  setBtnParams() {
    this.buttons = [
      {
        btnClass: 'btn btn-cancel',
        btnText: 'Cancel',
        redirectPath: this.appRoutes.MANAGE_EMPLOYEE
      },
      {
        btnClass: 'btn-save',
        btnText: 'Save',
        btnType: 'submit',
        action: this.showDialog.bind(this),
        loading: this.isSubmitting
      }
    ];
  }

  getSkillSet(employee) {
    const postionType = [];
    employee?.employee?.position_types?.forEach((type) => {
      postionType.push(type.position_type.name);
    });
    return postionType.join(',').replace(',', ', ');
  }

  updateEmployeeUtilizationApi() {
    this.isSubmitting = true;
    const employeeData = this.createEmployeeCostForm.value;
    employeeData.active_date = this.datePipe.transform(new Date(employeeData.active_date), 'yyyy-MM-dd');
    this.subscriptionManager.add(
      this.administrationService.updateEmployeeUtilization(employeeData, this.employeeId).subscribe(
        (res) => {
          this.closeModal();
          this.isSubmitting = false;
          this.router.navigateByUrl(APP_ROUTES.MANAGE_EMPLOYEE);
        },
        (err) => this.onError(err)
      )
    );
  }

  onError(err) {
    this.isSubmitting = false;
    this.closeModal();
    const error: OGantryHttpResponse<Employee> = err.error;
    this.authNoticeService.setNotice(error.errors, 'danger');
  }

  showDialog() {
    if (!this.checkFormForValidation(this.createEmployeeCostForm)) {
      this.showDialogBox = true;
    }
    this.setNextMonthName();
  }

  closeModal() {
    this.showDialogBox = false;
  }

  limitDecimalPlaces(event: any): void {
    const value = event?.target?.value;
    const regex = new RegExp(this.appConstants.regexForTwoDecimal).test(value);
    if (!regex) {
      event.target.value = value?.slice(0, -1);
      this.createEmployeeCostForm?.controls['utilization_target']?.setValue(event?.target?.value);
    }
  }

  ngOnDestroy(): void {
    this.authNoticeService.setNotice(null);
    this.ktDialogService.hide();
  }
}

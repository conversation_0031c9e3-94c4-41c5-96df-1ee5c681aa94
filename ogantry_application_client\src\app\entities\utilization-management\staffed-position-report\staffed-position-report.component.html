<mat-drawer-container class="h-100" id="staffedpositionReport">
  <mat-drawer class="detail-sidebar" #sidebarFilter mode="over" position="end" disableClose [opened]="openFilter">
    <div class="h-100 card card-custom add-contact">
      <app-card-header [cardTitle]="filterCardTitle" [buttons]="filterButtons" [cardLabelClass]="'mb-0'"></app-card-header>
      <div class="card-body create-card">
        <form class="form" autocomplete="off" novalidate="novalidate" id="applyFilter" (ngSubmit)="filterReport()">
          <div class="row">
            <div class="col-12">
              <app-shared-range-selector
                [showDateRangeRadioButton]="dataFilter.start_month && dataFilter.end_month ? true : false"
                [rollingRadioBtnSelected]="dataFilter.rollingOption ? true : false"
                [yearRadioBtnSelected]="dataFilter.year ? true : false"
                [quaterRadioBtnSelected]="dataFilter.quarter ? true : false"
                [start_month]="dataFilter.start_month"
                [end_month]="dataFilter.end_month"
                [selectedYear]="dataFilter.year"
                [selectedQuarterValue]="dataFilter.quarter"
                [selectedRollingOption]="dataFilter.rollingOption"
                (selected_end_date)="endMonthSelected($event)"
                (selected_start_date)="startMonthSelected($event)"
                (updatedYearValue)="dataFilter.year = $event"
                (updatedRollingValue)="dataFilter.rollingOption = $event"
                (updatedquaterValue)="dataFilter.quarter = $event"
                [dateError]="dateError"
                [dateRequired]="dateRequired"
              ></app-shared-range-selector>
              <div class="PL-border" id="project-status">
                <div class="form-group">
                  <label class="form-label background">Project Status</label>
                </div>
                <div class="form-group first dropdown p-2">
                  <p-multiSelect
                    #multiSelectComp
                    [(ngModel)]="projectStatus"
                    [overlayVisible]="true"
                    [showHeader]="false"
                    [options]="statuses"
                    [ngModelOptions]="{ standalone: true }"
                    placeholder="Status"
                    display="chip"
                    (onChange)="statusSelected($event)"
                  >
                  </p-multiSelect>
                </div>
              </div>
              <!-- <div class="form-group first dropdown">
                <label class="form-label">Saved Client Filter</label>
                <p-dropdown appendTo="body" [options]="clientGroup" placeholder="Select" [(ngModel)]="dataFilter.customer_name" [ngModelOptions]="{standalone: true}" (onChange)="getClientIds()">
                </p-dropdown>
              </div>
              <div class="form-group first dropdown">
                <label class="form-label">Saved Project Filter</label>
                <p-dropdown appendTo="body" [options]="projectGroup" placeholder="Select" [(ngModel)]="dataFilter.project_name" [ngModelOptions]="{standalone: true}" (onChange)="getProjectIds()">
                </p-dropdown>
              </div> -->
              <div class="PL-border">
                <div class="form-group">
                  <label class="form-label background">
                    <div class="width-65">
                      {{ showEmployeeFilter ? 'Saved Employee Filter' : 'Employee' }}
                    </div>
                    <div class="form-group pt-0 save-filter-radio">
                      <div class="form-check form-check-inline mt-1">
                        <input
                          class="form-check-input"
                          type="radio"
                          id="saveEmployeeFilter"
                          [checked]="showEmployeeFilter"
                          autocomplete="off"
                          (click)="showSaveEmployeeFilterSelected()"
                        />
                        <label class="mb-0" for="saveEmployeeFilter"> Use Saved Employee Filter </label>
                      </div>
                    </div>
                  </label>
                </div>
                <div class="form-group first dropdown p-2" *ngIf="showEmployeeFilter">
                  <p-dropdown
                    appendTo="body"
                    placeholder="Select"
                    [(ngModel)]="dataFilter.name"
                    [options]="employeeGroup"
                    [ngModelOptions]="{ standalone: true }"
                    (ngModelChange)="getEmployeeIds()"
                  >
                  </p-dropdown>
                </div>
                <div class="form-group first dropdown p-2" *ngIf="!showEmployeeFilter">
                  <p-multiSelect
                    appendTo="body"
                    #multiSelectComp4
                    [(ngModel)]="employeeName"
                    [overlayVisible]="false"
                    [showHeader]="false"
                    [options]="employeeList"
                    [ngModelOptions]="{ standalone: true }"
                    placeholder="Select"
                    display="chip"
                    (onChange)="employeeSelected($event)"
                  >
                  </p-multiSelect>
                  <input class="form-check-input" type="radio" [checked]="groupByEmployee" id="groupByEmployee" autocomplete="off" (click)="showGroupByEmplpoyee()" />
                  <label class="mb-0" for="groupByEmployee"> Group by Employee (Calendar) </label>
                </div>
              </div>
              <div class="PL-border">
                <div class="form-group">
                  <label class="form-label background">
                    <div class="width-65">
                      {{ showClientFilter ? 'Saved Client Filter' : 'Client' }}
                    </div>
                    <div class="form-group pt-0 save-filter-radio">
                      <div class="form-check form-check-inline mt-1">
                        <input
                          class="form-check-input"
                          type="radio"
                          id="saveClientFilter"
                          [checked]="showClientFilter"
                          autocomplete="off"
                          (click)="showSaveClientFilterSelected()"
                        />
                        <label class="mb-0" for="saveClientFilter"> Use Saved Client Filter </label>
                      </div>
                    </div>
                  </label>
                </div>
                <div class="form-group first dropdown p-2" *ngIf="showClientFilter">
                  <p-dropdown
                    appendTo="body"
                    placeholder="Select"
                    [(ngModel)]="dataFilter.customer_name"
                    [options]="clientGroup"
                    [ngModelOptions]="{ standalone: true }"
                    (ngModelChange)="getClientIds()"
                  >
                  </p-dropdown>
                </div>
                <div class="form-group first dropdown p-2" *ngIf="!showClientFilter">
                  <p-multiSelect
                    appendTo="body"
                    #multiSelectComp2
                    [(ngModel)]="ClientName"
                    [overlayVisible]="false"
                    [showHeader]="false"
                    [options]="client"
                    [ngModelOptions]="{ standalone: true }"
                    placeholder="Select"
                    display="chip"
                    (onChange)="clientSelected($event)"
                  >
                  </p-multiSelect>
                  <input class="form-check-input" type="radio" [checked]="groupByClient" id="saveClientFilter2" autocomplete="off" (click)="showGroupByClient()" />
                  <label class="mb-0" for="saveClientFilter2"> Group by Client (Calendar) </label>
                </div>
              </div>
              <div class="PL-border">
                <div class="form-group">
                  <label class="form-label background">
                    <div class="width-65">
                      {{ showProjectFilter ? 'Saved Project Filter' : 'Project' }}
                    </div>
                    <div class="form-group pt-0 save-filter-radio">
                      <div class="form-check form-check-inline mt-1">
                        <input
                          class="form-check-input"
                          type="radio"
                          id="saveProjFilter"
                          [checked]="showProjectFilter"
                          autocomplete="off"
                          (click)="showSaveProjectFilterSelected()"
                        />
                        <label class="mb-0" for="saveProjFilter"> Use Saved Project Filter </label>
                      </div>
                    </div>
                  </label>
                </div>
                <div class="form-group first dropdown p-2" *ngIf="showProjectFilter">
                  <p-dropdown
                    appendTo="body"
                    placeholder="Select"
                    [(ngModel)]="dataFilter.project_name"
                    [options]="projectGroup"
                    [ngModelOptions]="{ standalone: true }"
                    (ngModelChange)="getProjectIds()"
                  >
                  </p-dropdown>
                </div>
                <div class="form-group first dropdown p-2" *ngIf="!showProjectFilter">
                  <p-multiSelect
                    appendTo="body"
                    #multiSelectComp3
                    [(ngModel)]="projectName"
                    [overlayVisible]="false"
                    [showHeader]="false"
                    [options]="projectList"
                    [ngModelOptions]="{ standalone: true }"
                    placeholder="Select"
                    display="chip"
                    (onChange)="projectSelected($event)"
                  >
                  </p-multiSelect>
                  <input class="form-check-input" type="radio" [checked]="groupByProject" id="group-by-project" autocomplete="off" (click)="showGroupByProject()" />
                  <label class="mb-0" for="group-by-project"> Group by Project (Calendar) </label>
                </div>
              </div>
              <div class="form-group first dropdown p-2">
                <div class="form-group">
                  <label class="form-label background">Position</label>
                </div>
                <p-dropdown
                  appendTo="body"
                  [options]="positionOpenOrAllList"
                  placeholder="Select"
                  optionLabel="key"
                  optionValue="value"
                  [(ngModel)]="dataFilter.open_positions"
                  [ngModelOptions]="{ standalone: true }"
                ></p-dropdown>
                <!-- <mat-slide-toggle class="mx-3" color="primary" [(ngModel)]="dataFilter.open_position" [ngModelOptions]="{standalone: true}"></mat-slide-toggle> -->
              </div>

              <!-- Tags -->
              <div class="PL-border">
                <div class="form-group">
                  <label class="form-label background">Tags</label>
                </div>
                <div class="form-group first dropdown p-2">
                  <p-treeSelect
                    [(ngModel)]="selectedTags"
                    (ngModelChange)="tagSelected($event)"
                    [options]="groupedCategory?.data"
                    display="chip"
                    [metaKeySelection]="false"
                    selectionMode="checkbox"
                    placeholder="Select Tags"
                    [ngModelOptions]="{ standalone: true }"
                    filterBy="label"
                  >
                  </p-treeSelect>
                </div>
              </div>
              <div class="d-flex justify-content-around">
                <div>
                  <input class="form-check-input" type="radio" id="calendarView" [checked]="defaultView === 'calendar'" (click)="setDefaultView('calendar')" />
                  <label class="mb-0" for="calendarView"> Defaults to Calendar view </label>
                </div>

                <div>
                  <input class="form-check-input" type="radio" id="tableView" [checked]="defaultView === 'table'" (click)="setDefaultView('table')" />
                  <label class="mb-0" for="tableView"> Defaults to Table View </label>
                </div>
              </div>
            </div>
          </div>
          <div class="form-group d-flex flex-wrap justify-content-end align-items-center mt-2 pb-4">
            <button id="addContactCancel" type="button" class="btn-cancel" (click)="clearFilters()">Reset</button>
            <button id="addContactSubmit" type="submit" [isSubmitting]="isSubmitting" class="btn-save">Apply</button>
          </div>
        </form>
      </div>
    </div>
  </mat-drawer>
  <mat-drawer-content class="detail-sidebar-content">
    <ng-container *ngTemplateOutlet="positionReport"></ng-container>
  </mat-drawer-content>
</mat-drawer-container>

<ng-template #positionReport>
  <div class="card card-custom gutter-b">
    <app-card-header
      [cardTitle]="cardTitle"
      [cardSubTitle]="cardSubTitle"
      [buttons]="buttons"
      [exportButton]="exportButtons"
      [showHideButton]="showHideButton"
      [showSplitButton]="true"
      [splitButtonDropDownOption]="splitButtonDropDownOption"
    ></app-card-header>
    <div class="calendar-view-wrapper" [hidden]="!calendarButtonActive">
      <div class="d-flex justify-content-between align-items-start">
        <app-selected-filter-tags
          (filterReset)="resetFilter()"
          (saveFilter)="onSaveFilter()"
          [tags]="tags"
          (onCancel)="onCloseFromFilterPanel($event)"
          (onRemoveStatus)="onRemoveStatusFilter($event)"
        ></app-selected-filter-tags>

        <div>
          <span class="p-buttonset d-flex flex-nowrap align-items-center right-align mt-2 mb-2 mr-4">
            <ng-container *ngFor="let button of buttons">
              <ng-container *ngIf="button?.isSwitcherButton">
                <button
                  [type]="button?.btnType || 'button'"
                  [class]="button?.btnClass"
                  [ngClass]="{
                    'f-s-20': button?.btnSvg,
                    'switch-active': button?.isActive
                  }"
                  (click)="onAction(button)"
                  [isSubmitting]="button?.loading"
                >
                  <ng-container *hasAnyPermission="button?.permissions || []; disableEvent: true">
                    <ng-container *ngIf="button?.btnText">{{ button?.btnText }}</ng-container>
                    <ng-container *ngIf="button?.btnSvg">
                      <a title="Filter" class="btn btn-icon btn-sm icon-background" [ngClass]="{ 'pointer-disable': downloadButtonDisable }">
                        <span [inlineSVG]="'assets/media/svg/icons/' + button?.btnSvg + '.svg'" cacheSVG="true" class="svg-icon svg-icon-md"> </span>
                      </a>
                    </ng-container>
                    <ng-container *ngIf="button?.btnIcon">
                      <fa-icon [icon]="button?.btnIcon"></fa-icon>
                    </ng-container>
                  </ng-container>
                </button>
              </ng-container>
            </ng-container>
          </span>
        </div>
      </div>
      <div class="calendar-view calendar-filter-panel">
        <div class="calendar-filter-body" style="width: 100%">
          <div class="w-50">
            <span class="p-buttonset d-flex flex-wrap align-items-center ml-4">
              <!-- <button type="button" [ngClass]="timelineResolution == calendarViewType.day ? 'btn-save calendar-cust-btn calendar-cust-save-btn' : 'btn-cancel calendar-cust-btn'" (click)="pageResolutionHandler(calendarViewType.day)" label="Day">Day</button> -->
              <h4 class="mr-3 mb-0">Breakdown</h4>
              <button
                type="button"
                [ngClass]="timelineResolution == calendarViewType.week ? 'btn-save calendar-cust-btn calendar-cust-save-btn' : 'btn-cancel calendar-cust-btn'"
                (click)="pageResolutionHandler(calendarViewType.week)"
                label="Week"
              >
                Week
              </button>
              <button
                type="button"
                [ngClass]="timelineResolution == calendarViewType.month ? 'btn-save calendar-cust-btn calendar-cust-save-btn' : 'btn-cancel calendar-cust-btn'"
                (click)="pageResolutionHandler(calendarViewType.month)"
                label="Month"
              >
                Month
              </button>
              <button
                type="button"
                [ngClass]="timelineResolution == calendarViewType.year ? 'btn-save calendar-cust-btn calendar-cust-save-btn' : 'btn-cancel calendar-cust-btn'"
                (click)="pageResolutionHandler(calendarViewType.year)"
                label="Year"
              >
                Year
              </button>
            </span>
          </div>
        </div>
      </div>
      <p-progressSpinner *ngIf="loadingReport" styleClass="custom-spinner" [style]="{ width: '50px', height: '50px' }" strokeWidth="5" animationDuration="0.7s"></p-progressSpinner>
      <div class="calender-hight">
        <ng-container
          *ngIf="
            (!preparingCalendarData && groupByClientCalenderView) ||
              (groupByEmployeeCalenderView && !preparingCalendarData) ||
              (groupByProjectCalenderView && !preparingCalendarData);
            else normalView
          "
        >
          <!-- if group by otherwise normalView -->
          <mbsc-eventcalendar
            class="md-shift-management-calendar"
            [view]="myView"
            [data]="shifts"
            [options]="calendarOptions"
            [scheduleEventTemplate]="eventTemplate"
            [resourceTemplate]="resourceTemp"
            [resourceHeaderTemplate]="headerTemp"
          >
            <ng-template #resourceTemp let-resource>
              <div [title]="resource?.name" [ngClass]="{ 'md-shift-resource ': resource.children }" [ngStyle]="{ color: resource.children ? resource.children[0].color : '' }">
                <span *ngIf="resource.children" class="md-shift-resource-icon"></span>
                {{ resource.name }}
              </div>
            </ng-template>
            <ng-template #headerTemp>
              <div class="md-resource-header-template-title">
                <div class="md-resource-header-template-seats" *ngIf="groupByClientCalenderView">Client</div>
                <div class="md-resource-header-template-seats" *ngIf="groupByEmployeeCalenderView">Employee</div>
              </div>
            </ng-template>
          </mbsc-eventcalendar>
        </ng-container>
        <ng-template #normalView>
          <mbsc-eventcalendar
            class="md-resource-header-template"
            *ngIf="!preparingCalendarData && !groupByClientCalenderView"
            [view]="myView"
            [data]="myEvents"
            [options]="calendarOptions"
            [scheduleEventTemplate]="eventTemplate"
            [resources]="myResources"
            [resourceTemplate]="resourceTemp"
            [resourceHeaderTemplate]="headerTemp"
          >
            <ng-template #resourceTemp let-resource>
              <div class="md-resource-header-template-cont">
                <div class="md-resource-header-template-seats" style="font-size: 14px; font-weight: normal">
                  {{ resource.customer }}
                </div>
                <div class="md-resource-header-template-name" style="font-size: 14px; font-weight: normal">
                  {{ resource.name }}
                </div>
              </div>
            </ng-template>
            <ng-template #headerTemp>
              <div class="md-resource-header-template-title">
                <div class="md-resource-header-template-seats">Client</div>
                <div class="md-resource-header-template-name">Project</div>
              </div>
            </ng-template>
          </mbsc-eventcalendar>
        </ng-template>
      </div>
      <p-paginator
        [rows]="pageSize"
        [totalRecords]="totalRecords"
        [rowsPerPageOptions]="appConstants.paginationConfig.rowsPerPageOptions"
        [first]="page"
        (onPageChange)="onPageChange($event, true)"
        [showCurrentPageReport]="true"
        currentPageReportTemplate="Displaying {first} - {last} of {totalRecords} records"
      >
      </p-paginator>
    </div>

    <div [hidden]="!tableButtonActive" class="card-body table">
      <div class="d-flex justify-content-between align-items-start">
        <div>
          <app-selected-filter-tags
            (filterReset)="resetFilter()"
            (saveFilter)="onSaveFilter()"
            [tags]="tags"
            (onCancel)="onCloseFromFilterPanel($event)"
            (onRemoveStatus)="onRemoveStatusFilter($event)"
          ></app-selected-filter-tags>
        </div>
        <div class="d-flex flex-nowrap">
          <div>
            <div class="float-right mr-4 py-4 pointer" (click)="isShowHideColumns = !isShowHideColumns" #coulmnToggel>
              <app-filter-icon-shared></app-filter-icon-shared>
            </div>
            <div class="popup-column card">
              <app-filter-table-fields
                *ngIf="isShowHideColumns"
                [selectedColumns]="selectedColumns"
                [frozenCols]="frozenCols"
                dynamicBindingKey="title"
                (onSelectColumChange)="onSelectColumsChange($event)"
              ></app-filter-table-fields>
            </div>
          </div>
          <div class="d-flex flex-nowrap">
            <div class="selected-columns-switcher multiselect-wrapper" [ngClass]="{ 'switcher-flex-end': isShowHideColumns === false }">
              <div>
                <span class="p-buttonset d-flex flex-nowrap align-items-center right-align mb-2 mr-4">
                  <ng-container *ngFor="let button of buttons">
                    <ng-container *ngIf="button?.isSwitcherButton">
                      <button
                        [type]="button?.btnType || 'button'"
                        [class]="button?.btnClass"
                        [ngClass]="{
                          'f-s-20': button?.btnSvg,
                          'switch-active': button?.isActive
                        }"
                        (click)="onAction(button)"
                        [isSubmitting]="button?.loading"
                      >
                        <ng-container *hasAnyPermission="button?.permissions || []; disableEvent: true">
                          <ng-container *ngIf="button?.btnText">{{ button?.btnText }}</ng-container>
                          <ng-container *ngIf="button?.btnSvg">
                            <a title="Filter" class="btn btn-icon btn-sm icon-background" [ngClass]="{ 'pointer-disable': downloadButtonDisable }">
                              <span [inlineSVG]="'assets/media/svg/icons/' + button?.btnSvg + '.svg'" cacheSVG="true" class="svg-icon svg-icon-md"> </span>
                            </a>
                          </ng-container>
                          <ng-container *ngIf="button?.btnIcon">
                            <fa-icon [icon]="button?.btnIcon"></fa-icon>
                          </ng-container>
                        </ng-container>
                      </button>
                    </ng-container>
                  </ng-container>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div [ngClass]="positionReportData.length < 14 ? 'active' : ''">
        <ng-container *ngIf="showApplyMsg">
          <p class="filter-note ml-5 apply-filter-msg">Please Apply Filter To Load The Positions Report.</p>
        </ng-container>
        <p-table
          [sortField]="sortFieldName"
          [resizableColumns]="true"
          responsiveLayout="scroll"
          [sortOrder]="sortOrderNumber"
          [columns]="frozenCols"
          #staffedReportTable
          [value]="positionReportData"
          styleClass="p-datatable-customers"
          [style]="{ overflow: 'auto!important' }"
          dataKey="position.id"
          [loading]="loading"
          [scrollable]="true"
          [paginator]="loading ? false : showPaginator ? true : false"
          [rows]="pageSize"
          [lazy]="true"
          [rowsPerPageOptions]="appConstants.paginationConfig.rowsPerPageOptions"
          [totalRecords]="totalRecords"
          [scrollHeight]="positionReportData.length < 50 ? tableHeight : height"
          currentPageReportTemplate="Displaying {first} - {last} of {totalRecords} records"
          editMode="row"
          [first]="page"
          (onLazyLoad)="lazyLoadTable($event)"
          (onPage)="onPageChange($event)"
          [showCurrentPageReport]="true"
          [tableStyle]="{ width: 'calc(100vw - 302px)' }"
        >
          <ng-template pTemplate="header">
            <tr>
              <th *ngIf="_pCols.includes('customer_name')" id="customer_name" pSortableColumn="customer_name" class="header-width client-width" pResizableColumn>
                Client<p-sortIcon field="customer_name"></p-sortIcon>
              </th>

              <th *ngIf="_pCols.includes('project_name')" id="project_name" pSortableColumn="project_name" class="header-width project-width" pResizableColumn>
                Project<p-sortIcon field="project_name"></p-sortIcon>
              </th>
              <th *ngIf="_pCols.includes('project_status')" id="project_status" pSortableColumn="project_status" class="header-width status-width" pResizableColumn>
                Status<p-sortIcon field="project_status"></p-sortIcon>
              </th>
              <th *ngIf="_pCols.includes('position_name')" id="position" pSortableColumn="name" class="header-width position-width" pResizableColumn>
                Position<p-sortIcon field="name"></p-sortIcon>
              </th>
              <th *ngIf="_pCols.includes('employee_full_name')" id="employee" pSortableColumn="employee_first_name" class="header-width employee-width">
                Employee<p-sortIcon field="employee_first_name"></p-sortIcon>
              </th>
              <th *ngIf="_pCols.includes('employee_type')" id="employee-type" class="header-width skill-width" pResizableColumn>Skill Set</th>
              <ng-container *ngIf="_pCols.includes('bill_rate')">
                <th id="bill-rate" pSortableColumn="bill_rate" class="header-width billrate-width" *ngIf="showBillRate">Bill Rate<p-sortIcon field="bill_rate"></p-sortIcon></th>
              </ng-container>
              <th
                *ngIf="_pCols.includes('daily_billable_hours')"
                id="daily_billable_hours"
                pSortableColumn="daily_billable_hours"
                class="header-width hours-width"
                pResizableColumn
              >
                Hours (Daily)<p-sortIcon field="daily_billable_hours"></p-sortIcon>
              </th>
              <th
                *ngIf="_pCols?.includes('weekly_billable_hours')"
                id="weekly_billable_hours"
                pSortableColumn="daily_billable_hours"
                class="header-width hours-width"
                pResizableColumn
              >
                Hours (Weekly)<p-sortIcon field="daily_billable_hours"></p-sortIcon>
              </th>
              <th *ngIf="_pCols?.includes('cost')" id="cost" pSortableColumn="cost" class="header-width hours-width" pResizableColumn>
                Hourly Cost<p-sortIcon field="cost"></p-sortIcon>
              </th>
              <th
                *ngIf="_pCols?.includes('billable_hours')"
                id="daily_billable_hours"
                pSortableColumn="position.total.billable_hours"
                class="header-width hours-width"
                pResizableColumn
              >
                Total Projected Hours<p-sortIcon field="position.total.billable_hours"></p-sortIcon>
              </th>
              <th *ngIf="_pCols.includes('actual_hours')" id="daily_billable_hours" pSortableColumn="position.total.actual_hours" class="header-width hours-width" pResizableColumn>
                Total Actual Hours<p-sortIcon field="position.daily_billable_hours"></p-sortIcon>
              </th>
              <th *ngIf="_pCols.includes('variance')" id="daily_billable_hours" class="header-width hours-width" pResizableColumn>Total Variance</th>
              <th *ngIf="_pCols.includes('start_date')" id="start_date" pSortableColumn="start_date" class="header-width-date text-number-right start-end-date" pResizableColumn>
                Start Date<p-sortIcon field="start_date"></p-sortIcon>
              </th>
              <th *ngIf="_pCols.includes('end_date')" id="end_date" pSortableColumn="end_date" class="header-width-date text-number-right start-end-date" pResizableColumn>
                End Date<p-sortIcon field="end_date"></p-sortIcon>
              </th>
              <th *ngIf="_pCols.includes('tags')" id="tagging" class="header-width-date text-number-right tags-width">Tags</th>

              <ng-container *ngFor="let item of extendFields; let index = index">
                <ng-container *ngFor="let filed of item?.jsonData?.extendedFieldsConfig; let index2 = index">
                  <ng-container *ngIf="filed?.component">
                    <ng-container *ngFor="let filedDetails of filed?.fields; let index3 = index">
                      <th class="extended-field-header" pResizableColumn colspan="1" *ngIf="checkSelectedColumn(filed?.component, filedDetails?.name)">
                        {{ filed?.component }}: {{ filedDetails?.name | titlecase }}
                      </th>
                    </ng-container>
                  </ng-container>
                </ng-container>
              </ng-container>

              <ng-container *hasAnyPermission="permissionModules.VIEW_BILL_RATE; hideTemplate: true">
                <ng-container *ngIf="_pCols.includes('revenue')">
                  <th *ngFor="let col of tableHeaders" class="monthly-data-width" pResizableColumn>Revenue In {{ col.monthLabel }}</th>
                </ng-container>
                <ng-container *ngIf="_pCols.includes('monthly_cost')">
                  <th *ngFor="let col of tableHeaders" class="monthly-data-width" pResizableColumn>Cost In {{ col.monthLabel }}</th>
                </ng-container>
                <ng-container *ngIf="_pCols.includes('margins')">
                  <th *ngFor="let col of tableHeaders" class="monthly-data-width" pResizableColumn>Margin In {{ col.monthLabel }}</th>
                </ng-container>
              </ng-container>
              <ng-container *ngIf="_pCols.includes('monthly_hours')">
                <th *ngFor="let col of tableHeaders" class="monthly-data-width" pResizableColumn>Projected Hours In {{ col.monthLabel }}</th>
              </ng-container>
              <ng-container *ngIf="_pCols.includes('monthly_ActualHours')">
                <th *ngFor="let col of tableHeaders" class="monthly-data-width" pResizableColumn>Actual Hours In {{ col.monthLabel }}</th>
              </ng-container>
              <ng-container *ngIf="_pCols.includes('monthly_Variance')">
                <th *ngFor="let col of tableHeaders" class="monthly-data-width" pResizableColumn>Variance In {{ col.monthLabel }}</th>
              </ng-container>
              <th id="actions" class="header-width-action header-width text-center pt-0"></th>
            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-data let-editing="editing" let-ri="rowIndex">
            <tr [pEditableRow]="data" [ngClass]="{ 'editing-row': editing }">
              <td *ngIf="_pCols.includes('customer_name')" class="header-width" class="body-name client-width">
                <span class="ellipses" [title]="data?.position?.project?.customer?.name">{{ data?.position?.project?.customer?.name }}</span>
              </td>
              <td *ngIf="_pCols.includes('project_name')" class="header-width" class="body-name project-width">
                <a class="ellipses" *hasAnyPermission="permissionModules.MANAGE_PROJECT; disableEvent: true" [routerLink]="[appRoutes.EDIT_PROJECT, data?.position?.project?.id]">
                  <span class="ellipses" [title]="data?.position?.project?.name">{{ data?.position?.project?.name }}</span>
                </a>
              </td>
              <td *ngIf="_pCols.includes('project_status')" class="header-width body-name status-width">
                <span class="ellipses" [title]="data?.position?.project?.status">{{ data?.position?.project?.status }}</span>
              </td>
              <td
                *ngIf="_pCols.includes('position_name')"
                pEditableColumn
                [pEditableColumnDisabled]="true"
                class="body-name position-width"
                [ngClass]="{ 'danger-control': showPositionError }"
              >
                <p-cellEditor>
                  <ng-template pTemplate="input">
                    <input
                      pInputText
                      class="editRow"
                      type="search"
                      class="p-column-filter form-control-custom"
                      [(ngModel)]="editPositionObj.position.name"
                      appSflAutofocus
                      name="position name"
                      [ngModelOptions]="{ standalone: true }"
                      (input)="positionValueChange()"
                    />
                    <small *ngIf="showPositionError" class="position-abs form-text text-danger"> Position name is required. </small>
                  </ng-template>
                  <ng-template pTemplate="output">
                    <span class="ellipses" [title]="data?.position?.name">
                      {{ data?.position?.name }}
                    </span>
                  </ng-template>
                </p-cellEditor>
              </td>
              <td *ngIf="_pCols.includes('employee_full_name')" pEditableColumn [pEditableColumnDisabled]="true" class="body-person employee-width">
                <p-cellEditor>
                  <ng-template pTemplate="input">
                    <p-dropdown
                      [options]="employeeList"
                      class="editRow"
                      [style]="{ width: '100%' }"
                      [(ngModel)]="editPositionObj.position.employee.id"
                      name="edit-employee"
                      placeholder="Select Employee"
                      appendTo="body"
                      [ngModelOptions]="{ standalone: true }"
                      (onChange)="updateEmployeeDetail($event, true)"
                      optionLabel="label"
                    ></p-dropdown>
                  </ng-template>
                  <ng-template pTemplate="output">
                    <span [title]="data?.position?.employee?.employeeFullName">
                      {{ data?.position?.employee?.employeeFullName }}
                    </span>
                  </ng-template>
                </p-cellEditor>
              </td>
              <td *ngIf="_pCols.includes('employee_type')" id="showData" pEditableColumn [pEditableColumnDisabled]="true" class="header-width skill-width">
                <p-cellEditor>
                  <ng-template pTemplate="input">
                    <p-dropdown
                      [options]="skillSetList"
                      class="editRow"
                      [style]="{ width: '100%' }"
                      [(ngModel)]="editPositionObj.position.position_type_id"
                      placeholder="Select"
                      appendTo="body"
                      [ngModelOptions]="{ standalone: true }"
                    ></p-dropdown>
                  </ng-template>
                  <ng-template pTemplate="output">
                    <span [title]="data?.position?.type">
                      {{ data?.position?.type }}
                    </span>
                  </ng-template>
                </p-cellEditor>
              </td>
              <ng-container *ngIf="_pCols.includes('bill_rate')">
                <td id="showData" *ngIf="showBillRate" class="header-width body-rate billrate-width">
                  <span class="ellipses" [title]="data?.position?.bill_rate"> $ {{ data?.position?.bill_rate }}</span>
                </td>
              </ng-container>
              <td
                *ngIf="_pCols.includes('daily_billable_hours')"
                pEditableColumn
                [pEditableColumnDisabled]="true"
                class="body-name hours-width"
                [ngClass]="{ 'danger-control': isErrorInDailyBillableHours() }"
              >
                <p-cellEditor>
                  <ng-template pTemplate="input">
                    <input
                      pInputText
                      class="editRow"
                      type="number"
                      class="p-column-filter form-control-custom"
                      [(ngModel)]="editPositionObj.position.daily_billable_hours"
                      name="Hours"
                      [ngModelOptions]="{ standalone: true }"
                      (ngModelChange)="editDailyBillableHours(editPositionObj)"
                    />
                    <small
                      *ngIf="!editPositionObj.position.daily_billable_hours && editPositionObj?.position?.daily_billable_hours !== 0"
                      class="position-abs form-text text-danger"
                    >
                      Hours is required.
                    </small>
                    <small *ngIf="editPositionObj?.position?.daily_billable_hours < 0" class="position-abs form-text text-danger"> Hours min value is 0. </small>
                    <small *ngIf="editPositionObj?.position?.daily_billable_hours > 8" class="position-abs form-text text-danger"> Hours max value is 8. </small>
                  </ng-template>
                  <ng-template pTemplate="output">
                    <span class="ellipses" [title]="data?.position?.daily_billable_hours">
                      {{ data?.position?.daily_billable_hours }}
                    </span>
                  </ng-template>
                </p-cellEditor>
              </td>
              <td
                *ngIf="_pCols?.includes('weekly_billable_hours')"
                pEditableColumn
                [pEditableColumnDisabled]="true"
                class="body-name hours-width"
                [ngClass]="{ 'danger-control': isErrorInDailyBillableHours() }"
              >
                <p-cellEditor>
                  <ng-template pTemplate="input">
                    <input
                      pInputText
                      class="editRow"
                      type="number"
                      class="p-column-filter form-control-custom"
                      [(ngModel)]="editPositionObj.position.weekly_billable_hours"
                      name="Hours"
                      [ngModelOptions]="{ standalone: true }"
                      (ngModelChange)="editValueChange(editPositionObj)"
                    />
                  </ng-template>
                  <ng-template pTemplate="output">
                    <span class="ellipses" [title]="data?.position?.daily_billable_hours">
                      {{ (data?.position?.daily_billable_hours * 5)?.toFixed(2) }}
                    </span>
                  </ng-template>
                </p-cellEditor>
              </td>
              <td
                *ngIf="_pCols.includes('cost')"
                pEditableColumn
                [pEditableColumnDisabled]="true"
                class="body-name hours-width"
                [ngClass]="{ 'danger-control': isErrorInDailyBillableHours() }"
              >
                <p-cellEditor>
                  <ng-template pTemplate="input">
                    <input
                      pInputText
                      class="editRow"
                      type="number"
                      class="p-column-filter form-control-custom"
                      [(ngModel)]="editPositionObj.position.cost"
                      name="Hours"
                      [ngModelOptions]="{ standalone: true }"
                      (ngModelChange)="editDailyBillableHours(editPositionObj)"
                    />
                    <small *ngIf="!editPositionObj.position.cost && editPositionObj?.position?.cost !== 0" class="position-abs form-text text-danger">
                      Hourly cost is required.
                    </small>
                  </ng-template>
                  <ng-template pTemplate="output">
                    <span class="ellipses" [title]="data?.position?.cost">
                      {{ data?.position?.cost }}
                    </span>
                  </ng-template>
                </p-cellEditor>
              </td>
              <td *ngIf="_pCols?.includes('billable_hours')" pEditableColumn [pEditableColumnDisabled]="true" class="body-name hours-width">
                <p-cellEditor>
                  <ng-template pTemplate="input">
                    <input
                      pInputText
                      class="editRow"
                      type="search"
                      class="p-column-filter form-control-custom"
                      [(ngModel)]="editPositionObj.position.total.billable_hours"
                      name="Hours"
                      [ngModelOptions]="{ standalone: true }"
                      disabled
                    />
                  </ng-template>
                  <ng-template pTemplate="output">
                    <span class="ellipses" [title]="data?.position?.total?.billable_hours">
                      {{ data?.position?.total?.billable_hours }}
                    </span>
                  </ng-template>
                </p-cellEditor>
              </td>
              <td *ngIf="_pCols.includes('actual_hours')" pEditableColumn [pEditableColumnDisabled]="true" class="body-name hours-width">
                <p-cellEditor>
                  <ng-template pTemplate="input">
                    <input
                      pInputText
                      class="editRow"
                      type="search"
                      class="p-column-filter form-control-custom"
                      [(ngModel)]="editPositionObj.position.total.actual_hours"
                      name="Hours"
                      [ngModelOptions]="{ standalone: true }"
                      disabled
                    />
                  </ng-template>
                  <ng-template pTemplate="output">
                    <span class="ellipses" [title]="data?.position?.total?.billable_hours">
                      {{ data?.position?.total?.actual_hours }}
                    </span>
                  </ng-template>
                </p-cellEditor>
              </td>
              <td *ngIf="_pCols.includes('variance')" pEditableColumn [pEditableColumnDisabled]="true" class="body-name hours-width">
                <p-cellEditor>
                  <ng-template pTemplate="input">
                    <input
                      pInputText
                      class="editRow"
                      type="search"
                      class="p-column-filter form-control-custom"
                      [(ngModel)]="editPositionObj.position.total.variance"
                      name="Hours"
                      [ngModelOptions]="{ standalone: true }"
                      disabled
                    />
                  </ng-template>
                  <ng-template pTemplate="output">
                    <span class="ellipses">
                      <span [ngClass]="checkVarianceIsZero(data)"> {{ data?.position?.total?.billable_hours - data?.position?.total?.actual_hours || 0 | number : '1.2-2' }}</span>
                    </span>
                  </ng-template>
                </p-cellEditor>
              </td>
              <td
                *ngIf="_pCols.includes('start_date')"
                pEditableColumn
                [pEditableColumnDisabled]="true"
                class="body-start start-end-date"
                [ngClass]="{ 'danger-control': showStartDateError }"
              >
                <p-cellEditor>
                  <ng-template pTemplate="input">
                    <p-calendar
                      appendTo="body"
                      (onSelect)="editPositionStartDateSelected()"
                      [readonlyInput]="true"
                      inputId="startDate"
                      [(ngModel)]="editPositionObj.position.start_date"
                      [ngModelOptions]="{ standalone: true }"
                    >
                    </p-calendar>
                    <small *ngIf="showStartDateError" class="position-abs form-text text-danger"> Start date is required. </small>
                  </ng-template>
                  <ng-template pTemplate="output">
                    {{ data?.position?.start_date | date : 'MM/dd/yyyy' }}
                  </ng-template>
                </p-cellEditor>
              </td>
              <td
                *ngIf="_pCols.includes('end_date')"
                pEditableColumn
                [pEditableColumnDisabled]="true"
                class="body-end start-end-date"
                [ngClass]="{ 'danger-control': showEndDateError }"
                (clickOutside)="data.showHelpIconData = false"
                style="position: relative"
              >
                <p-cellEditor>
                  <ng-template pTemplate="input">
                    <p-calendar
                      appendTo="body"
                      [minDate]="positionMinDate"
                      [readonlyInput]="true"
                      inputId="startDate"
                      [(ngModel)]="editPositionObj.position.end_date"
                      [ngModelOptions]="{ standalone: true }"
                    >
                    </p-calendar>
                    <small *ngIf="showEndDateError" class="position-abs form-text text-danger"> End date is required. </small>
                  </ng-template>
                  <ng-template pTemplate="output">
                    {{ data?.position?.end_date | date : 'MM/dd/yyyy' }}
                    <ng-template #popOver>
                      <div class="heading">DURATION</div>
                      <div class="sub-heading">
                        {{ getNoOfWeeks(data?.position) }}
                      </div>
                      <div></div>
                    </ng-template>
                    <a placement="right" container="body" (click)="showHelpData(data)" [ngbPopover]="popOver">
                      <fa-icon [icon]="'info-circle'" class="ml-1 help-icon"></fa-icon>
                    </a>
                  </ng-template>
                </p-cellEditor>
              </td>
              <td *ngIf="_pCols.includes('tags')" class="header-width tags-width tags-td">
                <ng-container *ngIf="data?.position?.tags?.length">
                  <span class="ellipses">
                    <span class="taglist">
                      <p-chip *ngFor="let tag of data?.position?.tags; let i = index" (click)="openTagModal(data?.position?.tags)" class="cursor-pointer">
                        <span class="tooltip-hover" [ngbTooltip]="categoryDetails" #t2="ngbTooltip" (mouseenter)="toggleWithCategory(t2, tag)" container="body">{{
                          getTagsCount(tag, i < 2)
                        }}</span>
                      </p-chip>
                    </span>
                  </span>
                  <span class="count cursor-pointer" *ngIf="data?.position?.tags?.length > 2" (click)="openTagModal(data?.position?.tags)">
                    <span class="tag-count">
                      <p-badge [value]="getTagCount(data?.position?.tags)"></p-badge>
                    </span>
                  </span>
                </ng-container>
              </td>

              <ng-container *ngFor="let item of extendFields; let index = index">
                <ng-container *ngFor="let filed of item?.jsonData?.extendedFieldsConfig; let index2 = index">
                  <ng-container *ngIf="filed?.component">
                    <ng-container *ngFor="let filedDetails of filed?.fields; let index3 = index">
                      <ng-container *ngIf="filed?.component == componentType">
                        <td
                          class="extended-field-header show-pointer"
                          colspan="1"
                          *ngIf="checkSelectedColumn(filed?.component, filedDetails?.name)"
                          [id]="filedDetails?.name"
                          (click)="openExtendFiledPopup(data, filedDetails?.name)"
                          [ngClass]="{ 'dynamic-text text-truncate': filedDetails.type === filedType.Text_Area }"
                        >
                          {{ getValueByDBTag(filedDetails?.DBTag, data?.position?.extended_fields) }}
                        </td>
                      </ng-container>
                      <ng-container *ngIf="filed?.component !== componentType">
                        <td
                          *ngIf="checkSelectedColumn(filed?.component, filedDetails?.name)"
                          class="extended-field-header"
                          colspan="1"
                          [id]="filedDetails?.name"
                          [ngClass]="{ 'dynamic-text text-truncate': filedDetails.type === filedType.Text_Area }"
                        >
                          {{ getValueBYExtendFiled(filed?.component, data, filedDetails?.DBTag) }}
                        </td>
                      </ng-container>
                    </ng-container>
                  </ng-container>
                </ng-container>
              </ng-container>

              <ng-container *hasAnyPermission="permissionModules.VIEW_BILL_RATE; hideTemplate: true">
                <ng-container *ngIf="_pCols.includes('revenue')">
                  <td *ngFor="let col of tableHeaders" class="monthly-data-width">
                    ${{ getColumnData(data?.position?.validated_monthly_positions, col.month, col.year, 'revenue', '0') }}
                  </td>
                </ng-container>

                <ng-container *ngIf="_pCols.includes('monthly_cost')">
                  <td *ngFor="let col of tableHeaders" class="monthly-data-width">
                    ${{ getColumnData(data?.position?.validated_monthly_positions, col.month, col.year, 'cost', '0') }}
                  </td>
                </ng-container>

                <ng-container *ngIf="_pCols.includes('margins')">
                  <td *ngFor="let col of tableHeaders" class="monthly-data-width">
                    {{ getColumnData(data?.position?.validated_monthly_positions, col.month, col.year, 'margins', '0') }}%
                  </td>
                </ng-container>
              </ng-container>

              <ng-container *ngIf="_pCols.includes('monthly_hours')">
                <td *ngFor="let col of tableHeaders" class="monthly-data-width">
                  {{ getColumnData(data?.position?.validated_monthly_positions, col.month, col.year, 'billable_hours') }}
                </td>
              </ng-container>

              <ng-container *ngIf="_pCols.includes('monthly_ActualHours')">
                <td *ngFor="let col of tableHeaders" class="monthly-data-width">
                  {{ getColumnData(data?.position?.validated_monthly_positions, col.month, col.year, 'actual_hours') }}
                </td>
              </ng-container>

              <ng-container *ngIf="_pCols.includes('monthly_Variance')">
                <td *ngFor="let col of tableHeaders" class="monthly-data-width" [ngClass]="checkVarianceMonthVise(data, col)">
                  {{ getColumnData(data?.position?.validated_monthly_positions, col.month, col.year, 'monthly_Variance', '0.0') }}
                </td>
              </ng-container>

              <td class="header-width-action text-center action-buttons">
                <div ngbDropdown class="d-inline-block" *ngIf="!editing" container="body">
                  <button class="btn btn-clean btn-sm btn-icon btn-icon-md btn-expand" id="dropdownBasic1" ngbDropdownToggle [disabled]="positionEdit">
                    <em class="flaticon-more"></em>
                  </button>
                  <div ngbDropdownMenu aria-labelledby="dropdownBasic1" isOpen="false" placement="left">
                    <button ngbDropdownItem pInitEditableRow (click)="editPositionDetail(data)">Edit</button>
                    <button ngbDropdownItem (click)="editTags(data?.position?.tags, data.position.id)">Edit Tags</button>
                    <button ngbDropdownItem (click)="duplicatePosition(data, staffedReportTable)">Duplicate</button>
                  </div>
                </div>
                <button
                  *ngIf="editing"
                  pButton
                  pRipple
                  type="button"
                  icon="pi pi-check"
                  class="p-button-rounded p-button-text p-button-success p-mr-2"
                  (click)="saveEditPosition()"
                ></button>
                <button
                  *ngIf="editing"
                  pButton
                  pRipple
                  type="button"
                  pCancelEditableRow
                  icon="pi pi-times"
                  class="p-button-rounded p-button-text p-button-danger"
                  (click)="cancelEditPosition(data, ri)"
                ></button>
              </td>
            </tr>
          </ng-template>
          <ng-template pTemplate="emptymessage">
            <tr *ngIf="!showApplyMsg && positionReportData.length === 0 && !(loading$ | async)">
              <td colspan="6" class="center-align">No Data found.</td>
            </tr>
          </ng-template>
        </p-table>
      </div>
    </div>
  </div>
</ng-template>

<p-dialog
  dismissableMask="true"
  backdrop="false"
  position="top-right"
  [(visible)]="showExportOptionDialog"
  [modal]="true"
  class="export-dialog"
  [draggable]="false"
  [resizable]="false"
>
  <ng-template pTemplate>
    <div class="export-action-listing" *ngIf="showExportOptions" [ngClass]="{ 'pointer-disable': !downloadButtonDisable }">
      <button pButton class="btn p-button-text mb-2 mt-2" icon="pi pi-file-o" iconPos="left" (click)="exportReport('csv')">Export CSV</button>
      <button pButton class="btn p-button-text mb-2" icon="pi pi-file-pdf" iconPos="left" (click)="exportReport('pdf')">Export PDF</button>
      <button pButton class="btn p-button-text mb-2" icon="pi pi-file-excel" iconPos="left" (click)="exportReport('excel')">Export Excel</button>
    </div>
  </ng-template>
</p-dialog>

<p-dialog
  dismissableMask="true"
  backdrop="false"
  position="top-right"
  [(visible)]="showFilterListDialog"
  [modal]="true"
  class="filter-dialog"
  [draggable]="false"
  [resizable]="false"
>
  <ng-template pTemplate>
    <div class="filter-listing" *ngIf="showSavedFilter">
      <input type="search" class="form-control mb-2" placeholder="Search" (input)="searchFilters($event.target.value)" />
      <div *hasAnyPermission="permissionModules.VIEW_SHARE_FILTER; hideTemplate: true" class="title">Shared Filters</div>
      <ng-container *hasAnyPermission="permissionModules.VIEW_SHARE_FILTER; hideTemplate: true">
        <span *ngIf="sharedFilters?.length; else noData">
          <div
            class="form-check filter-body"
            *ngFor="let filterOption of sharedFilters"
            [ngClass]="{
              'selected-filter': filterOption?.query_filter?.id === selectedFilterFormControl?.value?.query_filter?.id
            }"
          >
            <label class="form-check-label">
              <input
                [formControl]="selectedFilterFormControl"
                (ngModelChange)="applySelectedFilterAndUpdateUrl()"
                type="radio"
                class="form-check-input custom-radio"
                [value]="filterOption"
                name="filteroption"
              />{{ filterOption?.query_filter?.name }}
            </label>
            <div class="filter-icons">
              <ng-container *hasAnyPermission="permissionModules.MANAGE_SHARE_FILTER; hideTemplate: true">
                <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary copy-icon" (click)="copyLinkToTheFilter(filterOption?.query_filter?.id)">
                  <em title="Copy Filter Link" class="fa-regular fa-copy"></em>
                </a>
                <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary share-icon" (click)="unShareFilter(filterOption)">
                  <em class="fa-solid fa-share" title="UnShare Filter"></em>
                </a>
                <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary">
                  <span title="Edit" [inlineSVG]="'assets/media/svg/icons/edit.svg'" cacheSVG="true" class="svg-icon svg-icon-md" (click)="editFilter(filterOption)"> </span>
                </a>
                <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary">
                  <span title="Delete" [inlineSVG]="'assets/media/svg/icons/delete.svg'" cacheSVG="true" class="svg-icon svg-icon-md" (click)="deleteFilter(filterOption)"> </span>
                </a>
              </ng-container>
            </div>
          </div>
        </span>
      </ng-container>

      <ng-template #noData>
        <div>-</div>
      </ng-template>
      <div class="title">My Filters</div>
      <span *ngIf="myFilters?.length; else noData">
        <div
          class="form-check filter-body"
          *ngFor="let filterOption of myFilters"
          [ngClass]="{
            'selected-filter': filterOption?.query_filter?.id === selectedFilterFormControl?.value?.query_filter?.id
          }"
        >
          <label class="form-check-label">
            <input
              [formControl]="selectedFilterFormControl"
              (ngModelChange)="applySelectedFilterAndUpdateUrl()"
              type="radio"
              class="form-check-input custom-radio"
              [value]="filterOption"
              name="filteroption"
            />{{ filterOption?.query_filter?.name }}
          </label>
          <div class="filter-icons">
            <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary copy-icon" (click)="copyLinkToTheFilter(filterOption?.query_filter?.id)">
              <em title="Copy Filter Link" class="fa-regular fa-copy"></em>
            </a>
            <a
              class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary"
              (click)="shareFilter(filterOption)"
              *hasAnyPermission="permissionModules.MANAGE_SHARE_FILTER; hideTemplate: true"
            >
              <em class="fa-solid fa-share" title="Share Filter"></em>
            </a>
            <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary">
              <span title="Edit" [inlineSVG]="'assets/media/svg/icons/edit.svg'" cacheSVG="true" class="svg-icon svg-icon-md" (click)="editFilter(filterOption)"> </span>
            </a>
            <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary">
              <span title="Delete" [inlineSVG]="'assets/media/svg/icons/delete.svg'" cacheSVG="true" class="svg-icon svg-icon-md" (click)="deleteFilter(filterOption)"> </span>
            </a>
          </div>
        </div>
      </span>
    </div>
  </ng-template>
</p-dialog>

<p-dialog
  [(visible)]="unableToDownloadPdf"
  header="Unable To Download PDF"
  [style]="{ width: '400px' }"
  class="confirm-dialog-expense"
  [modal]="true"
  [draggable]="false"
  [resizable]="false"
>
  <h5 class="p-m-0">Export to PDF is limited to a specific page width. Please reduce the number of columns or select a different export format option.</h5>
  <ng-template pTemplate="footer">
    <div class="d-flex justify-content-end align-items-center">
      <button type="button" class="btn-save" (click)="closePdfModel()" [isSubmitting]="isSubmitting">Ok</button>
    </div>
  </ng-template>
</p-dialog>

<p-dialog header="Rename" [(visible)]="showEditDialog" [modal]="true" class="confirm-dialog" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
  <div class="form-group first" *ngIf="editFilterObj">
    <input type="text" pInputText class="form-control custom" [(ngModel)]="editFilterObj.query_filter.name" (input)="inputFilterName()" />
    <small *ngIf="showNameError" class="form-text text-danger"> Name is required</small>
  </div>
  <ng-template pTemplate="footer">
    <div class="d-flex justify-content-end align-items-center">
      <button type="button" class="btn-cancel" (click)="closeModal()">Cancel</button>
      <button type="button" class="btn-save" (click)="saveEditFilter()" [isSubmitting]="isSubmitting">Update</button>
    </div>
  </ng-template>
</p-dialog>

<p-dialog header="Delete" [(visible)]="showDeleteDialog" [modal]="true" class="confirm-dialog" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
  <h5 class="p-m-0 delete-title">Do you want to delete "{{ deleteFilterObj?.query_filter?.name }}" Filter?</h5>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center">
      <button type="button" class="btn-cancel" (click)="closeModal()">No</button>
      <button type="button" class="btn-save" (click)="saveDeleteFilter()" [isSubmitting]="isSubmitting">Yes</button>
    </div>
  </ng-template>
</p-dialog>

<p-dialog [header]="shareFilterObj?.header" [(visible)]="showShareDialog" [modal]="true" class="confirm-dialog" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
  <h5 class="p-m-0 delete-title">{{ shareFilterObj?.text }}</h5>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center">
      <button type="button" class="btn-cancel" (click)="closeModal()">No</button>
      <button type="button" class="btn-save" (click)="saveShareFilter()" [isSubmitting]="isSubmitting">Yes</button>
    </div>
  </ng-template>
</p-dialog>

<ng-template #categoryDetails let-tag="tag">
  <p [innerHTML]="getTagCategorySubCategory(tag)"></p>
</ng-template>

<p-dialog header="Applied Tags" [(visible)]="showTagDialog" [modal]="true" class="dialog-applied-tags" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
  <ul>
    <li *ngFor="let tag of selectedTagToView">
      <span [ngbTooltip]="categoryDetails" #t3="ngbTooltip" (mouseenter)="toggleWithCategory(t3, tag)">{{ getExtractedTags(tag) }}</span>
    </li>
  </ul>
</p-dialog>
<mbsc-popup class="md-tooltip" #popup [anchor]="anchor" [options]="popupOptions">
  <div (mouseenter)="mouseEnter()" (mouseleave)="mouseLeave()">
    <div class="md-tooltip-header" [ngStyle]="{ background: bgColor }">
      <span class="md-tooltip-name-age">{{ info }}</span>
      <span class="md-tooltip-time">{{ time }}</span>
    </div>
    <div class="md-tooltip-info">
      <div class="md-tooltip-title">
        <span>
          Position:
          <span class="md-tooltip-status md-tooltip-text"
            ><strong>{{ selectedPosition?.position?.name }}</strong></span
          >
        </span>
        <span>
          Employee:
          <span class="md-tooltip-status md-tooltip-text"
            ><strong>{{ selectedPosition?.position?.employee?.first_name }} {{ selectedPosition?.position?.employee?.last_name }}</strong></span
          >
        </span>
        <span>
          Skill Set:
          <span class="md-tooltip-status md-tooltip-text"
            ><strong>{{ selectedPosition?.position?.type }}</strong></span
          >
        </span>
        <span>
          Daily Billable:
          <span class="md-tooltip-status md-tooltip-text"
            ><strong>{{ selectedPosition?.position?.daily_billable_hours }}</strong></span
          >
        </span>
        <span *ngIf="showBillRate">
          Bill Rate:
          <span class="md-tooltip-status md-tooltip-text"
            ><strong>{{ selectedPosition?.position?.bill_rate }}</strong></span
          >
        </span>
      </div>
    </div>
  </div>
</mbsc-popup>

<ng-template #eventTemplate let-data>
  <div [title]="data?.title" class="md-timeline-template-event" [ngStyle]="{ borderColor: data.color, background: data.color }">
    <div class="md-timeline-template-event-cont">
      <span class="md-timeline-template-title add-ellipsis" style="color: #000; display: block; font-size: 14px" [id]="data.resource"
        >{{ data?.title }} ({{ data?.original?.employeeFirstName }} {{ data?.original?.employeeLastName }})</span
      >
      <span class="md-timeline-template-time" style="color: #000; display: block; font-size: 14px"
        >{{ data?.startDate | date : 'MM/dd/yyy' }} - {{ data?.endDate | date : 'MM/dd/yyy' }}</span
      >
    </div>
  </div>
</ng-template>

<p-dialog
  [header]="'Update ' + updateExtendFiled"
  [(visible)]="showUpdateExtendFiledDialog"
  [modal]="true"
  class="description-dialog"
  [baseZIndex]="10000"
  [draggable]="false"
  [resizable]="false"
  [style]="{ width: '40vw' }"
>
  <app-extended-form [extendFieldsObj]="positionObj?.position?.extended_fields" [filedName]="updateExtendFiled" [componentType]="componentType"> </app-extended-form>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center">
      <button type="button" class="btn-cancel" (click)="closeExtendFiledPopup()">Cancel</button>
      <button type="button" class="btn-save" (click)="saveEditPosition(true)" [isSubmitting]="isSubmitting">Save</button>
    </div>
  </ng-template>
</p-dialog>

// Angular
import { Component, OnDestroy, OnInit, ViewEncapsulation } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
// Translate
import { TranslateService } from '@ngx-translate/core';
import { AuthService } from '@auth/auth.service';
import { AuthNoticeService } from '@auth/auth-notice/auth-notice.service';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { AppConstants } from '@shared/constants/app.constant';
// Auth

@Component({
  selector: 'kt-forgot-password',
  templateUrl: './forgot-password.component.html',
  encapsulation: ViewEncapsulation.None
})
export class ForgotPasswordComponent extends SflBaseComponent implements OnInit, OnDestroy {
  // Public params
  forgotPasswordForm: FormGroup;

  /**
   * Component constructor
   *
   * @param authService
   * @param authNoticeService
   * @param translate
   * @param router
   * @param fb
   * @param cdr
   */
  constructor(
    private readonly authService: AuthService,
    public readonly authNoticeService: AuthNoticeService,
    private readonly translate: TranslateService,
    private readonly router: Router,
    private readonly fb: FormBuilder
  ) {
    super();
  }

  /**
   * On init
   */
  ngOnInit() {
    this.initRegistrationForm();
  }

  /**
   * On destroy
   */
  ngOnDestroy(): void {
    this.isSubmitting = false;
  }

  /**
   * Form initalization
   * Default params, validators
   */
  initRegistrationForm() {
    this.forgotPasswordForm = this.fb.group({
      email: [
        '',
        Validators.compose([Validators.required, Validators.email, Validators.minLength(AppConstants.requiredMinLength), Validators.maxLength(AppConstants.requiredMaxLength)])
      ]
    });
  }

  /**
   * Form Submit
   */
  submit() {
    const controls = this.forgotPasswordForm.controls;
    /** check form */
    if (this.forgotPasswordForm.invalid) {
      Object.keys(controls).forEach((controlName) => controls[controlName].markAsTouched());
      return;
    }

    this.isSubmitting = true;

    const email = controls.email.value;
    this.subscriptionManager.add(
      this.authService.forgetPasswordInit(email).subscribe(
        () => {
          this.authNoticeService.setNotice(this.translate.instant('AUTH.FORGOT.SUCCESS'), 'success');
          this.router.navigateByUrl(this.appRoutes.LOGIN);
          this.isSubmitting = false;
        },
        () => {
          this.isSubmitting = false;
          this.authNoticeService.setNotice(this.translate.instant('AUTH.VALIDATION.NOT_FOUND', { name: this.translate.instant('AUTH.INPUT.EMAIL') }), 'danger');
        }
      )
    );
  }
}

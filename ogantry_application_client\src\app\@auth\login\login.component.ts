// Angular
import { AfterViewInit, ChangeDetectorRef, Component, OnDestroy, OnInit, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute, Router, Params } from '@angular/router';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
// RxJS
import { Observable } from 'rxjs/internal/Observable';
import { Subject } from 'rxjs/internal/Subject';
import { finalize, takeUntil, tap } from 'rxjs/operators';
// Translate
import { TranslateService } from '@ngx-translate/core';
import { environment } from '@environment/environment';
import { AuthNoticeService } from '@auth/auth-notice/auth-notice.service';
import { AuthService } from '@auth/auth.service';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { APP_ROUTES, AppConstants } from '@shared/constants';
import { getModelType, SocialAuthRequest, SocialLoginOption, SocialNewAuthResponse } from '..';

@Component({
  selector: 'kt-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class LoginComponent extends SflBaseComponent implements OnInit, OnDestroy, AfterViewInit {
  // Public params
  loginForm: FormGroup;
  isLoggedIn$: Observable<boolean>;
  buttonProgress = {
    googleLogin: false,
    githubLogin: false,
    microsoftLogin: false
  };

  private readonly unsubscribe: Subject<string>;

  private returnUrl: string;
  socialLoginOpts = SocialLoginOption;
  showPasswordBasedLogin = false;

  /**
   * Component constructor
   *
   * @param router: Router
   * @param auth: AuthService
   * @param authNoticeService: AuthNoticeService
   * @param translate: TranslateService
   * @param fb: FormBuilder
   * @param cdr
   * @param route
   */
  constructor(
    private readonly router: Router,
    private readonly auth: AuthService,
    private readonly authNoticeService: AuthNoticeService,
    private readonly translate: TranslateService,
    private readonly fb: FormBuilder,
    private readonly cdr: ChangeDetectorRef,
    private readonly route: ActivatedRoute
  ) {
    super();
    this.unsubscribe = new Subject();
  }

  ngOnInit(): void {
    this.initLoginForm();
  }

  ngAfterViewInit() {
    setTimeout(() => this.initializeSocialAuthListener(), 1000);
  }

  /**
   * On destroy
   */
  ngOnDestroy(): void {
    this.authNoticeService.setNotice(null);
    this.unsubscribe.next();
    this.unsubscribe.complete();
    this.isSubmitting = false;
  }

  /**
   * Form initalization
   * Default params, validators
   */
  initLoginForm() {
    this.loginForm = this.fb.group({
      email: [
        '',
        Validators.compose([
          Validators.required,
          Validators.email,
          Validators.pattern(/^[a-z0-9]+(?:[_.-][a-z0-9]+)*@[a-z0-9.-]+\.[a-z]{2,4}$/),
          Validators.minLength(AppConstants.requiredMinLength),
          Validators.maxLength(AppConstants.requiredMaxLength)
        ])
      ],
      password: ['', Validators.compose([Validators.required, Validators.minLength(AppConstants.requiredMinLength), Validators.maxLength(AppConstants.passwordMaxLength)])]
    });
  }

  /**
   * Form Submit
   */
  submit() {
    if (!this.showPasswordBasedLogin) {
      this.showPasswordBasedLogin = true;
      return;
    }
    const controls = this.loginForm.controls;
    /** check form */
    if (this.loginForm.invalid) {
      Object.keys(controls).forEach((controlName) => controls[controlName].markAsTouched());
      return;
    }

    this.isSubmitting = true;

    const authData = {
      email: controls.email.value,
      password: controls.password.value
    };
    this.auth
      .login(authData)
      .pipe(
        tap(
          async (user) => {
            if (user) {
              await this.auth.updatePermissionsInCache();
              await this.navigateTo();
            } else {
              this.authNoticeService.setNotice(this.translate.instant('AUTH.VALIDATION.INVALID_LOGIN'), 'danger');
            }
          },
          (err) => this.authNoticeService.setNotice(this.translate.instant('AUTH.VALIDATION.INCORRECT_EMAIL_PASSWORD'), 'danger')
        ),
        takeUntil(this.unsubscribe),
        finalize(() => {
          this.isSubmitting = false;
          this.cdr.markForCheck();
        })
      )
      .subscribe();
  }

  async initiateSocialLogin(socialLoginOption: SocialLoginOption) {
    this.toggleLoaders(true, socialLoginOption);
    const redirectUri = `${environment.socialRedirectUri}`;
    const newSocialAuth = await this.socialNewAuth(redirectUri, socialLoginOption).catch((err) => {
      this.toggleLoaders(false, socialLoginOption);
      this.clearParams();
    });
    if (newSocialAuth) {
      this.openAuthPopup(newSocialAuth.url);
    }
  }

  async socialNewAuth(redirectUri, socialLoginOption: SocialLoginOption): Promise<SocialNewAuthResponse> {
    return new Promise((resolve) => {
      this.auth.socialLogin(redirectUri, socialLoginOption).subscribe(
        (res) => {
          this.authNoticeService.setNotice(null);
          resolve(res.data);
        },
        (error) => {
          this.socialErrorHandler(socialLoginOption, error);
        }
      );
    });
  }

  async initializeSocialAuthListener() {
    // redirect back to the returnUrl before login
    this.route.queryParams.subscribe((params) => {
      this.returnUrl = params.returnUrl || '/';
      if (params.code) {
        const socialLoginType = this.getSocialLoginTypeFromRouteParams(params);
        this.toggleLoaders(true, socialLoginType);
        const socialAuthRequest = getModelType(socialLoginType);
        socialAuthRequest.code = params.code;
        socialAuthRequest.session_params = { state: params.state };
        socialAuthRequest.redirect_uri = `${environment.socialRedirectUri}`;
        this.completeSocialAuth(socialAuthRequest, socialLoginType);
      }
    });
  }

  completeSocialAuth(googleAuthReq: SocialAuthRequest, socialLoginOption: SocialLoginOption) {
    this.subscriptionManager.add(
      this.auth.completeSocialLogin(googleAuthReq, socialLoginOption).subscribe(
        async (res) => {
          await this.auth.updatePermissionsInCache();
          await this.navigateTo();
          this.toggleLoaders(false, socialLoginOption);
          this.authNoticeService.setNotice(null);
        },
        (error) => {
          this.socialErrorHandler(socialLoginOption, error);
        }
      )
    );
  }

  socialErrorHandler(socialLoginOption, error) {
    this.toggleLoaders(false, socialLoginOption);
    this.clearParams();
    this.handleSocialLoginError(socialLoginOption, error);
  }

  clearParams() {}

  toggleLoaders(showLoader: boolean, socialLoginOption: SocialLoginOption) {
    switch (socialLoginOption) {
      case SocialLoginOption.GOOGLE:
        if (showLoader) {
          this.buttonProgress.googleLogin = true;
        } else {
          this.buttonProgress.googleLogin = false;
        }
        break;
      case SocialLoginOption.GITHUB:
        if (showLoader) {
          this.buttonProgress.githubLogin = true;
        } else {
          this.buttonProgress.githubLogin = false;
        }
        break;
      case SocialLoginOption.MICROSOFT:
        if (showLoader) {
          this.buttonProgress.microsoftLogin = true;
        } else {
          this.buttonProgress.microsoftLogin = false;
        }
        break;
    }
  }

  handleSocialLoginError(socialLoginOption: SocialLoginOption, error) {
    if (error?.error?.message?.includes('not pre-authorized')) {
      this.authNoticeService.setNotice('This user is not pre-authorized.  Please contact your OGantry administrator.', 'danger');
    } else {
      switch (socialLoginOption) {
        case SocialLoginOption.GOOGLE:
          this.authNoticeService.setNotice('There was an error during Google authentication. Please try again', 'danger');
          break;
        case SocialLoginOption.GITHUB:
          this.authNoticeService.setNotice('There was an error during Github authentication. Please try again', 'danger');
          break;
        case SocialLoginOption.MICROSOFT:
          this.authNoticeService.setNotice('There was an error during Microsoft authentication. Please try again', 'danger');
          break;
      }
    }
  }

  openAuthPopup(url) {
    window.open(url, '_self');
  }

  private getSocialLoginTypeFromRouteParams(routeParams: Params): SocialLoginOption {
    const scope: string = routeParams?.scope;
    if (!scope) {
      return SocialLoginOption.MICROSOFT;
    }
    if (scope?.includes('google')) {
      return SocialLoginOption.GOOGLE;
    } else if (scope?.includes('github')) {
      return SocialLoginOption.GITHUB;
    }
  }

  navigateTOProject(): void {
    this.router.navigateByUrl(this.appRoutes.MANAGE_PROJECT);
  }

  async navigateTo() {
    const permission = await this.auth.currentPermissions;
    const role = localStorage.getItem('role');
    const navigation = JSON.parse(localStorage.getItem('navigation'));
    if (role.toLowerCase() === 'admin') {
      this.navigateTOProject();
    } else if (role && permission && navigation && navigation[0]?.navigation.value?.permissionModules && navigation[0]?.navigation.value?.page) {
      const isActionPermitted = await this.auth.isPermittedAction(navigation[0]?.navigation?.value?.permissionModules);
      if (isActionPermitted) {
        this.router.navigateByUrl(navigation[0]?.navigation?.value?.page);
      } else {
        this.router.navigateByUrl(APP_ROUTES.FORBIDDEN);
      }
    } else {
      this.navigateTOProject(); // Main page
    }
  }
}

import { Component, OnInit } from '@angular/core';
import { AbstractControl, FormControl, FormGroup, ValidationErrors, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { AuthNoticeService } from '@auth/auth-notice/auth-notice.service';
import { GlobalDetail } from '@entities/administration/administration.model';
import { AdministrationService } from '@entities/administration/administration.service';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { ButtonParams } from '@shared/models';

@Component({
  selector: 'app-add-global-detail',
  templateUrl: './add-global-detail.component.html',
  styleUrls: ['./add-global-detail.component.scss']
})
export class AddGlobalDetailComponent extends SflBaseComponent implements OnInit {
  cardTitle = 'Create Global Detail';
  cardSubTitle = null;
  buttons: ButtonParams[] = [];
  jsonForm: FormGroup;
  loading = false;
  globalDetail = {} as GlobalDetail;
  id: number;

  constructor(private readonly administrationService: AdministrationService, private readonly authNoticeService: AuthNoticeService, private readonly route: ActivatedRoute) {
    super();
  }

  ngOnInit(): void {
    this.route.params.subscribe((params) => {
      this.id = params?.id;
    });
    if (this.id) {
      this.cardTitle = 'Edit Global Detail';
      this.getGlobalDetail();
    }
    this.setBtnParams();
    this.initForm();
    this.authNoticeService.setNotice(null);
  }

  initForm(): void {
    this.jsonForm = new FormGroup({
      name: new FormControl('', [Validators.required]),
      jsonData: new FormControl('', [this.jsonValidator])
    });
  }

  getGlobalDetail(): void {
    this.loading = true;
    this.subscriptionManager.add(
      this.administrationService.getGlobalDetail(this.id).subscribe(
        (res) => {
          this.loading = false;
          if (res?.data?.global_detail) {
            const globalDetail = res?.data?.global_detail;
            this.globalDetail.name = res?.data?.global_detail?.name;
            this.globalDetail.extended_fields = res?.data?.global_detail?.extended_fields;
            this.jsonForm?.patchValue({
              name: this.globalDetail?.name,
              jsonData: JSON.stringify(this.globalDetail.extended_fields, null, 1)
            });
          }
        },
        () => (this.loading = false)
      )
    );
  }

  jsonValidator(control: AbstractControl): ValidationErrors | null {
    try {
      JSON.parse(control?.value);
      if (Object.keys(JSON.parse(control?.value))?.length < 1) {
        return { jsonInvalid: true };
      }
    } catch (e) {
      return { jsonInvalid: true };
    }
  }

  onSubmit(): void {
    if (this.jsonForm?.valid) {
      this.save();
    } else {
      this.jsonForm?.markAsPristine;
      this.jsonForm?.markAsTouched;
      this.jsonForm?.markAsDirty;
    }
  }

  setBtnParams(): void {
    this.buttons = [
      {
        btnClass: 'btn btn-cancel',
        btnText: 'Cancel',
        redirectPath: this.appRoutes.MANAGE_GLOBAL_DETAIL
      },
      {
        btnClass: 'btn-save',
        btnText: 'Save',
        btnType: 'submit',
        action: this.onSubmit.bind(this),
        loading: this.isSubmitting
      }
    ];
  }

  save(): void {
    if (this.jsonForm.valid) {
      this.isSubmitting = true;
      this.setBtnParams();
      const globalDetailsObj = this.formatGlobalDetailObj();

      if (this.id) {
        this.subscriptionManager.add(
          this.administrationService.updateGlobalDetail(globalDetailsObj, this.id).subscribe((res) => {
            this.isSubmitting = false;
            this.onSuccess(res?.data, this.appConstants.updateGlobalDetail);
          })
        );
      } else {
        this.subscriptionManager.add(
          this.administrationService.addGlobalDetail(globalDetailsObj).subscribe(
            (res) => {
              this.onSuccess(res?.data, this.appConstants.createGlobalDetail);
            },
            (err) => this.onError(err)
          )
        );
      }
    }
  }

  formatGlobalDetailObj(): GlobalDetail {
    const jsonData = this.jsonForm?.value?.jsonData;
    const globalDetailsObj = JSON.parse(jsonData);
    return {
      name: this.jsonForm?.value.name,
      extended_fields: globalDetailsObj
    };
  }

  onSuccess(data, successMsg: string): void {
    this.isSubmitting = false;
    this.setBtnParams();
    this.administrationService.globalDetails = data?.global_detail;
    this.authNoticeService.setNotice(successMsg, 'success');
  }

  onError(err): void {
    this.isSubmitting = false;
    this.setBtnParams();
    const error = err?.error;
    this.authNoticeService.setNotice(error.errors, 'danger');
  }
}

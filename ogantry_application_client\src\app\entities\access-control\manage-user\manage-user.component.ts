import { Component, On<PERSON>ni<PERSON>, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { ButtonParams } from '@shared/models';
import { AlertType } from '@shared/models/alert-type.enum';
import { LayoutUtilsService } from '@shared/services/layout-utils.service';
import { LazyLoadEvent } from 'primeng/api';
import { AccessControlService } from '../access-control.service';
import { GetSetCacheFiltersService } from '@shared/services/get-set-cache-filters.service';
import { SaveFilter } from '../../administration/administration.model';
import { IFilter } from '@entities/utilization-management/utilization.model';
import { FilterUser } from './mange-user-model';

export interface Users {
  user_authorizations: UserAuthorization[];
}

export interface User {
  user_authorization: {
    email: string;
    first_name: string;
    id: number;
    last_name: string;
    self_service_storage: string;
    active: boolean;
  };
}
export interface UserAuthorization {
  user_authorization: {
    email: string;
    first_name: string;
    id: number;
    last_name: string;
    self_service_storage: string;
  };
}
@Component({
  selector: 'app-manage-user',
  templateUrl: './manage-user.component.html',
  styleUrls: ['./manage-user.component.scss']
})
export class ManageUserComponent extends SflBaseComponent implements OnInit {
  sortColumnFlag = false;
  statuses: any[];
  sortFieldName: string = 'name';
  sortOrderNumber: number = 1;
  showFilter = false;
  dataFilter: any = { name_search: '' };
  splitButtonDropDownOption = {
    action: this.showFilterOption.bind(this),
    options: [
      // {
      //   label: "Get Stored Filters",
      //   icon: "get-stored-filter-split-button-icon",
      //   command: () => {
      //     // this.showSavedFilterDropDown();
      //   },
      // },
      // {
      //   label: "Save Filter",
      //   icon: "save-filter-split-button-icon",
      //   command: () => {
      //     // this.onSave();
      //   },
      // },
      // {
      //   label: "Reset Filter",
      //   icon: "reset-filter-split-button-icon",
      //   command: () => {
      //     // this.resetFilters();
      //   },
      // },
    ]
  };
  users: Users;
  loading = false;
  cardTitle = 'User Management';
  cardSubTitle = null;
  buttons: ButtonParams[] = [
    {
      btnClass: 'btn-save btn-add-wrapper',
      btnText: 'Add New',
      redirectPath: this.appRoutes.ADD_USER,
      permissions: [this.permissionModules.MANAGE_USER]
    }
  ];
  deleteTypeId: number;
  showDeleteDialog = false;
  permissonDeniedModel: boolean;

  constructor(
    private readonly accessControlService: AccessControlService,
    private readonly cacheFilter: GetSetCacheFiltersService,
    private readonly layoutUtilsService: LayoutUtilsService
  ) {
    super();
  }

  ngOnInit(): void {
    this.getStatus();
    this.checkEmployeeFilterStatus('user');
    this.checkAndApplyLocalFilter('user');
  }

  getUsers(event?: LazyLoadEvent) {
    this.loading = true;
    this.subscriptionManager.add(
      this.accessControlService.getUsers().subscribe(
        (res) => {
          this.loading = false;
          this.users = res?.data;
          for (const role of this.users?.user_authorizations) {
            role.user_authorization.self_service_storage = JSON.parse((role?.user_authorization?.self_service_storage).replace(/'/g, '"'));
          }
        },
        (error) => {
          if (error.error.message === 'Sorry, your account does not permit api access') {
            this.permissonDeniedModel = true;
          }
          this.loading = false;
        }
      )
    );
  }

  confirmDeleteType(id: number) {
    this.deleteTypeId = id;
    this.showDeleteDialog = true;
  }

  closeModal() {
    this.deleteTypeId = null;
    this.showDeleteDialog = false;
  }

  closePermissionModel() {
    this.permissonDeniedModel = false;
  }

  deleteUser() {
    this.isSubmitting = true;
    this.subscriptionManager.add(
      this.accessControlService.deleteUser(this.deleteTypeId).subscribe(
        (res) => {
          this.isSubmitting = false;
          this.closeModal();
          this.layoutUtilsService.showActionNotification('User has been deleted successsfully', AlertType.Success);
          this.getUsers();
        },
        () => (this.isSubmitting = false)
      )
    );
  }

  showFilterOption() {
    this.showFilter = !this.showFilter;
  }

  clearCompanyFilter() {
    this.dataFilter.name_search = '';
  }

  filter() {}

  getStatus() {
    this.statuses = [
      { label: 'All', value: '' },
      { label: 'Active', value: 'true' },
      { label: 'Inactive', value: 'false' }
    ];
  }

  sortColumn() {
    this.sortColumnFlag = true;
  }

  saveApplyFilter() {
    this.cacheFilter.setCacheFilters(this.dataFilter, 'user');
  }

  getSavedFilter() {
    this.cacheFilter.getCacheFilters('user');
  }

  ngOnDestroy() {
    this.dataFilter.filterOpen = this.showFilter;
    this.saveApplyFilter();
  }

  checkEmployeeFilterStatus(page: string): void {
    const filter = this.cacheFilter.getCacheFilters(page);
    if (filter?.filterOpen) {
      this.showFilter = filter.filterOpen;
    }
  }

  checkAndApplyLocalFilter(page: string): void {
    const filter = this.cacheFilter.getCacheFilters(page);
    if (filter) {
      this.dataFilter = filter;
    }
  }

  clearNameFilter(): void {
    delete this.dataFilter.name_search;
  }

  clearEmailFilter() {
    delete this.dataFilter.email;
  }
  clearRoleFilter() {
    delete this.dataFilter.role;
  }

  resetFilter() {
    this.cacheFilter.resetCacheFilters('user');
    console.log((this.dataFilter = new FilterUser()), '');
  }
}

import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ApiUrl } from '@shared/constants';
import { OGantryHttpResponse } from '@shared/models';
import { Observable } from 'rxjs';
import { User, Users } from './manage-user/manage-user.component';

@Injectable({
  providedIn: 'root'
})
export class AccessControlService {
  constructor(private readonly http: HttpClient) {}

  getUsers(): Observable<OGantryHttpResponse<Users>> {
    return this.http.get<OGantryHttpResponse<Users>>(ApiUrl.userManagement);
  }

  getUser(userId?: number): Observable<OGantryHttpResponse<User>> {
    return this.http.get<OGantryHttpResponse<User>>(`${ApiUrl.userManagement}/${userId}`);
  }

  deleteUser(userId?: number): Observable<OGantryHttpResponse<Users>> {
    return this.http.delete<OGantryHttpResponse<Users>>(`${ApiUrl.userManagement}/${userId}`);
  }

  addOrUpdateUser(userDetails: User, userId?: number): Observable<OGantryHttpResponse<User>> {
    if (userId) {
      return this.http.put<OGantryHttpResponse<User>>(`${ApiUrl.userManagement}/${userId}`, userDetails);
    }
    return this.http.post<OGantryHttpResponse<User>>(`${ApiUrl.userManagement}`, userDetails);
  }
}

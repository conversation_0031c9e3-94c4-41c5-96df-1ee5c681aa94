import { Component, OnInit } from '@angular/core';
import { FormArray, FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Params } from '@angular/router';
import { AuthNoticeService } from '@auth/auth-notice/auth-notice.service';
import { TagCategory } from '@entities/administration/administration.model';
import { AdministrationService } from '@entities/administration/administration.service';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { ButtonParams } from '@shared/models';
import { Utility } from '@shared/utils/utils';

@Component({
  selector: 'app-add-update-tag-category',
  templateUrl: './add-update-tag-category.component.html',
  styleUrls: ['./add-update-tag-category.component.scss']
})
export class AddUpdateTagCategoryComponent extends SflBaseComponent implements OnInit {
  cardTitle = 'Create Tag Category';
  cardSubTitle = null;
  buttons: ButtonParams[] = [];
  constructor(
    private readonly administrationService: AdministrationService,
    private readonly authNoticeService: AuthNoticeService,
    private readonly activatedRoute: ActivatedRoute
  ) {
    super();
  }
  createTagCategoryForm: FormGroup;
  categoryId: number;

  ngOnInit(): void {
    this.initForm();
    this.getParams();
  }

  getParams() {
    this.activatedRoute.params.subscribe((params: Params) => {
      this.categoryId = params.categoryId;
      if (this.categoryId) {
        this.cardTitle = 'Edit Tag Category';
        this.setCategoryForm();
      }
      this.setBtnParams();
    });
  }

  setCategoryForm() {
    const tagCategoryData = this.administrationService.tagCategories?.extended_fields?.tagCategory?.filter((category) => category.id === this.categoryId);
    if (tagCategoryData.length) {
      this.createTagCategoryForm.controls['name'].setValue(tagCategoryData[0].name);
      this.createTagCategoryForm.controls['id'].setValue(tagCategoryData[0].id);
      this.createTagCategoryForm.controls['subTagCategory'].setValue(tagCategoryData[0].subTagCategory);
      this.createTagCategoryForm.controls['tags'] = new FormArray([]);
    }
  }

  initForm() {
    this.createTagCategoryForm = new FormGroup({
      id: new FormControl(Utility.getUniqueId(4)),
      name: new FormControl('', Validators.required),
      subTagCategory: new FormArray([]),
      tags: new FormArray([])
    });
  }

  setBtnParams() {
    this.buttons = [
      {
        btnClass: 'btn btn-cancel',
        btnText: 'Cancel',
        redirectPath: this.appRoutes.MANAGE_TAG_CATEGORY
      },
      {
        btnClass: 'btn-save',
        btnText: 'Save',
        btnType: 'submit',
        action: this.onSave.bind(this),
        loading: this.isSubmitting
      }
    ];
  }

  toggleButton(role) {
    role.expand = !role.expand;
  }

  onSuccess(role, successMsg: string) {
    this.isSubmitting = false;
    this.setBtnParams();
    this.administrationService.tagCategories = role.global_detail;
    this.authNoticeService.setNotice(successMsg, 'success');
  }

  onError(err) {
    this.isSubmitting = false;
    this.setBtnParams();
    const error = err.error;
    this.authNoticeService.setNotice(error.errors, 'danger');
  }

  onSave() {
    if (!this.checkFormForValidation(this.createTagCategoryForm)) {
      this.isSubmitting = true;
      this.setBtnParams();
      this.createTagCategoryForm.value.name = this.createTagCategoryForm.value.name.replace(/ /g, '-');
      const categoryData = this.getExtendedData(this.createTagCategoryForm.value);
      if (this.administrationService.tagCategories?.id) {
        this.subscriptionManager.add(
          this.administrationService.updateCategory(categoryData, this.administrationService.tagCategories.id).subscribe(
            (res) => {
              if (this.categoryId) {
                this.onSuccess(res.data, 'Tag category updated successfully');
              } else {
                this.onSuccess(res.data, 'Tag category created successfully');
              }
            },
            (err) => this.onError(err)
          )
        );
      } else {
        this.subscriptionManager.add(
          this.administrationService.addTagCategory(categoryData).subscribe(
            (res) => {
              this.onSuccess(res.data, 'Tag category created successfully');
            },
            (err) => this.onError(err)
          )
        );
      }
    }
  }

  getExtendedData(categoryFormValue: TagCategory) {
    const returnData = { name: '', extended_fields: { tagCategory: [] } };
    categoryFormValue = { ...categoryFormValue };
    if (this.administrationService.tagCategories?.id) {
      returnData.name = this.administrationService.tagCategories.name;
      if (this.administrationService.tagCategories && this.administrationService.tagCategories?.extended_fields?.tagCategory?.length) {
        this.administrationService.tagCategories.extended_fields.tagCategory = this.administrationService.tagCategories.extended_fields.tagCategory.filter(
          (category) => category.id !== categoryFormValue.id
        );
        returnData.extended_fields.tagCategory = this.administrationService.tagCategories.extended_fields.tagCategory;
        returnData.extended_fields.tagCategory.push(categoryFormValue);
      } else {
        returnData.extended_fields.tagCategory = [categoryFormValue];
      }
    } else {
      returnData.name = 'TagCategoryManagement';
      returnData.extended_fields.tagCategory = [categoryFormValue];
    }
    return returnData;
  }

  ngOnDestroy() {
    this.authNoticeService.setNotice(null);
  }
}

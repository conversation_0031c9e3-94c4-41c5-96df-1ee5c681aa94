import { DatePipe } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { IFilter, QueryFilterParams } from '@entities/administration/administration.model';
import { AdministrationService } from '@entities/administration/administration.service';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { Debounce } from '@shared/decorators/debounce.decorator';
import { ButtonParams } from '@shared/models';
import { AlertType } from '@shared/models/alert-type.enum';
import { LayoutUtilsService } from '@shared/services/layout-utils.service';
import { MessageService, TableState } from 'primeng/api';
import { Table } from 'primeng/table';

@Component({
  selector: 'app-manage-holidays',
  templateUrl: './manage-holidays.component.html',
  styleUrls: ['./manage-holidays.component.scss'],
  providers: [MessageService, DatePipe],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ManageHolidaysComponent extends SflBaseComponent implements OnInit {
  holidayData = [];
  loading = true;
  cardTitle = 'Manage Holidays';
  cardSubTitle = null;
  buttons: ButtonParams[] = [
    {
      btnClass: 'btn-save',
      btnText: 'Add New',
      redirectPath: this.appRoutes.CREATE_HOLIDAY,
      permissions: [this.permissionModules.MANAGE_HOLIDAY]
    }
  ];
  @ViewChild('dt') table: Table;
  deleteExceptionId: number;
  showDeleteDialog = false;
  filteredFlag = false;
  dataFilter: IFilter = new IFilter();
  sortColumnFlag = false;
  sortFieldName: string = '';
  sortOrderNumber: number = 1;
  constructor(
    private readonly adminService: AdministrationService,
    private readonly cdf: ChangeDetectorRef,
    private readonly layoutUtilsService: LayoutUtilsService,
    private readonly datePipe: DatePipe,
    private readonly router: Router,
    private readonly activatedRoute: ActivatedRoute
  ) {
    super();
  }

  ngOnInit(): void {}
  @Debounce()
  getHolidays(event?: TableState) {
    let queryFilter: QueryFilterParams = {
      order_by: !this.sortColumnFlag ? (this.dataFilter?.order_by ? this.dataFilter.order_by : this.activeSort(event)) : this.activeSort(event)
    };
    if (!this.sortColumnFlag) {
      this.sortFieldName = this.dataFilter?.order_by ? this.dataFilter.order_by.split(':')[1] : event?.sortField;
      this.sortOrderNumber = this.dataFilter?.order_by ? (this.dataFilter.order_by.split(':')[0] === 'asc' ? 1 : -1) : event?.sortOrder;
    }

    if (this.dataFilter) {
      if (Object.entries(this.dataFilter).length) {
        if (this.filteredFlag) {
          queryFilter.offset = 0;
          event.first = 0;
          this.filteredFlag = false;
        }
        for (const [key] of Object.entries(this.dataFilter)) {
          if (key === 'date') {
            if (this.dataFilter[key]) {
              queryFilter[`${key}`] = this.datePipe.transform(new Date(this.dataFilter[key]), 'yyyy-MM-dd');
            } else {
              delete queryFilter[`${key}`];
            }
          } else {
            if (key !== 'offset' && key !== 'limit' && key !== 'order_by') queryFilter[`${key}`] = this.dataFilter[key];
          }
        }
      }
    }
    delete queryFilter['employee_active'];
    delete queryFilter['employee_inactive'];
    queryFilter = this.queryStringUtil(queryFilter);
    this.loading = true;
    this.holidayData = [];

    this.subscriptionManager.add(
      this.adminService.getManageHolidayWorkExceptions(queryFilter).subscribe(
        (res) => {
          this.holidayData = res?.body?.data?.holidays;
          this.loading = false;
          this.cdf.detectChanges();
        },
        () => (this.loading = false)
      )
    );
  }

  confirmDeleteHoliday(id: number) {
    this.deleteExceptionId = id;
    this.showDeleteDialog = true;
  }

  closeModal() {
    this.deleteExceptionId = null;
    this.showDeleteDialog = false;
  }

  deleteHoliday() {
    this.isSubmitting = true;
    const exceptionData = this.holidayData;
    const id = this.deleteExceptionId;
    this.subscriptionManager.add(
      this.adminService.deleteHolidayWorkException(id).subscribe(
        () => {
          this.isSubmitting = false;
          this.closeModal();
          this.holidayData = exceptionData.filter((p) => p.holiday.id !== id);
          this.cdf.detectChanges();
          this.layoutUtilsService.showActionNotification('Holiday has been archived successsfully', AlertType.Success);
        },
        () => (this.isSubmitting = true)
      )
    );
  }

  clearFilter(key) {
    delete this.dataFilter[key];
    this.filter();
  }

  // used to apply filter on the table
  filter() {
    if (!this.dataFilter.description_search) {
      this.dataFilter.description_search = null;
    }
    if (!this.dataFilter.date) {
      this.dataFilter.date = null;
    }
    this.filteredFlag = true;
    this.holidayData = [];
    this.loading = true;
    this.getHolidays(this.table);
  }

  activeSort(event?: TableState) {
    if (event?.sortField) {
      if (event.sortOrder === 1) {
        return 'asc:' + event.sortField;
      } else {
        return 'desc:' + event.sortField;
      }
    }
    return null;
  }

  // used to take query string param and removes values which are not set in the query string
  queryStringUtil(queryStringParam: QueryFilterParams) {
    const queryFilter: QueryFilterParams = {};
    if (queryStringParam) {
      for (const [key] of Object.entries(queryStringParam)) {
        if (queryStringParam[key] === '' || queryStringParam[key] === null || queryStringParam[key] === undefined) {
          delete queryStringParam[key];
        }
      }
    }
    return queryStringParam;
  }

  resetFilters() {
    // reseting the url by removing the filter query string
    this.router.navigate([], { relativeTo: this.activatedRoute, queryParams: { filterId: null }, queryParamsHandling: 'merge' });
    // reset the filter selection as well
    this.dataFilter = new IFilter();
    this.filter();
  }

  clearStartDate() {
    delete this.dataFilter.date;
    this.filter();
  }
}

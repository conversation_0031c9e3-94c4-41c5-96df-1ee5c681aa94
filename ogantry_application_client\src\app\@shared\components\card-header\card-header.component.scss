@import '/src/assets/sass/components/variables.bootstrap';

.f-s-20 {
  font-size: 20px !important;
}

.card-header.custom {
  align-items: center !important;
  .card-title {
    display: block !important;
    p {
      font-size: 13px;
      margin-bottom: 0;
    }
  }
}

.btn-add-wrapper {
  margin-right: 1rem;
}

.icon-background {
  background-color: $primary !important;
  padding: 0.2rem;
}

.filter-btn-wrapper {
  width: 46.53px !important;
  height: 38px !important;
}
::ng-deep .export-btn .p-splitbutton {
  min-width: 80px;
  height: 36px;
  display: flex;
  justify-content: center;
}

::ng-deep .export-btn .p-splitbutton .p-splitbutton-defaultbutton {
  border-top-left-radius: 6px !important;
  border-bottom-left-radius: 6px !important;
}

::ng-deep .export-btn .p-button {
  border-radius: 6px;
  border-top-left-radius: 1px;
  border-bottom-left-radius: 1px;
}

::ng-deep .export-btn .p-button:focus {
  box-shadow: none;
}

::ng-deep .export-btn .p-menu {
  width: 7.5rem;
}

::ng-deep .export-btn .p-menu .p-menuitem-link .p-menuitem-icon {
  font-size: 1.5rem;
  color: $primary;
}

::ng-deep .export-btn .p-menu .p-menuitem-link .p-menuitem-text {
  color: #242424;
  font-family: Poppins;
  font-size: 13px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 20px;
}

::ng-deep .export-btn .p-button-label {
  color: $white;
  font-family: Poppins;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 21px;
  text-align: center;
}

::ng-deep .btn-filter-icon {
  background: $primary;
  border-radius: 6px;
  margin-right: 1rem;
}

@media (max-width: 500px) {
  .card-header.custom .card-title {
    margin: 10px 0 !important;
  }

  .card-label {
    font-weight: 800 !important;
    font-size: 12px !important;
  }
}

::ng-deep .p-button {
  background: $primary;
  border-color: $primary;
}

::ng-deep .p-button:enabled:hover {
  background: #574985;
  border-color: #574985;
}
.btn-switcher {
  border-color: transparent;
}

.btn-switcher.btn-right {
  border-radius: 0 6px 6px 0;
  svg {
    background: #574985;
  }
}

.btn-switcher.btn-left {
  border-radius: 6px 0 0 6px;
  svg {
    background: #574985;
  }
}

.btn-switcher.switch-active {
  background: #574985;
  svg {
    background: $white;
  }
}
::ng-deep {
  .showHideButton button.p-element.p-splitbutton-defaultbutton.p-button.p-component {
    display: none;
  }
  .showHideButton .pi-chevron-down:before {
    color: $white;
  }
}
::ng-deep .showHideButton .p-splitbutton {
  height: 36px;
  display: flex;
  justify-content: center;
}

::ng-deep .showHideButton .p-splitbutton .p-splitbutton-defaultbutton {
  border-top-left-radius: 6px !important;
  border-bottom-left-radius: 6px !important;
}

::ng-deep .showHideButton .p-button {
  border-radius: 6px;
}

::ng-deep .showHideButton .p-button:focus {
  box-shadow: none;
}

::ng-deep .showHideButton .p-menu .p-menuitem-link .p-menuitem-icon {
  font-size: 1.5rem;
  color: $primary;
}

::ng-deep .showHideButton .p-menu .p-menuitem-link .p-menuitem-text {
  color: #242424;
  font-family: Poppins;
  font-size: 13px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 20px;
}

::ng-deep .showHideButton .p-button-label {
  color: $white;
  font-family: Poppins;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0;
  line-height: 21px;
  text-align: center;
}

.split-button-wrapper {
  ::ng-deep .pi-filter:before {
    font-size: 17px;
  }

  ::ng-deep .p-button:focus {
    box-shadow: none !important;
  }
  ::ng-deep .pi-chevron-down::before {
    color: $white !important;
  }

  ::ng-deep .p-button-icon {
    font-weight: normal !important;
  }
  ::ng-deep .p-button.p-button-icon-only {
    width: 2.37rem !important;
  }
}

::ng-deep .get-stored-filter-split-button-icon {
  background: url('../../../../assets/media/svg/icons/filter-list.svg') no-repeat center center;
  width: 16px;
  height: 16px;
}

::ng-deep.save-filter-split-button-icon {
  background: url('../../../../assets/media/svg/icons/save.svg') no-repeat center center;
  width: 16px;
  height: 16px;
}
::ng-deep .reset-filter-split-button-icon {
  background: url('../../../../assets/media/svg/icons/clear-filter.svg') no-repeat center center;
  width: 16px;
  height: 16px;
}
::ng-deep .p-tieredmenu .p-menuitem-link .p-menuitem-text {
  color: $white !important;
}

::ng-deep .p-tieredmenu.p-tieredmenu-overlay {
  background-color: $primary;
}

::ng-deep .p-tieredmenu .p-menuitem-link:not(.p-disabled):hover {
  background-color: $primary;
}
::ng-deep .p-tieredmenu .p-menuitem.p-menuitem-active > .p-menuitem-link {
  background-color: $primary;
}

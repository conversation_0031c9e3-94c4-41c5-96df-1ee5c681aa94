"use strict";var KTDialog=function(e){var t,n=this,i=KTUtil.getBody(),o={placement:"top center",type:"loader",width:100,state:"default",message:"Loading..."},r={construct:function(e){return r.init(e),n},init:function(e){n.events=[],n.options=KTUtil.deepExtend({},o,e),n.state=!1},show:function(){return r.eventTrigger("show"),t=document.createElement("DIV"),KTUtil.setHTML(t,n.options.message),KTUtil.addClass(t,"dialog dialog-shown"),KTUtil.addClass(t,"dialog-"+n.options.state),KTUtil.addClass(t,"dialog-"+n.options.type),"top center"==n.options.placement&&KTUtil.addClass(t,"dialog-top-center"),i.appendChild(t),n.state="shown",r.eventTrigger("shown"),n},hide:function(){return t&&(r.eventTrigger("hide"),t.remove(),n.state="hidden",r.eventTrigger("hidden")),n},eventTrigger:function(e){for(var t=0;t<n.events.length;t++){var i=n.events[t];if(i.name==e){if(1!=i.one)return i.handler.call(this,n);if(0==i.fired)return n.events[t].fired=!0,i.handler.call(this,n)}}},addEvent:function(e,t,i){return n.events.push({name:e,handler:t,one:i,fired:!1}),n}};return n.setDefaults=function(e){o=e},n.shown=function(){return"shown"==n.state},n.hidden=function(){return"hidden"==n.state},n.show=function(){return r.show()},n.hide=function(){return r.hide()},n.on=function(e,t){return r.addEvent(e,t)},n.one=function(e,t){return r.addEvent(e,t,!0)},r.construct.apply(n,[e]),n};"undefined"!=typeof module&&void 0!==module.exports&&(module.exports=KTDialog);

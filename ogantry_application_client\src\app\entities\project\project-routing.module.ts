import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { CreateProjectComponent } from './create-project/create-project.component';
import { ManageProjectComponent } from './manage-project/manage-project.component';
import { PageRefreshGuard } from '@shared/services/page-refresh-guard.service';
import { PermissionAuthGuard } from '@shared/services/permission-auth-guard.service';
import { PermissionModules } from '@shared/models/permission.enum';
import { CheckAuthResolver } from '@shared/services';
import { CanDeactivateGuard } from '@entities/manage-people/guards/can-deactivate.guard';
const routes: Routes = [
  {
    path: '',
    redirectTo: 'manage'
  },
  {
    path: 'manage',
    component: ManageProjectComponent,
    canActivate: [PermissionAuthGuard],
    data: {
      permissionModules: [PermissionModules.VIEW_PROJECT]
    },
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'create',
    component: CreateProjectComponent,
    canActivate: [PermissionAuthGuard],
    canDeactivate: [CanDeactivateGuard],
    data: {
      permissionModules: [PermissionModules.MANAGE_PROJECT]
    },
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'create/:projectId',
    component: CreateProjectComponent,
    canActivate: [PageRefreshGuard, PermissionAuthGuard],
    canDeactivate: [CanDeactivateGuard],
    data: {
      permissionModules: [PermissionModules.MANAGE_PROJECT]
    },
    resolve: { authState: CheckAuthResolver }
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ProjectRoutingModule { }

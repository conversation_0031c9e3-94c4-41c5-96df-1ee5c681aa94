import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { AdministrationService } from '@entities/administration/administration.service';
import { MonthlyExpenseType } from '@entities/project/project.model';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { ButtonParams } from '@shared/models';
import { AlertType } from '@shared/models/alert-type.enum';
import { LayoutUtilsService } from '@shared/services/layout-utils.service';
import { LazyLoadEvent } from 'primeng/api';

@Component({
  selector: 'app-manage-monthly-expense-type',
  templateUrl: './manage-monthly-expense-type.component.html',
  styleUrls: ['./manage-monthly-expense-type.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ManageMonthlyExpenseTypeComponent extends SflBaseComponent implements OnInit {
  expenseTypes: MonthlyExpenseType[] = [];
  loading = false;
  cardTitle = 'Manage Monthly Expense Types';
  cardSubTitle = null;
  buttons: ButtonParams[] = [
    {
      btnClass: 'btn-save',
      btnText: 'Add New',
      redirectPath: this.appRoutes.CREATE_MONTHLY_EXPENSE_TYPE,
      permissions: [this.permissionModules.MANAGE_MONTHLY_EXPENSE_TYPE]
    }
  ];

  deleteTypeId: number;
  showDeleteDialog = false;
  constructor(private readonly adminService: AdministrationService, private readonly cdf: ChangeDetectorRef, private readonly layoutUtilsService: LayoutUtilsService) {
    super();
  }

  ngOnInit(): void {}

  getExpenseTypes(event?: LazyLoadEvent) {
    this.expenseTypes = [];
    this.loading = true;
    this.cdf.detectChanges();
    this.subscriptionManager.add(
      this.adminService.getMonthlyExpenseTypes().subscribe(
        (res) => {
          this.loading = false;
          if (res?.body?.data?.monthly_expense_types) {
            this.expenseTypes = res.body.data.monthly_expense_types;
          }
          this.cdf.detectChanges();
        },
        () => (this.loading = false)
      )
    );
  }

  confirmDeleteType(id: number) {
    this.deleteTypeId = id;
    this.showDeleteDialog = true;
  }

  closeModal() {
    this.deleteTypeId = null;
    this.showDeleteDialog = false;
  }

  deleteType() {
    this.isSubmitting = true;
    this.subscriptionManager.add(
      this.adminService.deleteMonthlyExpenseType(this.deleteTypeId).subscribe(
        (res) => {
          this.isSubmitting = false;
          this.closeModal();
          this.layoutUtilsService.showActionNotification('Monthly Expense Type has been archived successsfully', AlertType.Success);
          this.getExpenseTypes();
          this.cdf.detectChanges();
        },
        () => (this.isSubmitting = false)
      )
    );
  }
}

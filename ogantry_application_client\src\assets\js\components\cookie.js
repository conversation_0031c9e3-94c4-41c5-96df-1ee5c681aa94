"use strict";var KTCookie={getCookie:function(e){var o=document.cookie.match(new RegExp("(?:^|; )"+e.replace(/([\.$?*|{}\(\)\[\]\\\/\+^])/g,"\\$1")+"=([^;]*)"));return o?decodeURIComponent(o[1]):void 0},setCookie:function(e,o,n){n||(n={}),(n=Object.assign({},{path:"/"},n)).expires instanceof Date&&(n.expires=n.expires.toUTCString());var t=encodeURIComponent(e)+"="+encodeURIComponent(o);for(var i in n)if(n.hasOwnProperty(i)){t+="; "+i;var r=n[i];!0!==r&&(t+="="+r)}document.cookie=t},deleteCookie:function(e){setCookie(e,"",{"max-age":-1})}};"undefined"!=typeof module&&void 0!==module.exports&&(module.exports=KTCookie);

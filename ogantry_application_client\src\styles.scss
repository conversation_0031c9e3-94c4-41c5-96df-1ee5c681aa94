/* You can add global styles to this file, and also import other style files */
// Global vendors
@import '~bootstrap/dist/css/bootstrap.css';
@import '~material-design-icons/iconfont/material-icons.css';
@import '~perfect-scrollbar/css/perfect-scrollbar.css';
@import '@angular/material/prebuilt-themes/deeppurple-amber.css';
@import '~primeng/resources/themes/saga-blue/theme.css';
@import '~primeng/resources/primeng.min.css';
@import '~primeicons/primeicons.css';

// Global fonts
@import '~socicon/css/socicon.css';
@import '~@fortawesome/fontawesome-free/css/all.min.css';
@import './assets/plugins/flaticon/flaticon.css';
@import './assets/plugins/flaticon2/flaticon.css';
@import './assets/plugins/keenthemes-icons/font/ki.css';

// Metronic styles
@import './assets/sass/style.angular.scss';
@import './assets/sass/vendors/angular/material/angular-material';
@import './assets/sass/vendors/angular/material/include';

// Default Layout themes
@import './assets/sass/themes/layout/header/base/light.scss';
@import './assets/sass/themes/layout/header/menu/light.scss';
@import './assets/sass/themes/layout/aside/dark.scss';

// Brand themes
.brand-dark {
  @import './assets/sass/themes/layout/brand/dark.scss';
}

.brand-light {
  @import './assets/sass/themes/layout/brand/light.scss';
}

@import './assets/sass/custom';

//hide bootstrap dropdown arrow globally
.dropdown-toggle::after {
  display: none !important;
}

.mat-table-wrapper-dashboard .mat-table {
  min-width: 600px !important;
  width: 100% !important;
}

.position-static {
  position: static;
}

.dropdown-fluid {
  height: 100%;
  .topbar-item {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
  }
}

.mat-dialog-container {
  padding: 0 !important;
}

::-webkit-scrollbar {
  width: 0.5em !important;
  height: 0.5em !important;
}

::-webkit-scrollbar-track {
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3) !important;
}

::-webkit-scrollbar-thumb {
  background-color: darkgray !important;
  outline: 1px solid slategray !important;
}

.dropdown-left {
  left: 60%;
  position: relative !important;
}

:root {
  ::-webkit-scrollbar {
    width: 5px; /* width of the entire scrollbar */
    height: 4px;
  }
  ::-webkit-scrollbar-track {
    background: #d0d7e1; /* color of the tracking area */
  }
  ::-webkit-scrollbar-thumb {
    background-color: darken(#d0d7e1, 20%); /* color of the scroll thumb */
    border-radius: 20px; /* roundness of the scroll thumb */
    border: 3px solid darken(#d0d7e1, 20%); /* creates padding around scroll thumb */
  }
}

.mat-chip.mat-standard-chip {
  background-color: #0000;
  color: #4b3f72;
  min-height: 20px;
  font-size: 12px;
  font-weight: 500;
}

body,
body .p-component {
  font-family: Poppins !important;
  font-size: 12px;
}

thead {
  th:last-child {
    z-index: 0 !important;
  }
}
$color-danger: red;
.required {
  &:after {
    content: ' *';
    color: $color-danger;
  }
}

@import '~@mobiscroll/angular/dist/css/mobiscroll.scss';

.incorrect-toggle-wrapper {
  ::ng-deep .p-inputswitch-slider {
    height: 20px;
  }
  ::ng-deep .p-inputswitch.p-inputswitch-checked:not(.p-disabled) .p-inputswitch-slider {
    background-color: #ea3b52;
  }

  .toggle-text {
    color: #ea3b52;
  }
}

.correction-modal-wrapper {
  ::ng-deep .p-dialog-content {
    min-width: 250px !important;
  }
  .incorrect-data {
    color: #ea3b52;
  }
  .similar-incorrect-msg {
    font-size: 12px;
  }
  .similar-incorrect-number {
    color: #ea3b52;
  }
  .dropdown-wrapper {
    ::ng-deep .p-dropdown {
      width: 170px;
    }

    ::ng-deep .p-dropdown:not(.p-disabled).p-focus {
      box-shadow: none !important;
      border-color: #ced4da !important;
    }
    ::ng-deep .p-dropdown:not(.p-disabled):hover {
      border-color: #ced4da !important;
    }
  }
  ::ng-deep .p-inputtext:enabled:hover {
    border: 1px solid #ced4da;
  }
  ::ng-deep .p-inputtext:enabled:focus {
    box-shadow: none !important;
    border-color: #ced4da;
  }
  .date-picker--wrapper {
    background-color: transparent !important;
    ::ng-deep .p-calendar {
      width: 180px !important;
      .p-button:focus {
        box-shadow: none !important;
      }
    }
  }
  .hours-picker {
    ::ng-deep .p-inputtext {
      width: 120px;
    }
  }
}

::ng-deep .p-datepicker-timeonly {
  min-width: 120px !important;
}

::ng-deep .p-datepicker-calendar-container {
  .p-datepicker-calendar td > .p-ripple {
    width: 2rem !important;
    height: 2rem !important;
  }
}

::ng-deep .p-datatable-scrollable-both .p-datatable-thead > tr > th,
.p-datatable-scrollable-both .p-datatable-tbody > tr > td,
.p-datatable-scrollable-both .p-datatable-tfoot > tr > td,
.p-datatable-scrollable-horizontal .p-datatable-thead > tr > th .p-datatable-scrollable-horizontal .p-datatable-tbody > tr > td,
.p-datatable-scrollable-horizontal .p-datatable-tfoot > tr > td,
.p-datatable-scrollable-both .p-datatable-thead > tr > th {
  flex: 1 1 0 !important;
}

::ng-deep .p-datatable-scrollable-both .p-datatable-tbody > tr > td,
.p-datatable-scrollable-both .p-datatable-tfoot > tr > td,
.p-datatable-scrollable-horizontal .p-datatable-thead > tr > th .p-datatable-scrollable-horizontal .p-datatable-tbody > tr > td,
.p-datatable-scrollable-horizontal .p-datatable-tfoot > tr > td {
  flex: 1 1 0 !important;
}

p-checkbox {
  padding-left: 1rem !important;
}

.p-checkbox-label {
  margin-top: 6px !important;
  font-weight: 600 !important;
}

.fix-p-aliment {
  padding-left: 1rem;
}

::ng-deep p-checkbox > label {
  margin-top: 1px !important;
  font-weight: 600 !important;
}
.repair-csv {
  ::ng-deep .p-checkbox .p-checkbox-box {
    height: 17px !important;
    width: 17px !important;
  }
}

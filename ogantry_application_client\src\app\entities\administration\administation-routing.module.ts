import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { PermissionAuthGuard } from '@shared/services/permission-auth-guard.service';
import { AdminOnlyPermission, PermissionModules } from '@shared/models/permission.enum';
import { PageRefreshGuard } from '@shared/services/page-refresh-guard.service';
import { ManageExpenseTypesComponent } from './expense-types/manage-expense-types/manage-expense-types.component';
import { CreateExpenseTypesComponent } from './expense-types/create-expense-types/create-expense-types.component';
import { ManageEmployeeTypeComponent } from './employee-type/manage-employee-type/manage-employee-type.component';
import { CreateEmployeeTypeComponent } from './employee-type/create-employee-type/create-employee-type.component';
import { CreatePositionTypeComponent } from './position-type/create-position-type/create-position-type.component';
import { ManagePositionTypeComponent } from './position-type/manage-position-type/manage-position-type.component';
import { CreateMonthlyExpenseTypeComponent } from './monthly-expense-types/create-monthly-expense-types/create-monthly-expense-type/create-monthly-expense-type.component';
import { ManageMonthlyExpenseTypeComponent } from './monthly-expense-types/manage-monthly-expense-types/manage-monthly-expense-type/manage-monthly-expense-type.component';
import { CheckAuthResolver } from '@shared/services';
import { CreateHolidaysComponent } from './holidays/create-holidays/create-holidays.component';
import { ManageHolidaysComponent } from './holidays/manage-holidays/manage-holidays.component';
import { ManageWorkExceptionTypeComponent } from './work-exception-types/manage-work-exception-type/manage-work-exception-type.component';
import { CreateWorkExceptionTypeComponent } from './work-exception-types/create-work-exception-type/create-work-exception-type.component';
import { ManageTagCategoryComponent } from './manage-tag-category/manage-tag-category.component';
import { AddUpdateTagCategoryComponent } from './manage-tag-category/add-update-tag-category/add-update-tag-category.component';
import { ManageTagSubCategoryComponent } from './manage-tag-sub-category/manage-tag-sub-category.component';
import { AddUpdateSubCategoryComponent } from './manage-tag-sub-category/add-update-sub-category/add-update-sub-category.component';
import { ManageTagComponent } from './manage-tag/manage-tag.component';
import { AddUpdateTagsComponent } from './manage-tag/add-update-tags/add-update-tags.component';
import { ManageExtendedFiledComponent } from './manage-exteded-filed/manage-exteded-filed.component';
import { AddExtendedFiledComponent } from './manage-exteded-filed/add-extended-filed/add-extended-filed.component';
import { ManageGlobalDetailsComponent } from './manage-global-details/manage-global-details.component';
import { AddGlobalDetailComponent } from './manage-global-details/add-global-detail/add-global-detail.component';

const routes: Routes = [
  {
    path: 'employees-type',
    redirectTo: 'employees-type/manage'
  },
  {
    path: 'employees-type/manage',
    component: ManageEmployeeTypeComponent,
    canActivate: [PermissionAuthGuard],
    data: {
      permissionModules: [PermissionModules.VIEW_EMPLOYEE_TYPE]
    },
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'employees-type/create',
    component: CreateEmployeeTypeComponent,
    canActivate: [PermissionAuthGuard],
    data: {
      permissionModules: [PermissionModules.MANAGE_EMPLOYEE_TYPE]
    },
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'employees-type/create/:typeId',
    component: CreateEmployeeTypeComponent,
    canActivate: [PermissionAuthGuard],
    data: {
      permissionModules: [PermissionModules.MANAGE_EMPLOYEE_TYPE]
    },
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'position-type',
    redirectTo: 'position-type/manage'
  },
  {
    path: 'position-type/manage',
    component: ManagePositionTypeComponent,
    canActivate: [PermissionAuthGuard],
    data: {
      permissionModules: [PermissionModules.VIEW_POSITION_TYPE]
    },
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'position-type/create',
    component: CreatePositionTypeComponent,
    canActivate: [PermissionAuthGuard],
    data: {
      permissionModules: [PermissionModules.MANAGE_POSITION_TYPE]
    },
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'position-type/create/:typeId',
    component: CreatePositionTypeComponent,
    canActivate: [PermissionAuthGuard],
    data: {
      permissionModules: [PermissionModules.MANAGE_POSITION_TYPE]
    },
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'work-exception-type',
    redirectTo: 'work-exception-type/manage'
  },
  {
    path: 'work-exception-type/manage',
    component: ManageWorkExceptionTypeComponent,
    canActivate: [PermissionAuthGuard],
    data: {
      permissionModules: [PermissionModules.VIEW_WORK_EXCEPTION_TYPE]
    },
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'work-exception-type/create',
    component: CreateWorkExceptionTypeComponent,
    canActivate: [PermissionAuthGuard],
    data: {
      permissionModules: [PermissionModules.MANAGE_WORK_EXCEPTION_TYPE]
    },
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'work-exception-type/create/:exceptionId',
    component: CreateWorkExceptionTypeComponent,
    canActivate: [PermissionAuthGuard],
    data: {
      permissionModules: [PermissionModules.MANAGE_WORK_EXCEPTION_TYPE]
    },
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'holidays',
    redirectTo: 'holidays/manage'
  },
  {
    path: 'holidays/manage',
    component: ManageHolidaysComponent,
    canActivate: [PermissionAuthGuard],
    data: {
      permissionModules: [PermissionModules.VIEW_HOLIDAY]
    },
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'holidays/create',
    component: CreateHolidaysComponent,
    canActivate: [PermissionAuthGuard],
    data: {
      permissionModules: [PermissionModules.MANAGE_HOLIDAY]
    },
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'holidays/create/:holidayId',
    component: CreateHolidaysComponent,
    canActivate: [PermissionAuthGuard],
    data: {
      permissionModules: [PermissionModules.MANAGE_HOLIDAY]
    },
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'expense-type',
    redirectTo: 'expense-type/manage'
  },
  {
    path: 'expense-type/manage',
    component: ManageExpenseTypesComponent,
    data: {
      permissionModules: [PermissionModules.VIEW_EXPENSE_TYPE]
    },
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'expense-type/create',
    component: CreateExpenseTypesComponent,
    data: {
      permissionModules: [PermissionModules.MANAGE_EXPENSE_TYPE]
    },
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'expense-type/create/:typeId',
    component: CreateExpenseTypesComponent,
    data: {
      permissionModules: [PermissionModules.MANAGE_EXPENSE_TYPE]
    },
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'monthly-expense-type',
    redirectTo: 'monthly-expense-type/manage'
  },
  {
    path: 'monthly-expense-type/manage',
    component: ManageMonthlyExpenseTypeComponent,
    data: {
      permissionModules: [PermissionModules.VIEW_MONTHLY_EXPENSE_TYPE]
    },
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'monthly-expense-type/create',
    component: CreateMonthlyExpenseTypeComponent,
    data: {
      permissionModules: [PermissionModules.MANAGE_MONTHLY_EXPENSE_TYPE]
    },
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'monthly-expense-type/create/:typeId',
    component: CreateMonthlyExpenseTypeComponent,
    data: {
      permissionModules: [PermissionModules.MANAGE_MONTHLY_EXPENSE_TYPE]
    },
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'tag-category/manage',
    component: ManageTagCategoryComponent,
    data: {
      permissionModules: [PermissionModules.VIEW_MANAGE_TAG_CATEGORY]
    },
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'tag-category/create',
    component: AddUpdateTagCategoryComponent,
    data: {
      permissionModules: [PermissionModules.MANAGE_TAG_CATEGORY]
    },
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'tag-category/:categoryId',
    component: AddUpdateTagCategoryComponent,
    data: {
      permissionModules: [PermissionModules.MANAGE_TAG_CATEGORY]
    },
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'tag-sub-category/manage',
    component: ManageTagSubCategoryComponent,
    data: {
      permissionModules: [PermissionModules.VIEW_MANAGE_TAG_CATEGORY]
    },
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'tag-sub-category/create',
    component: AddUpdateSubCategoryComponent,
    data: {
      permissionModules: [PermissionModules.MANAGE_TAG_CATEGORY]
    },
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'tag-sub-category/:categoryId',
    component: AddUpdateSubCategoryComponent,
    data: {
      permissionModules: [PermissionModules.MANAGE_TAG_CATEGORY]
    },
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'tag/manage',
    component: ManageTagComponent,
    data: {
      permissionModules: [PermissionModules.VIEW_MANAGE_TAG_CATEGORY]
    },
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'tag/create',
    component: AddUpdateTagsComponent,
    data: {
      permissionModules: [PermissionModules.MANAGE_TAG_CATEGORY]
    },
    canActivate: [PageRefreshGuard],
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'tag/:tagId',
    component: AddUpdateTagsComponent,
    data: {
      permissionModules: [PermissionModules.MANAGE_TAG_CATEGORY]
    },
    canActivate: [PageRefreshGuard],
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'extended-Fields/manage',
    component: ManageExtendedFiledComponent,
    data: {
      permissionModules: [PermissionModules.View_Extended_Filed]
    },
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'extended-Field/create',
    component: AddExtendedFiledComponent,
    data: {
      permissionModules: [PermissionModules.MANAGE_Extended_Filed]
    },
    canActivate: [PageRefreshGuard],
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'global-detail/manage',
    component: ManageGlobalDetailsComponent,
    data: {
      permissionModules: [AdminOnlyPermission.MANAGE_GLOBAL_DETAIL]
    },
    canActivate: [PermissionAuthGuard],
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'global-detail/create',
    component: AddGlobalDetailComponent,
    data: {
      permissionModules: [AdminOnlyPermission.MANAGE_GLOBAL_DETAIL]
    },
    canActivate: [PermissionAuthGuard],
    resolve: { authState: CheckAuthResolver }
  },
  {
    path: 'global-detail/create/:id',
    component: AddGlobalDetailComponent,
    data: {
      permissionModules: [AdminOnlyPermission.MANAGE_GLOBAL_DETAIL]
    },
    canActivate: [PermissionAuthGuard],
    resolve: { authState: CheckAuthResolver }
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class AdministationRoutingModule {}

<div class="card card-custom gutter-b" id="create_role_form">
  <app-card-header [cardTitle]="cardTitle" [cardSubTitle]="cardSubTitle" [buttons]="buttons"></app-card-header>
  <div class="d-flex flex-column">
    <div class="mx-auto pt-5 content-center">
      <form [formGroup]="jsonForm" class="form" (ngSubmit)="jsonForm.valid && onSubmit()">
        <kt-auth-notice></kt-auth-notice>
        <label class="form-label mr-3">Enter JSON Data:</label>
        <div>
          <textarea class="form-control custom" rows="20" cols="100" pInputTextarea formControlName="jsonData" name="jsonData"></textarea>
          <p class="form-text text-danger" *ngIf="jsonForm.invalid">Invalid JSON format.</p>
        </div>
      </form>
    </div>
  </div>
</div>

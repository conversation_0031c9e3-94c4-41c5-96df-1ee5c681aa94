<div class="card card-custom gutter-b" id="createEmployee">
  <app-card-header [cardTitle]="cardTitle" [cardSubTitle]="cardSubTitle" [buttons]="buttons"></app-card-header>
  <div class="card-body create-card scrollable-content" *isFetchingData="loading$">
    <form
      class="form"
      autocomplete="off"
      [formGroup]="createEmployeeForm"
      autocomplete="off"
      novalidate="novalidate"
      id="create_client_form"
      (ngSubmit)="createEmployeeForm.valid && onSave()"
    >
      <div id="client-detail">
        <kt-auth-notice></kt-auth-notice>
        <div class="row">
          <div class="col-sm-6 col-12 pr-md-5">
            <div class="form-group">
              <label class="form-label">First Name</label>
              <input type="text" class="form-control custom" placeholder="John" required formControlName="first_name" />
              <app-form-error [validation]="'required'" [form]="createEmployeeForm" [controlName]="'first_name'" [fieldLabel]="'First Name'"></app-form-error>
              <app-form-error [validation]="'minlength'" [length]="2" [form]="createEmployeeForm" [controlName]="'first_name'" [fieldLabel]="'First Name'"></app-form-error>
            </div>
          </div>
          <div class="col-sm-6 col-12 pr-md-5">
            <div class="form-group">
              <label class="form-label">Last Name</label>
              <input type="text" class="form-control custom" placeholder="Doe" required formControlName="last_name" />
              <app-form-error [validation]="'required'" [form]="createEmployeeForm" [controlName]="'last_name'" [fieldLabel]="'Last Name'"></app-form-error>
              <app-form-error [validation]="'minlength'" [length]="2" [form]="createEmployeeForm" [controlName]="'last_name'" [fieldLabel]="'Last Name'"></app-form-error>
            </div>
          </div>
        </div>
        <div class="row border-bottom">
          <div class="col-sm-6 col-12 pr-md-5">
            <div class="form-group">
              <label class="form-label">Email Address</label>
              <input type="text" class="form-control custom" placeholder="<EMAIL>" required formControlName="email" email (keydown)="handleKeyDown($event, false)" />
              <app-form-error [validation]="'required'" [form]="createEmployeeForm" [controlName]="'email'" [fieldLabel]="'Email'"></app-form-error>
              <app-form-error [validation]="'email'" [form]="createEmployeeForm" [controlName]="'email'" [fieldLabel]="'Email'"></app-form-error>
            </div>
          </div>
          <div class="col-sm-6 col-12 pr-md-5">
            <div class="form-group first dropdown">
              <label class="form-label">Skill Set</label>
              <p-multiSelect
                appendTo="body"
                [options]="employeeRoles"
                placeholder="Select"
                formControlName="position_types"
                display="chip"
                (onPanelHide)="setFocus($event)"
                #status
              ></p-multiSelect>
            </div>
          </div>
        </div>
        <div class="row border-bottom">
          <!-- <div class="col-sm-6 col-12 pr-md-5"> -->
          <!-- <div class="form-group first dropdown">
              <label class="form-label">Region</label>
              <p-dropdown appendTo="body" [options]="regions" placeholder="Select" formControlName="region_id" (onChange)="regionSelected()" required #regionsDropDown (keydown)="handleKeyDown($event,true)"></p-dropdown>
              <app-form-error *ngIf="regionFlag" [validation]="'required'" [form]="createEmployeeForm" [controlName]="'region_id'" [fieldLabel]="'Region'"></app-form-error>
            </div> -->
          <!-- now position is all so it will show  region_id=1 -->
          <!-- </div> -->
          <div class="col-sm-6 col-12 pr-md-5 pb-5">
            <div class="form-group first pb-0">
              <label class="form-label">Travel %</label>
            </div>
            <div class="p-inputgroup travel-input">
              <input sflIsNumber type="text" pInputText placeholder="00" formControlName="ext_travel" />
              <span class="p-inputgroup-addon">%</span>
            </div>
            <app-form-error [validation]="'min'" [length]="0" [form]="createEmployeeForm" [controlName]="'ext_travel'" [fieldLabel]="'Travel'"></app-form-error>
            <app-form-error [validation]="'max'" [length]="100" [form]="createEmployeeForm" [controlName]="'ext_travel'" [fieldLabel]="'Travel'"></app-form-error>
          </div>
          <div class="col-sm-6 col-12 pr-md-5">
            <div class="form-group first pb-0">
              <label class="form-label">Utilization Target %</label>
            </div>
            <div class="p-inputgroup travel-input">
              <input sflIsDecimalNumber type="number" pInputText placeholder="00.00" formControlName="utilization_target" (input)="limitDecimalPlaces($event)" />
              <span class="p-inputgroup-addon">%</span>
            </div>
            <app-form-error [validation]="'required'" [form]="createEmployeeForm" [controlName]="'utilization_target'" [fieldLabel]="'Utilization Target'"></app-form-error>
            <app-form-error [validation]="'min'" [length]="0" [form]="createEmployeeForm" [controlName]="'utilization_target'" [fieldLabel]="'Utilization Target'"></app-form-error>
            <app-form-error [validation]="'max'" [length]="100" [form]="createEmployeeForm" [controlName]="'utilization_target'" [fieldLabel]="'Utilization Target'">
            </app-form-error>
          </div>
        </div>
        <div class="row">
          <div class="col-sm-3 col-12 pr-md-5">
            <div class="form-group pb-0">
              <label class="form-label">Start Date</label>
            </div>
            <div class="form-group pt-0 range-calender">
              <p-calendar
                appendTo="body"
                placeholder="Select Date"
                class="form-control custom p-0"
                [showIcon]="true"
                inputId="date"
                formControlName="start_date"
                [disabled]="employeeId"
              >
              </p-calendar>
              <app-form-error [validation]="'required'" [form]="createEmployeeForm" [controlName]="'start_date'" [fieldLabel]="'Start Date'"></app-form-error>
            </div>
          </div>
          <div class="col-sm-3 col-12 pr-md-5">
            <ng-container *ngIf="inactive">
              <div class="form-group first pb-0">
                <label class="form-label">End Date</label>
              </div>
              <div class="form-group pt-0 range-calender">
                <p-calendar appendTo="body" placeholder="Select Date" class="form-control custom p-0" [showIcon]="true" inputId="date" formControlName="end_date"> </p-calendar>
                <!-- <app-form-error [validation]="'required'" [form]="createEmployeeForm" [controlName]="'start_date'" [fieldLabel]="'Start Date'"></app-form-error> -->
              </div>
            </ng-container>
          </div>
          <div class="col-sm-3 col-12 pr-md-5">
            <div class="form-group first dropdown">
              <label class="form-label">Employee Type</label>
              <p-dropdown
                appendTo="body"
                [options]="employeeTypes"
                placeholder="Select"
                formControlName="employee_type_id"
                required
                (onChange)="employeeTypeSelected()"
              ></p-dropdown>
              <app-form-error
                *ngIf="employeeTypeFlag"
                [validation]="'required'"
                [form]="createEmployeeForm"
                [controlName]="'employee_type_id'"
                [fieldLabel]="'Employee Type'"
              ></app-form-error>
            </div>
          </div>
          <div *ngIf="!employeeId" class="col-sm-3 col-12 pr-md-5">
            <div class="form-group first pb-0">
              <label class="form-label">Hourly Cost</label>
            </div>
            <div class="p-inputgroup">
              <span class="p-inputgroup-addon">$</span>
              <input sflIsDecimalNumber [decimals]="2" type="text" pInputText placeholder="0.00" required formControlName="hourly_cost" />
            </div>
            <app-form-error [validation]="'required'" [form]="createEmployeeForm" [controlName]="'hourly_cost'" [fieldLabel]="'Cost'"></app-form-error>
          </div>
          <div class="col-12 mt-4 mb-4" *ngIf="employeeId">
            <hr />
            <app-accrual-list [employeeId]="employeeId"> </app-accrual-list>
          </div>
          <div class="col-12">
            <app-extended-form #extendFrom [projectId]="employeeId" [componentType]="componentType" [extendFieldsObj]="extendFieldsObj"></app-extended-form>
          </div>
        </div>
        <hr />
      </div>
    </form>
    <div>
      <app-audit-history [employeeId]="employeeId" [showAccordian]="showAccordian" [showFinancial]="true"></app-audit-history>
    </div>
    <hr *ngIf="employeeId" />
    <div class="mt-2" *ngIf="employeeId">
      <app-tag-listing-view [_employeeId]="employeeId" [_tags]="tags" [_flag]="'EMPLOYEE_TAG'" (_reloadTags)="getEmployee()"></app-tag-listing-view>
      <hr />
    </div>
  </div>
</div>
<p-dialog header="Warning" [(visible)]="showDialog" [modal]="true" class="confirm-dialog-expense" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
  <h5 class="p-m-0">
    Are you certain that you want to terminate this employee? You will not be able to make any additional edits to the employee record and all impacted projects will be adjusted.
    This action cannot be reversed!
  </h5>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center">
      <button type="button" class="btn-cancel" (click)="closeModal()">No</button>
      <button type="button" class="btn-save" (click)="callSaveEmployeeApi()" [isSubmitting]="isSubmitting">Yes</button>
    </div>
  </ng-template>
</p-dialog>

<div class="card card-custom gutter-b" id="createHolidays">
  <app-card-header [cardTitle]="cardTitle" [cardSubTitle]="cardSubTitle" [buttons]="buttons"></app-card-header>
  <div class="card-body create-card" *isFetchingData="loading$">
    <form class="form" novalidate="novalidate" id="create_exception_form" [formGroup]="createHolidayForm" autocomplete="off" (ngSubmit)="onSave()">
      <div id="client-detail">
        <kt-auth-notice></kt-auth-notice>
        <div class="mx-auto content-center">
          <div class="row">
            <div class="col-12 pr-md-5">
              <div class="form-group first">
                <label class="form-label">Holiday Name</label>
                <input type="text" class="form-control custom" placeholder="e.g. New Year's Day" formControlName="description" />
                <app-form-error [validation]="'required'" [form]="createHolidayForm" [controlName]="'description'" [fieldLabel]="'Holiday Name'"></app-form-error>
              </div>
              <span>
                <div class="form-group pb-0">
                  <label class="form-label">Date</label>
                </div>
                <div class="form-group pt-0 range-calender">
                  <p-calendar appendTo="body" placeholder="Select Date" class="form-control custom" formControlName="date" [showIcon]="true" inputId="date"></p-calendar>
                  <app-form-error [validation]="'required'" [form]="createHolidayForm" [controlName]="'date'" [fieldLabel]="'Date'"></app-form-error>
                </div>
              </span>
            </div>
          </div>
          <div class="row pr-md-4">
            <div class="col-6">
              <div class="form-group first dropdown">
                <label class="form-label">Holiday Type</label>
                <div ngbDropdown class="d-inline-block w-100">
                  <button
                    type="button"
                    class="btn btn-outline-primary dropdown-btn dropdown-height"
                    id="dropdownBasic1"
                    ngbDropdownToggle
                    [ngClass]="{ 'dropdown-text': exception, 'dropdown-placeholder': 'Select' }"
                  >
                    {{ exception ? exception : 'Select' }} <i class="pi pi-chevron-down" aria-hidden="true"></i>
                  </button>
                  <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
                    <button type="button" (click)="setExceptionType(exceptionType)" ngbDropdownItem *ngFor="let exceptionType of exceptionTypes">{{ exceptionType?.label }}</button>
                  </div>
                </div>
                <app-form-error
                  *ngIf="exceptionTypeFlag"
                  [validation]="'required'"
                  [form]="createHolidayForm"
                  [controlName]="'work_exception_type_id'"
                  [fieldLabel]="'Holiday Type'"
                >
                </app-form-error>
              </div>
            </div>
            <div class="col-6">
              <div class="form-group first">
                <label class="form-label">Hours</label>
                <input sflIsNumber type="text" class="form-control custom range-width" placeholder="8" formControlName="hours" />
                <app-form-error [validation]="'max'" [form]="createHolidayForm" [length]="24" [controlName]="'hours'" [fieldLabel]="'It'"></app-form-error>
                <app-form-error [validation]="'min'" [form]="createHolidayForm" [controlName]="'hours'" [fieldLabel]="'It'"></app-form-error>
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>

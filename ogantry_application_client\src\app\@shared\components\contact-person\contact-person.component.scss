.contact-person {
  border-radius: 9px;
  border: 1px solid #cfcfd9;
  padding: 5px 15px;
  display: flex;
  justify-content: space-between;
}

.del-icon {
  display: flex;
  align-items: center;
}

.contact-info {
  line-height: 2;
}

.person-name {
  font-weight: bold;
  font-size: 15px;
  margin-right: 10px;
}

.contact {
  display: flex;
  justify-content: space-between;
}

.flaticon2-phone,
.flaticon2-send {
  color: #7c7c7c;
}
@media (min-width: 1024px) {
  .contact-person-list {
    overflow-x: auto;
    height: 100%;
    max-height: calc(100vh - 56vh);
  }
}
@media (min-width: 1920px) {
  .contact-person-list {
    overflow-x: auto;
    height: 100%;
    max-height: calc(100vh - 38vh);
  }
}

span.primary-contact {
  padding: 0 5px;
  margin: 0 10px;
  background: #eee;
  color: #000;
  font-weight: 500;
}

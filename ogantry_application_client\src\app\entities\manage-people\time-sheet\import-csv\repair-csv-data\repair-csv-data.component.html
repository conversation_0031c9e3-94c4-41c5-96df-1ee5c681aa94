<span class="repair-csv">
  <div class="ml-2 d-flex align-items-center incorrect-toggle-wrapper py-2">
    <p-inputSwitch [(ngModel)]="showincorrectDataToggle" class="d-flex" (click)="toggleIncoorectData()" s></p-inputSwitch>
    <span class="ml-2 toggle-text">Only Show row which have problem</span>
  </div>
  <app-time-sheet-table [tableHeader]="tableHeader" [tableData]="tableData" (correctionData)="openCorrectionModal($event)"></app-time-sheet-table>

  <p-dialog header="Update Data" [(visible)]="showCorrectionModal" class="correction-modal-wrapper" [modal]="true" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
    <div class="d-flex align-items-center justify-content-between p-2">
      <div class="p-2">
        <p class="mb-0 red-text f-13">{{ selectedinCorrectData }}</p>
        <!-- <div class="similar-incorrect-msg"><svg xmlns="http://www.w3.org/2000/svg" width="10" height="11"
                    viewBox="0 0 10 11" fill="none">
                    <mask id="mask0_1002_6768" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="10"
                        height="11">
                        <rect y="0.5" width="10" height="10" fill="#D9D9D9" />
                    </mask>
                    <g mask="url(#mask0_1002_6768)">
                        <path
                            d="M4.5835 7.58203H5.41683V5.08203H4.5835V7.58203ZM5.00016 4.2487C5.11822 4.2487 5.21718 4.20877 5.29704 4.12891C5.3769 4.04905 5.41683 3.95009 5.41683 3.83203C5.41683 3.71398 5.3769 3.61502 5.29704 3.53516C5.21718 3.4553 5.11822 3.41536 5.00016 3.41536C4.88211 3.41536 4.78315 3.4553 4.70329 3.53516C4.62343 3.61502 4.5835 3.71398 4.5835 3.83203C4.5835 3.95009 4.62343 4.04905 4.70329 4.12891C4.78315 4.20877 4.88211 4.2487 5.00016 4.2487ZM5.00016 9.66537C4.42377 9.66537 3.88211 9.55599 3.37516 9.33724C2.86822 9.11849 2.42725 8.82162 2.05225 8.44662C1.67725 8.07162 1.38037 7.63064 1.16162 7.1237C0.942871 6.61675 0.833496 6.07509 0.833496 5.4987C0.833496 4.92231 0.942871 4.38064 1.16162 3.8737C1.38037 3.36675 1.67725 2.92578 2.05225 2.55078C2.42725 2.17578 2.86822 1.87891 3.37516 1.66016C3.88211 1.44141 4.42377 1.33203 5.00016 1.33203C5.57655 1.33203 6.11822 1.44141 6.62516 1.66016C7.13211 1.87891 7.57308 2.17578 7.94808 2.55078C8.32308 2.92578 8.61995 3.36675 8.83871 3.8737C9.05746 4.38064 9.16683 4.92231 9.16683 5.4987C9.16683 6.07509 9.05746 6.61675 8.83871 7.1237C8.61995 7.63064 8.32308 8.07162 7.94808 8.44662C7.57308 8.82162 7.13211 9.11849 6.62516 9.33724C6.11822 9.55599 5.57655 9.66537 5.00016 9.66537Z"
                            fill="#1C1B1F" />
                    </g>
                </svg>
                Update to <span class="similar-incorrect-number">2</span> similar Names
            </div> -->
      </div>
      <div class="ml-3 p-2" *ngIf="!isDateCorrection && !isHoursCorrection">
        <p-dropdown appendTo="body" [options]="dropDownOptions" [(ngModel)]="selectedCorrectData" class="dropdown-wrapper" placeholder="Select a option"></p-dropdown>
      </div>
      <div *ngIf="isDateCorrection && !isHoursCorrection">
        <p-calendar
          appendTo="body"
          placeholder="Select Date"
          class="form-control custom p-0 date-picker--wrapper d-flex align-items-center"
          [showIcon]="true"
          [(ngModel)]="selectedCorrectData"
        >
        </p-calendar>
      </div>
      <div *ngIf="isHoursCorrection && !isDateCorrection">
        <p-calendar [(ngModel)]="selectedCorrectData" [timeOnly]="true" appendTo="body" class="hours-picker"></p-calendar>
      </div>
    </div>

    <div *ngIf="!isDateCorrection && !isHoursCorrection">
      <p-checkbox [(ngModel)]="updateAllButton" [binary]="true" label="Apply to all"></p-checkbox>
    </div>
    <p class="red-text fix-p-aliment pt-2" *ngIf="dropDownOptions.length === 0 && !isDateCorrection && !isHoursCorrection">Verify the data and try to re-import the time sheet</p>
    <ng-template pTemplate="footer">
      <div class="d-flex flex-wrap justify-content-end align-items-center">
        <button *ngIf="!isDateCorrection && !isHoursCorrection" type="button" class="btn-save" (click)="correctData(updateAllButton)" [disabled]="!selectedCorrectData">
          Update
        </button>
        <button *ngIf="!(!isDateCorrection && !isHoursCorrection)" type="button" class="btn-save" (click)="correctData(false)" [disabled]="!selectedCorrectData">Update</button>

        <button type="button" class="btn-cancel" (click)="hideCorrectionModal()">Cancel</button>
      </div>
    </ng-template>
  </p-dialog>
</span>

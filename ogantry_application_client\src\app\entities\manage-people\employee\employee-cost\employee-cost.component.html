<div class="card card-custom gutter-b" id="employeeCost">
  <app-card-header [cardTitle]="cardTitle" [cardSubTitle]="cardSubTitle" [buttons]="buttons"></app-card-header>
  <div class="card-body create-card scrollable-content" *isFetchingData="loading$">
    <form
      class="form"
      autocomplete="off"
      [formGroup]="createEmployeeCostForm"
      autocomplete="off"
      novalidate="novalidate"
      id="create_role_form"
      (ngSubmit)="(createEmployeeCostForm.valid)"
    >
      <div id="client-detail">
        <kt-auth-notice></kt-auth-notice>
        <div class="mx-auto content-center">
          <div class="row mb-10 warning-background mt-2">
            <div class="d-flex col-12">
              <div class="d-flex warning-msg align-items-center">
                Warning : This will impact any project that the employee is currently assigned to and the resulting changes cannot be reversed!
              </div>
            </div>
          </div>
          <div class="row mb-8">
            <div class="col-12">
              <span class="title"> {{ employee?.employee?.first_name + ' ' }}{{ employee?.employee?.last_name }} </span>
            </div>
            <div class="col-12">
              <span class="subTitle">
                {{ this.getSkillSet(employee) }}
              </span>
            </div>
          </div>
          <div class="row">
            <div class="col-sm-3 col-12 pr-md-5">
              <div class="form-group first pb-0">
                <label class="form-label">Hourly Cost</label>
              </div>
              <div class="p-inputgroup">
                <span class="p-inputgroup-addon">$</span>
                <p-inputNumber [minFractionDigits]="2" placeholder="0.00" required formControlName="hourly_cost"></p-inputNumber>
              </div>
              <app-form-error [validation]="'required'" [form]="createEmployeeCostForm" [controlName]="'hourly_cost'" [fieldLabel]="'Cost'"></app-form-error>
            </div>
            <div class="col-sm-3 col-12 pr-md-5">
              <div class="form-group pb-0">
                <label class="form-label">Effective Date</label>
              </div>
              <div class="form-group pt-0 range-calender">
                <p-calendar
                  [maxDate]="maxDate"
                  [minDate]="minDate"
                  appendTo="body"
                  placeholder="Select Date"
                  class="form-control custom p-0"
                  [showIcon]="true"
                  inputId="date"
                  formControlName="active_date"
                >
                </p-calendar>
                <app-form-error [validation]="'required'" [form]="createEmployeeCostForm" [controlName]="'active_date'" [fieldLabel]="'Effective Date'"></app-form-error>
              </div>
            </div>
          </div>
        </div>
      </div>
      <hr />
      <div>
        <app-audit-history [employeeId]="employeeId" [showAccordian]="showAccordian"></app-audit-history>
      </div>
    </form>
  </div>
</div>
<p-dialog header="Warning" [(visible)]="showDialogBox" [modal]="true" class="confirm-dialog-expense" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
  <h5 class="p-m-0">
    Are you certain that you want to update this employee's hourly cost? This will impact any project that the employee is currently assigned to and the resulting changes cannot be
    reversed!
  </h5>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center">
      <button type="button" class="btn-cancel" (click)="closeModal()">No</button>
      <button type="button" class="btn-save" (click)="callupdateEmployeeCostApi()" [isSubmitting]="isSubmitting">Yes</button>
    </div>
  </ng-template>
</p-dialog>

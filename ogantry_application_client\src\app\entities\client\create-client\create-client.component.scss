#createClient {
  .card-body {
    overflow: auto;
  }

  .add-new {
    text-decoration: underline;
    margin-left: 1rem;
  }

  .add-new:hover {
    text-decoration: underline !important;
    color: lighten(#4b3f72, 10%);
    cursor: pointer;
  }

  .flaticon2-comment-send {
    background-color: #eeeef6;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    bottom: 0px;
    right: 13px;
    border-bottom-right-radius: 9px;
    border-top-left-radius: 9px;
    &:hover {
      background-color: #e7e7ee;
      cursor: pointer;
    }
  }

  textarea {
    resize: none;
  }

  @media (min-width: 768px) {
    .flaticon2-comment-send {
      right: 26px;
    }
  }

  .status-toggle {
    margin-left: 3rem;
    font-size: 14px;
  }

  .detail-sidebar {
    width: 500px;
    max-width: 100%;
  }

  ::ng-deep .mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-bar {
    background-color: #3acad1;
  }

  ::ng-deep .mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-thumb {
    background-color: #109aa1;
  }

  ::ng-deep .mat-slide-toggle .mat-slide-toggle-bar {
    background-color: rgba(220, 47, 68, 0.54);
  }

  ::ng-deep .mat-slide-toggle .mat-slide-toggle-thumb {
    background-color: #dc2f44;
  }

  small {
    font-size: 0.9rem;
  }

  ::ng-deep .confirm-dialog .p-dialog {
    width: 30vw;
  }
}

.createClientWrapper {
  ::ng-deep .mat-drawer.mat-drawer-end {
    transform: none !important;
  }
}
::ng-deep .p-datatable-scrollable-both .p-datatable-thead > tr > th,
.p-datatable-scrollable-both .p-datatable-tbody > tr > td,
.p-datatable-scrollable-both .p-datatable-tfoot > tr > td,
.p-datatable-scrollable-horizontal .p-datatable-thead > tr > th .p-datatable-scrollable-horizontal .p-datatable-tbody > tr > td,
.p-datatable-scrollable-horizontal .p-datatable-tfoot > tr > td,
.p-datatable-scrollable-both .p-datatable-thead > tr > th {
  flex: 1 1 0 !important;
}

::ng-deep .p-datatable-scrollable-both .p-datatable-tbody > tr > td,
.p-datatable-scrollable-both .p-datatable-tfoot > tr > td,
.p-datatable-scrollable-horizontal .p-datatable-thead > tr > th .p-datatable-scrollable-horizontal .p-datatable-tbody > tr > td,
.p-datatable-scrollable-horizontal .p-datatable-tfoot > tr > td {
  flex: 1 1 0 !important;
}
